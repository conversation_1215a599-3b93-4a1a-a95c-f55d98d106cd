'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import appointmentService, { AppointmentResponse } from '@/lib/api/services/fetchAppointment';
import { DrawerFooter } from './ui/drawer';
import { LoginForm } from '@/app/(auth)/components/login-form';

interface Calendar20Props {
  propertyId: string;
  propertyName: string;
  onSuccess?: (data: AppointmentResponse['data']) => void;
  onClose?: () => void;
  isDrawer?: boolean;
}

export default function Calendar20({
  propertyId,
  onSuccess,
  onClose,
  isDrawer = false,
}: Calendar20Props) {
  const [date, setDate] = React.useState<Date | undefined>();
  const [selectedTime, setSelectedTime] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [appointmentData, setAppointmentData] = React.useState<AppointmentResponse['data'] | null>(
    null
  );
  const [showLoginDialog, setShowLoginDialog] = React.useState(false);

  const timeSlots = Array.from({ length: 11 }, (_, i) => {
    const totalMinutes = i * 60;
    const hour = Math.floor(totalMinutes / 60) + 9;
    const minute = totalMinutes % 60;
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  });

  const handleSubmit = async () => {
    if (!date || !selectedTime) {
      toast.error('Vui lòng chọn ngày và giờ hẹn');
      return;
    }

    try {
      setIsLoading(true);
      // Combine date and time
      const [hours, minutes] = selectedTime.split(':').map(Number);
      const appointmentDate = new Date(date);
      appointmentDate.setHours(hours, minutes, 0, 0);

      const response = await appointmentService.createAppointment({
        propertyId,
        date: appointmentDate.toISOString(),
      });
      console.log({
        propertyId,
        date: appointmentDate.toISOString(),
      });
      if (response.status) {
        setAppointmentData(response.data);
        onSuccess?.(response.data);
        toast.success('Đặt lịch hẹn thành công');
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      if (error?.response?.status === 401 || error?.status === 401) {
        setShowLoginDialog(true);
        toast.error('Vui lòng đăng nhập để đặt lịch hẹn', {
          position: isDrawer ? 'top-center' : 'bottom-right',
        });
      } else {
        toast.error('Không thể đặt lịch hẹn. Vui lòng thử lại sau.', {
          position: isDrawer ? 'top-center' : 'bottom-right',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setAppointmentData(null);
    setDate(undefined);
    setSelectedTime(null);
    onClose?.();
  };

  if (appointmentData) {
    return (
      <Card>
        <CardContent className="space-y-4 pt-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium">Thời gian:</span>
              <span>{format(new Date(appointmentData.date), 'PPP p', { locale: vi })}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium">Môi giới:</span>
              <span>{appointmentData.saler.fullName}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium">Email:</span>
              <span>{appointmentData.saler.email}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium">Số điện thoại:</span>
              <span>{appointmentData.saler.phoneNumber}</span>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            <p>Mã lịch hẹn: {appointmentData.id}</p>
            <p>Trạng thái: {appointmentData.status}</p>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleClose} className="w-full">
            Đóng
          </Button>
        </CardFooter>
      </Card>
    );
  }

  const calendarContent = (
    <Card className="gap-0 p-0">
      <CardContent className="relative p-0 md:pr-48">
        <div className="p-2 md:p-6 flex items-center justify-center">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            defaultMonth={date}
            disabled={(date: Date) => date < new Date()}
            showOutsideDays={false}
            className="bg-transparent p-0 [--cell-size:2.5rem] md:[--cell-size:3.1rem] md:p-0"
            formatters={{
              formatWeekdayName: (date: Date) => {
                return date.toLocaleString('vi-VN', { weekday: 'short' });
              },
              formatMonthCaption: (date: Date) => {
                return date.toLocaleString('vi-VN', {
                  month: 'long',
                  year: 'numeric',
                });
              },
            }}
            buttonVariant="ghost"
          />
        </div>
        <div className="no-scrollbar inset-y-0 right-0 flex max-h-72 w-full scroll-pb-6 flex-col gap-4 overflow-y-auto border-t p-6 md:absolute md:max-h-none md:w-48 md:border-l md:border-t-0 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
          <div className="grid gap-2 max-md:grid-cols-4">
            {timeSlots.map(time => (
              <Button
                key={time}
                variant={selectedTime === time ? 'default' : 'outline'}
                onClick={() => setSelectedTime(time)}
                className="w-full md:text-sm text-xs shadow-none"
              >
                {time}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
      {!isDrawer && (
        <CardFooter className="flex flex-col gap-4 border-t !py-5 px-6 md:flex-row">
          <div className="text-sm">
            {date && selectedTime ? (
              <>
                Lịch hẹn của bạn được đặt vào{' '}
                <span className="font-medium">{format(date, 'EEEE, d MMMM', { locale: vi })}</span>{' '}
                lúc <span className="font-medium">{selectedTime}</span>.
              </>
            ) : (
              <>Vui lòng chọn ngày và giờ cho lịch hẹn của bạn.</>
            )}
          </div>
          <div className="flex gap-2 w-full md:w-auto md:ml-auto">
            <Button variant="outline" disabled={isLoading} onClick={handleClose}>
              Hủy
            </Button>
            <Button onClick={handleSubmit} disabled={!date || !selectedTime || isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang xử lý
                </>
              ) : (
                'Xác nhận'
              )}
            </Button>
          </div>
        </CardFooter>
      )}
    </Card>
  );

  if (isDrawer) {
    return (
      <>
        {calendarContent}
        <DrawerFooter className="flex flex-col gap-4 !py-5 px-6 md:flex-row">
          <div className="text-sm">
            {date && selectedTime ? (
              <>
                Lịch hẹn của bạn được đặt vào{' '}
                <span className="font-medium">{format(date, 'EEEE, d MMMM', { locale: vi })}</span>{' '}
                lúc <span className="font-medium">{selectedTime}</span>.
              </>
            ) : (
              <>Vui lòng chọn ngày và giờ cho lịch hẹn của bạn.</>
            )}
          </div>
          <div className="flex gap-2 w-full md:w-auto md:ml-auto">
            <Button
              variant="outline"
              disabled={isLoading}
              onClick={handleClose}
              size="sm"
              className="text-xs"
            >
              Hủy
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!date || !selectedTime || isLoading}
              size="sm"
              className="text-xs"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang xử lý
                </>
              ) : (
                'Xác nhận'
              )}
            </Button>
          </div>
        </DrawerFooter>
      </>
    );
  }

  return (
    <>
      {calendarContent}
      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent className="sm:max-w-[625px]">
          {/* <DialogHeader>
            <DialogTitle>Đăng nhập</DialogTitle>
            <DialogDescription>
              Vui lòng đăng nhập để đặt lịch hẹn xem bất động sản
            </DialogDescription>
          </DialogHeader> */}
          <LoginForm />
        </DialogContent>
      </Dialog>
    </>
  );
}
