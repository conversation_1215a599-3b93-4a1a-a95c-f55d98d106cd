import { But<PERSON> } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Mail, Phone } from 'lucide-react';

interface UserInfo {
  avatar?: string;
  name?: string;
  role?: string;
  email?: string;
  phone?: string;
}

interface UserInfoTooltipProps {
  userInfo: UserInfo;
  children: React.ReactNode;
}

export const UserInfoTooltip = ({ userInfo, children }: UserInfoTooltipProps) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side="top" className="w-80 p-0">
        <div className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-white font-semibold text-lg">
                {userInfo.avatar}
              </div>
              <div>
                <h3 className="font-semibold text-lg">{userInfo.name}</h3>
                <p className="text-blue-100 text-sm">{userInfo.role}</p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 space-y-3">
            {/* Email */}
            <div className="flex items-center gap-3">
              <Mail className="w-4 h-4 text-gray-500 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-xs text-gray-500 mb-1">Email</p>
                <p className="text-sm font-medium text-blue-600 truncate">{userInfo.email}</p>
              </div>
            </div>

            {/* Phone */}
            <div className="flex items-center gap-3">
              <Phone className="w-4 h-4 text-gray-500 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-xs text-gray-500 mb-1">Phone</p>
                <p className="text-sm font-medium">{userInfo.phone}</p>
              </div>
            </div>

            {/* Department */}
            {/* <div className="flex items-center gap-3">
              <Building2 className="w-4 h-4 text-gray-500 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-xs text-gray-500 mb-1">Department</p>
                <p className="text-sm font-medium">{userInfo.department}</p>
              </div>
            </div> */}

            {/* Location */}
            {/* <div className="flex items-center gap-3">
              <MapPin className="w-4 h-4 text-gray-500 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-xs text-gray-500 mb-1">Location</p>
                <p className="text-sm font-medium">{userInfo.location}</p>
              </div>
            </div> */}

            {/* Last Active */}
            {/* <div className="flex items-center gap-3">
              <Calendar className="w-4 h-4 text-gray-500 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-xs text-gray-500 mb-1">Last Active</p>
                <p className="text-sm font-medium">{userInfo.lastActive}</p>
              </div>
            </div> */}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 border-t">
            <div className="flex gap-2">
              <Button size="sm" variant="outline" className="flex-1">
                <Mail className="w-3 h-3 mr-1" />
                Email
              </Button>
              <Button size="sm" variant="outline" className="flex-1">
                <Phone className="w-3 h-3 mr-1" />
                Call
              </Button>
            </div>
          </div>
        </div>
      </TooltipContent>
    </Tooltip>
  );
};
