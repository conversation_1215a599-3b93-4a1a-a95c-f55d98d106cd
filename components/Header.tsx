'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile } from '@/hooks/useUsers';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  UserCircleIcon,
  LogOutIcon,
  Menu,
  LayoutDashboardIcon,
  HomeIcon,
  UsersIcon,
  TrendingUpIcon,
  HeartIcon,
  CalendarIcon,
  ClockIcon,
  PlusIcon,
  BuildingIcon,
  Building2,
  LandPlot,
  Building,
  Store,
  StoreIcon,
  HousePlus,
  BedDouble,
  Warehouse,
  SearchIcon,
  BookmarkIcon,
  EyeIcon,
  SettingsIcon,
  BellIcon,
  Send,
  PiggyBankIcon,
  UserSearchIcon,
  PhoneCallIcon,
  ScaleIcon,
  ClipboardListIcon,
  TruckIcon,
  Armchair,
  ChevronRightIcon,
  CircleIcon,
} from 'lucide-react';
import { NavigationBar } from './NavigationBar';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from './ui/scroll-area';

// Navigation menu data structure
type NavigationItem = {
  name: string;
  icon: React.ElementType;
  href?: string;
  type: 'page' | 'category';
  children?: NavigationItem[];
};

const navigationMenu: NavigationItem[] = [
  {
    name: 'Bất động sản',
    icon: Building,
    type: 'category',
    children: [
      {
        name: 'Chung cư bán',
        icon: Building2,
        href: '/properties?propertyType=apartment&transactionType=ForSale',
        type: 'page',
      },
      {
        name: 'Nhà riêng bán',
        icon: HomeIcon,
        href: '/properties?propertyType=shop_house&transactionType=ForSale',
        type: 'page',
      },
      {
        name: 'Đất bán',
        icon: LandPlot,
        href: '/properties?propertyType=land_plot&transactionType=ForSale',
        type: 'page',
      },
      {
        name: 'Văn phòng bán',
        icon: Building,
        href: '/properties?propertyType=apartment&transactionType=ForSale',
        type: 'page',
      },
      {
        name: 'Chung cư thuê',
        icon: StoreIcon,
        href: '/properties?propertyType=apartment&transactionType=ForRent',
        type: 'page',
      },
      {
        name: 'Nhà riêng thuê',
        icon: HousePlus,
        href: '/properties?propertyType=shop_house&transactionType=ForRent',
        type: 'page',
      },
      {
        name: 'Văn phòng thuê',
        icon: Warehouse,
        href: '/properties?propertyType=apartment&transactionType=ForRent',
        type: 'page',
      },
      {
        name: 'Phòng trọ thuê',
        icon: BedDouble,
        href: '/properties?propertyType=shop_house&transactionType=ForRent',
        type: 'page',
      },
    ],
  },
  {
    name: 'Dịch vụ',
    icon: Store,
    type: 'category',
    children: [
      {
        name: 'Vay mua nhà',
        icon: PiggyBankIcon,
        href: '/services/loan',
        type: 'page',
      },
      {
        name: 'Tìm người bán',
        icon: UserSearchIcon,
        href: '/services/find-seller',
        type: 'page',
      },
      {
        name: 'Tư vấn nhà ở',
        icon: PhoneCallIcon,
        href: '/services/consultation',
        type: 'page',
      },
      {
        name: 'Tìm bạn ở ghép',
        icon: UsersIcon,
        href: '/services/find-roommate',
        type: 'page',
      },
      {
        name: 'Định giá nhà',
        icon: ScaleIcon,
        href: '/services/property-valuation',
        type: 'page',
      },
      {
        name: 'Quản lý nhà',
        icon: ClipboardListIcon,
        href: '/services/property-management',
        type: 'page',
      },
      {
        name: 'Dịch vụ chuyển nhà',
        icon: TruckIcon,
        href: '/services/moving',
        type: 'page',
      },
      {
        name: 'Thiết kế nội thất',
        icon: Armchair,
        href: '/services/interior-design',
        type: 'page',
      },
    ],
  },
  {
    name: 'Bất động sản của tôi',
    icon: HomeIcon,
    type: 'category',
    children: [
      {
        name: 'Nhà của bạn',
        icon: HomeIcon,
        href: '/myrevo',
        type: 'page',
      },
      {
        name: 'Nhà đã lưu',
        icon: BookmarkIcon,
        href: '/myrevo',
        type: 'page',
      },
      {
        name: 'Đã xem gần đây',
        icon: EyeIcon,
        href: '/myrevo',
        type: 'page',
      },
      {
        name: 'Quản lý lịch hẹn',
        icon: CalendarIcon,
        href: '/myrevo',
        type: 'page',
      },
      {
        name: 'Tìm kiếm đã lưu',
        icon: SearchIcon,
        href: '/myrevo',
        type: 'page',
      },
      {
        name: 'Tin nhắn của bạn',
        icon: Send,
        href: '/myrevo',
        type: 'page',
      },
      {
        name: 'Thông báo',
        icon: BellIcon,
        href: '/myrevo',
        type: 'page',
      },
      {
        name: 'Cài đặt tài khoản',
        icon: SettingsIcon,
        href: '/myrevo',
        type: 'page',
      },
    ],
  },
];

// Navigation Menu Component
const NavigationMenu = ({ item, level }: { level: number; item: NavigationItem }) => {
  if (item.type === 'page') {
    return (
      <Link
        href={item.href || '#'}
        className="focus-visible:ring-ring/50 flex items-center gap-2 rounded-md p-2 outline-none focus-visible:ring-[3px] hover:bg-accent transition-colors"
        style={{ paddingLeft: `${level === 0 ? 0.5 : 2}rem` }}
      >
        <item.icon className="size-4 shrink-0" />
        <span className="text-sm">{item.name}</span>
      </Link>
    );
  }

  return (
    <Collapsible
      defaultOpen={false}
      className="flex flex-col gap-1.5"
      style={{ paddingLeft: `${level === 0 ? 0 : 1.5}rem` }}
    >
      <CollapsibleTrigger className="focus-visible:ring-ring/50 flex items-center gap-2 rounded-md p-1 outline-none focus-visible:ring-[3px]">
        {level === 0 ? (
          <item.icon className="size-4 shrink-0" />
        ) : (
          <CircleIcon className="size-4 shrink-0" />
        )}
        <span className="flex-1 text-start text-sm">{item.name}</span>
        <ChevronRightIcon className='size-4 shrink-0 transition-transform [[data-state="open"]>&]:rotate-90' />
      </CollapsibleTrigger>
      <CollapsibleContent className="data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down flex flex-col gap-1.5 overflow-hidden transition-all duration-300">
        {item.children?.map(child => (
          <NavigationMenu key={child.name} item={child} level={level + 1} />
        ))}
      </CollapsibleContent>
    </Collapsible>
  );
};

export default function Header() {
  const { isAuthenticated, logout } = useAuth();
  const { data: profileData, error: profileError } = useUserProfile();
  console.log(profileError);
  // Get user data from profile response, with fallback for unauthenticated users
  const user =
    profileData?.profile && isAuthenticated
      ? {
          name: profileData.profile.fullName,
          email: profileData.profile.email,
          avatar: profileData.profile.avatar || '',
        }
      : {
          name: '',
          email: '',
          avatar: '',
        };

  // Create initials from name for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const initials = getInitials(user.name);

  return (
    <header className="w-full bg-background text-foreground sticky top-0 z-50 border-b border-zinc-300 h-14 flex-shrink-0">
      <div className="container mx-auto px-4 h-full">
        <div className="flex h-full items-center justify-between">
          {/* Logo and Mobile Menu */}
          <div className="flex items-center gap-4">
            <Sheet>
              <SheetTrigger asChild className="md:hidden">
                <Button variant="ghost" size="icon" className="h-9 w-9">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] sm:w-[400px]">
                <SheetHeader>
                  <SheetTitle>Menu</SheetTitle>
                </SheetHeader>
                <ScrollArea className="h-full">
                  <div className="flex flex-col gap-2.5 p-4 pt-0 pb-10">
                    {/* Quick Actions for Authenticated Users */}
                    {/* {isAuthenticated && (
                    <>
                      <div className="border-b pb-4 mb-4">
                        <h3 className="text-sm font-semibold text-muted-foreground mb-3">Hành động nhanh</h3>
                        <div className="space-y-2">
                          <Link
                            href="/saler/property/action"
                            className="flex items-center gap-2 p-2 rounded-md hover:bg-accent transition-colors"
                          >
                            <PlusIcon className="h-4 w-4" />
                            <span className="text-sm">Đăng tin bất động sản</span>
                          </Link>
                          <Link
                            href="/saler/property"
                            className="flex items-center gap-2 p-2 rounded-md hover:bg-accent transition-colors"
                          >
                            <BuildingIcon className="h-4 w-4" />
                            <span className="text-sm">Quản lý bất động sản</span>
                          </Link>
                        </div>
                      </div>
                    </>
                  )} */}

                    {/* Main Navigation Menu */}
                    {navigationMenu.map(item => (
                      <NavigationMenu key={item.name} item={item} level={0} />
                    ))}
                  </div>
                </ScrollArea>
              </SheetContent>
            </Sheet>

            <Link href="/" className="flex items-center">
              <Image
                src="/logo_revoland_red.png"
                alt="Revoland icon"
                width={40}
                height={40}
                priority
                className="rounded-md"
              />
              <span className="lg:block hidden text-2xl font-medium text-red-500">Revoland</span>
              {/* <div className="lg:block hidden">
                <Image
                  src="/revoland_text.png"
                  alt="Revoland text"
                  width={120}
                  height={120}
                  priority
                  className="rounded-md"
                />
              </div> */}
            </Link>
          </div>

          {/* Desktop Navigation - Centered absolutely */}
          <div className="hidden md:block absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <NavigationBar />
          </div>

          {/* Auth Buttons */}
          <div className="flex items-center gap-2">
            {isAuthenticated ? (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative size-10 rounded-full">
                      <Avatar className="size-10 ring-zinc-300 ring-2">
                        <AvatarImage src={user.avatar} alt={user.name} className="object-cover" />
                        <AvatarFallback className="bg-red-500/10 text-red-500">
                          {initials}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{user.name}</p>
                        <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    {/* Quick Actions Section */}
                    <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                      Tin đăng
                    </DropdownMenuLabel>
                    <DropdownMenuGroup>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/property/action" className="cursor-pointer">
                          <PlusIcon className="mr-2 h-4 w-4" />
                          Đăng tin bất động sản
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/property" className="cursor-pointer">
                          <BuildingIcon className="mr-2 h-4 w-4" />
                          Quản lý bất động sản
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>

                    <DropdownMenuSeparator />

                    {/* Dashboard Section */}
                    <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                      Quản lý
                    </DropdownMenuLabel>
                    <DropdownMenuGroup>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/dashboard" className="cursor-pointer">
                          <LayoutDashboardIcon className="mr-2 h-4 w-4" />
                          Dashboard
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/lead" className="cursor-pointer">
                          <UsersIcon className="mr-2 h-4 w-4" />
                          Quản lý khách hàng
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/sales" className="cursor-pointer">
                          <TrendingUpIcon className="mr-2 h-4 w-4" />
                          Quản lý giao dịch
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>

                    <DropdownMenuSeparator />

                    {/* User Account Section */}
                    <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                      Tài khoản
                    </DropdownMenuLabel>
                    <DropdownMenuGroup>
                      <DropdownMenuItem asChild>
                        <Link href="/myrevo" className="cursor-pointer">
                          <UserCircleIcon className="mr-2 h-4 w-4" />
                          Thông tin cá nhân
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/myrevo?tab=saved" className="cursor-pointer">
                          <HeartIcon className="mr-2 h-4 w-4" />
                          Bất động sản đã lưu
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/myrevo?tab=appointments" className="cursor-pointer">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          Lịch hẹn xem nhà
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/myrevo?tab=recent" className="cursor-pointer">
                          <ClockIcon className="mr-2 h-4 w-4" />
                          Đã xem gần đây
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>

                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => logout()} className="cursor-pointer">
                      <LogOutIcon className="mr-2 h-4 w-4" />
                      Đăng xuất
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <div className="flex items-center gap-2">
                {/* <Button asChild variant="ghost" size="sm" className="hidden sm:inline-flex">
                  <Link href="/register">Đăng ký</Link>
                </Button> */}
                <Button asChild variant="ghost" size="sm" className="">
                  <Link href="/login">Đăng nhập</Link>
                </Button>
                <Button asChild variant="default" size="sm" className="bg-red-500 hover:bg-red-600">
                  <Link href="/login">Đăng tin</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
