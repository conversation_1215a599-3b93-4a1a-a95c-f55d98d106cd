'use client';

import * as React from 'react';
import {
  BarChartIcon,
  CameraIcon,
  FileCodeIcon,
  FileTextIcon,
  FolderIcon,
  HandCoins,
  House,
  LayoutDashboardIcon,
  ListIcon,
  ListTodo,
  Settings2,
  Users,
  UsersIcon,
  Building2,
  FileEdit,
  ClipboardList,
  TrendingUp,
  Target,
  Calendar,
  FileSignature,
  FileType,
  Clock,
  CheckCircle,
  Contact,
  UserPlus,
  PieChart,
  DollarSign,
  Activity,
  ShoppingCart,
  Home,
  Hammer,
  CheckSquare,
  PlusSquare,
  UsersRound,
  ClipboardCheck,
  User,
  Sliders,
  Bell,
  Shield,
  Files,
} from 'lucide-react';
import { NavMain } from '@/components/common/navMain';
import { NavUser } from '@/components/common/navUser';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import Image from 'next/image';
import Link from 'next/link';
import { NavSecondary } from './navSecondary';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const navigationData = {
    navMain: [
      {
        title: 'Tổng quan',
        url: '/saler/dashboard',
        icon: LayoutDashboardIcon,
      },
      {
        title: 'Bất động sản',
        url: '/saler/property',
        icon: House,
      },
      {
        title: 'Deal của tôi',
        url: '/saler/sales',
        icon: HandCoins,
      },
      {
        title: 'Khách hàng tiềm năng',
        url: '/saler/lead',
        icon: Users,
      },
      {
        title: 'Công việc',
        url: '/saler/tasks',
        icon: ListTodo,
      },
    ],
    navClouds: [
      {
        title: 'Quản lý',
        icon: CameraIcon,
        isActive: true,
        url: '#',
        items: [
          {
            title: 'Hoạt động',
            url: '#',
          },
          {
            title: 'Đã lưu',
            url: '#',
          },
        ],
      },
      {
        title: 'Hợp đồng',
        icon: FileTextIcon,
        url: '#',
        items: [
          {
            title: 'Hoạt động',
            url: '#',
          },
          {
            title: 'Đã lưu',
            url: '#',
          },
        ],
      },
      {
        title: 'Prompts',
        icon: FileCodeIcon,
        url: '#',
        items: [
          {
            title: 'Active Proposals',
            url: '#',
          },
          {
            title: 'Archived',
            url: '#',
          },
        ],
      },
    ],
    navSecondary: [
      {
        title: 'Bất động sản',
        url: '/saler/property',
        icon: Building2,
        isActive: true,
        items: [
          {
            title: 'Danh sách',
            url: '/saler/property/manage',
            icon: ListIcon,
          },
          {
            title: 'Bản nháp',
            url: '/saler/property/draft',
            icon: FileEdit,
          },
          {
            title: 'Đã bán/Cho thuê',
            url: '/saler/property/sold',
            icon: TrendingUp,
          },
          {
            title: 'Lịch sử',
            url: '/saler/property/history',
            icon: Calendar,
          },
        ],
      },
      {
        title: 'Hợp đồng',
        url: '#',
        icon: FileTextIcon,
        items: [
          {
            title: 'Hợp đồng cho thuê',
            url: '/saler/contract/rental',
            icon: FileSignature,
          },
          {
            title: 'Hợp đồng mua bán',
            url: '/saler/contract/sale',
            icon: FileType,
          },
          {
            title: 'Đang chờ ký',
            url: '/saler/contract/pending',
            icon: Clock,
          },
          {
            title: 'Đã hoàn thành',
            url: '/saler/contract/completed',
            icon: CheckCircle,
          },
        ],
      },
      {
        title: 'Chủ nhà',
        url: '#',
        icon: Users,
        items: [
          {
            title: 'Danh sách chủ nhà',
            url: '/saler/owner/list',
            icon: Contact,
          },
          {
            title: 'Thêm chủ nhà',
            url: '/saler/owner/add',
            icon: UserPlus,
          },
          {
            title: 'Thống kê',
            url: '/saler/owner/stats',
            icon: BarChartIcon,
          },
        ],
      },
      {
        title: 'Khách hàng',
        url: '#',
        icon: UsersRound,
        items: [
          {
            title: 'Danh sách khách hàng',
            url: '/saler/customers/list',
            icon: Users,
          },
          {
            title: 'Thêm khách hàng',
            url: '/saler/customers/add',
            icon: UserPlus,
          },
          {
            title: 'Khách hàng tiềm năng',
            url: '/saler/customers/prospects',
            icon: Target,
          },
          {
            title: 'Lịch sử tương tác',
            url: '/saler/customers/history',
            icon: Clock,
          },
          {
            title: 'Thống kê khách hàng',
            url: '/saler/customers/stats',
            icon: PieChart,
          },
        ],
      },
      {
        title: 'Thống kê & Báo cáo',
        url: '#',
        icon: BarChartIcon,
        items: [
          {
            title: 'Tổng quan',
            url: '/saler/stats/overview',
            icon: PieChart,
          },
          {
            title: 'Doanh thu',
            url: '/saler/stats/revenue',
            icon: DollarSign,
          },
          {
            title: 'Hiệu suất',
            url: '/saler/stats/performance',
            icon: Activity,
          },
          {
            title: 'Báo cáo',
            url: '/saler/stats/reports',
            icon: FileTextIcon,
          },
        ],
      },
      {
        title: 'Quy trình',
        url: '#',
        icon: ClipboardList,
        items: [
          {
            title: 'Quy trình bán',
            url: '/saler/process/sale',
            icon: ShoppingCart,
          },
          {
            title: 'Quy trình cho thuê',
            url: '/saler/process/rental',
            icon: Home,
          },
          {
            title: 'Mẫu tài liệu',
            url: '/saler/process/templates',
            icon: Files,
          },
        ],
      },
      {
        title: 'Dự án',
        url: '#',
        icon: FolderIcon,
        items: [
          {
            title: 'Dự án đang thực hiện',
            url: '/saler/projects/active',
            icon: Hammer,
          },
          {
            title: 'Dự án đã hoàn thành',
            url: '/saler/projects/completed',
            icon: CheckSquare,
          },
          {
            title: 'Tạo dự án mới',
            url: '/saler/projects/create',
            icon: PlusSquare,
          },
        ],
      },
      {
        title: 'Nhóm',
        url: '#',
        icon: UsersIcon,
        items: [
          {
            title: 'Thành viên nhóm',
            url: '/saler/team/members',
            icon: UsersRound,
          },
          {
            title: 'Phân công công việc',
            url: '/saler/team/assignments',
            icon: ClipboardCheck,
          },
          {
            title: 'Hiệu suất nhóm',
            url: '/saler/team/performance',
            icon: TrendingUp,
          },
        ],
      },
      {
        title: 'Cài đặt',
        url: '#',
        icon: Settings2,
        items: [
          {
            title: 'Hồ sơ cá nhân',
            url: '/saler/profile',
            icon: User,
          },
          {
            title: 'Cài đặt chung',
            url: '/saler/settings/general',
            icon: Sliders,
          },
          {
            title: 'Thông báo',
            url: '/saler/settings/notifications',
            icon: Bell,
          },
          {
            title: 'Bảo mật',
            url: '/saler/settings/security',
            icon: Shield,
          },
        ],
      },
    ],
  };

  return (
    <Sidebar collapsible="icon" {...props} className="font-medium">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <Link href="/">
                <Image
                  src="/logo_revoland_red.png"
                  alt="Revoland icon"
                  width={20}
                  height={20}
                  priority
                  className="rounded-md size-7"
                />
                <span className="text-base font-semibold text-red-500">RevoLand</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navigationData.navMain} />
        <SidebarSeparator />
        {/* <NavDocuments items={navigationData.documents} /> */}
        <NavSecondary items={navigationData.navSecondary} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
