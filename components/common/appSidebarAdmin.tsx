'use client';

import * as React from 'react';
import {
  ArrowUpCircleIcon,
  BadgeCent,
  CameraIcon,
  FileCodeIcon,
  FileTextIcon,
  HomeIcon,
  LayoutDashboardIcon,
  UserCog,
} from 'lucide-react';
import { NavMain } from '@/components/common/navMain';
import { NavUser } from '@/components/common/navUser';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

export function AppSidebarAdmin({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const navigationData = {
    navMain: [
      {
        title: 'Dashboard',
        url: '/admin/dashboard',
        icon: LayoutDashboardIcon,
      },
      {
        title: 'Bất động sản',
        url: '/admin/property',
        icon: HomeIcon,
      },
      {
        title: '<PERSON>h<PERSON>ch hàng tiềm năng',
        url: '/admin/lead',
        icon: UserCog,
      },
      {
        title: 'Sales',
        url: '/admin/sales',
        icon: BadgeCent,
      },
    ],
    navClouds: [
      {
        title: 'Capture',
        icon: CameraIcon,
        isActive: true,
        url: '#',
        items: [
          {
            title: 'Active Proposals',
            url: '#',
          },
          {
            title: 'Archived',
            url: '#',
          },
        ],
      },
      {
        title: 'Proposal',
        icon: FileTextIcon,
        url: '#',
        items: [
          {
            title: 'Active Proposals',
            url: '#',
          },
          {
            title: 'Archived',
            url: '#',
          },
        ],
      },
      {
        title: 'Prompts',
        icon: FileCodeIcon,
        url: '#',
        items: [
          {
            title: 'Active Proposals',
            url: '#',
          },
          {
            title: 'Archived',
            url: '#',
          },
        ],
      },
    ],
    // navSecondary: [
    //   {
    //     title: t('appSidebar.settings'),
    //     url: '#',
    //     icon: SettingsIcon,
    //   },
    //   {
    //     title: t('appSidebar.getHelp'),
    //     url: '#',
    //     icon: HelpCircleIcon,
    //   },
    //   {
    //     title: t('appSidebar.search'),
    //     url: '#',
    //     icon: SearchIcon,
    //   },
    // ],
    // documents: [
    //   {
    //     name: 'Data Library',
    //     url: '#',
    //     icon: DatabaseIcon,
    //   },
    //   {
    //     name: 'Reports',
    //     url: '#',
    //     icon: ClipboardListIcon,
    //   },
    //   {
    //     name: 'Word Assistant',
    //     url: '#',
    //     icon: FileIcon,
    //   },
    // ],
  };

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <a href="/admin/dashboard">
                <ArrowUpCircleIcon className="h-5 w-5" />
                <span className="text-base font-semibold">RevoLand Inc.</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navigationData.navMain} />
        {/* <NavDocuments items={navigationData.documents} /> */}
        {/* <NavSecondary items={navigationData.navSecondary} className="mt-auto" /> */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
