import { Skeleton } from '@/components/ui/skeleton';

export default function PropertyShowcaseSkeleton() {
  return (
    <div className="mb-6 px-4 md:px-8 xl:px-8 py-4 mx-auto bg-background text-foreground">
      {/* PropertiesShowcase Section */}

      {/* Breadcrumb Navigation */}
      <nav className="flex flex-col md:flex-row md:items-center justify-between gap-4 md:gap-0 mb-6">
        <div className="flex flex-wrap items-center gap-1 md:gap-2 text-sm text-muted-foreground">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-32" />
        </div>
      </nav>

      {/* Main Gallery Layout - 7 Images */}
      <div className="relative w-full mb-4 md:mb-8 rounded-xl overflow-hidden group">
        {/* Mobile View - Single Column */}
        <div className="md:hidden space-y-1 md:space-y-2">
          <Skeleton className="aspect-[4/3] rounded-md md:rounded-xl" />
          <div className="grid grid-cols-2 md:grid-cols-3 gap-1 md:gap-2">
            {Array(6)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className="aspect-[4/3] rounded-md md:rounded-xl" />
              ))}
          </div>
        </div>

        {/* Desktop View - Original Layout */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-5 gap-2 h-[40rem]">
          {/* Main Large Image - 2/5 width */}
          <Skeleton className="col-span-1 md:col-span-2 rounded-xl" />
          {/* Right Side Grid - 6 Images - 3/5 width */}
          <div className="hidden md:grid md:col-span-3 grid-cols-3 grid-rows-2 gap-2">
            {Array(6)
              .fill(0)
              .map((_, i) => (
                <Skeleton key={i} className="rounded-xl" />
              ))}
          </div>
        </div>

        {/* Show All Photos Button - Mobile */}
        <Skeleton className="md:hidden absolute bottom-4 right-4 h-8 w-32 rounded-full" />

        {/* Show All Photos Button - Desktop */}
        <Skeleton className="hidden md:block absolute bottom-4 right-4 h-10 w-40 rounded-full" />

        {/* Image Counter Badge */}
        <Skeleton className="absolute top-4 right-4 h-6 w-12 rounded-full" />
      </div>

      {/* PropertyDetailsSection */}
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Main Content - 2/3 width */}
        <div className="lg:w-2/3">
          {/* Property Header */}
          <div className="mb-4 md:mb-8">
            <div className="flex md:flex-row md:items-center justify-between gap-4 md:gap-0">
              <Skeleton className="h-8 md:h-12 w-64 md:w-96" />
              <div className="flex gap-2">
                <Skeleton className="h-8 md:h-9 w-8 md:w-10" />
                <Skeleton className="h-8 md:h-9 w-8 md:w-10" />
                <Skeleton className="h-8 md:h-9 w-8 md:w-10" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <Skeleton className="h-4 w-4 mr-1" />
              <Skeleton className="h-4 w-48" />
            </div>
            <div className="flex items-center gap-2 mt-2">
              <Skeleton className="h-3 w-32" />
            </div>
          </div>

          <div className="h-px bg-border my-4 md:my-6" />

          {/* Property Specs */}
          <div className="flex flex-wrap gap-2 md:gap-4 mb-4 md:mb-8">
            {Array(7)
              .fill(0)
              .map((_, i) => (
                <div
                  key={i}
                  className="flex flex-col items-center bg-zinc-50/50 p-2 rounded-md md:rounded-xl border md:border-2 border-zinc-200"
                >
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              ))}
          </div>

          {/* Description Section */}
          <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
            <div className="p-4 md:p-6">
              <div className="flex items-center gap-2 mb-2 md:mb-4">
                <Skeleton className="h-4 md:h-5 w-4 md:w-5" />
                <Skeleton className="h-5 md:h-6 w-16" />
              </div>
              <div className="h-px bg-border mb-4 md:mb-6" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-5/6" />
              </div>
            </div>
          </div>

          {/* Features & Amenities */}
          <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
            <div className="p-4 md:p-6">
              <div className="flex items-center gap-2 mb-2 md:mb-4">
                <Skeleton className="h-4 md:h-5 w-4 md:w-5" />
                <Skeleton className="h-5 md:h-6 w-32" />
              </div>
              <div className="h-px bg-border mb-4 md:mb-6" />
              <div className="grid grid-cols-2 sm:grid-cols-2 gap-0">
                {Array(8)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i} className="relative">
                      <div className="flex items-center gap-3 py-3">
                        <Skeleton className="h-4 md:h-5 w-4 md:w-5 flex-shrink-0" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>

          {/* Location Details */}
          <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
            <div className="p-4 md:p-6">
              <div className="flex items-center gap-2 mb-2 md:mb-4">
                <Skeleton className="h-4 md:h-5 w-4 md:w-5" />
                <Skeleton className="h-5 md:h-6 w-16" />
              </div>
              <div className="h-px bg-border mb-4 md:mb-6" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 md:mb-6">
                {Array(4)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i}>
                      <Skeleton className="h-3 w-16 mb-1" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                  ))}
              </div>
              <Skeleton className="h-64 w-full rounded-lg" />
            </div>
          </div>

          {/* Floor Plans Section */}
          <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
            <div className="p-4 md:p-6">
              <div className="flex items-center gap-2 mb-2 md:mb-4">
                <Skeleton className="h-4 md:h-5 w-4 md:w-5" />
                <Skeleton className="h-5 md:h-6 w-32" />
              </div>
              <div className="h-px bg-border mb-4 md:mb-6" />
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {Array(2)
                  .fill(0)
                  .map((_, i) => (
                    <Skeleton key={i} className="aspect-[4/3] rounded-lg" />
                  ))}
              </div>
            </div>
          </div>

          {/* Videos Section */}
          <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
            <div className="p-4 md:p-6">
              <div className="flex items-center gap-2 mb-2 md:mb-4">
                <Skeleton className="h-4 md:h-5 w-4 md:w-5" />
                <Skeleton className="h-5 md:h-6 w-32" />
              </div>
              <div className="h-px bg-border mb-4 md:mb-6" />
              <Skeleton className="aspect-video rounded-lg mb-4" />
              <Skeleton className="h-5 w-32 mb-2" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>

          {/* Property Details */}
          <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
            <div className="p-4 md:p-6">
              <div className="flex items-center gap-2 mb-2 md:mb-4">
                <Skeleton className="h-4 md:h-5 w-4 md:w-5" />
                <Skeleton className="h-5 md:h-6 w-16" />
              </div>
              <div className="h-px bg-border mb-4 md:mb-6" />
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {Array(12)
                  .fill(0)
                  .map((_, i) => (
                    <div
                      key={i}
                      className="flex justify-between p-3 bg-zinc-50/50 rounded-md border-2 border-zinc-200"
                    >
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>

        {/* Sticky Sidebar - 1/3 width */}
        <div className="lg:w-1/3">
          <div className="sticky top-20 border-2 border-zinc-200 rounded-2xl bg-background/50 p-6">
            {/* Price Section */}
            <div className="mb-6">
              <Skeleton className="h-4 w-16 mb-2" />
              <Skeleton className="h-8 md:h-10 w-32 mb-2" />
              <Skeleton className="h-5 w-24" />
            </div>

            <div className="h-px bg-border my-6" />

            {/* Action Buttons */}
            <div className="space-y-4 mb-6">
              <div className="relative">
                <Skeleton className="h-12 w-full rounded-md" />
                <Skeleton className="absolute -top-2 -right-2 h-5 w-20 rounded-full" />
              </div>
              <div className="relative">
                <Skeleton className="h-12 w-full rounded-md" />
              </div>
              <div className="relative">
                <Skeleton className="h-12 w-full rounded-md" />
                <Skeleton className="absolute -top-2 -right-2 h-5 w-20 rounded-full" />
              </div>
            </div>

            <div className="h-px bg-border my-6" />

            {/* Agent Details */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-32" />
              <div className="flex items-center gap-4">
                <Skeleton className="w-12 h-12 rounded-full" />
                <div>
                  <Skeleton className="h-4 w-24 mb-1" />
                  <Skeleton className="h-3 w-16" />
                </div>
              </div>
              <Skeleton className="h-9 w-full rounded-md" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
