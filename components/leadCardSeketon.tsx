import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

export function LeadCardSkeleton() {
  return (
    <Card className="overflow-hidden">
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div>
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
        </div>
      </div>
      <CardContent className="p-0">
        <div>
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="border-t px-4 py-3">
              <Skeleton className="h-4 w-3/4" />
            </div>
          ))}
        </div>
        <div className="border-t px-4 py-3 space-y-2 bg-gradient-to-r from-gray-100 to-gray-200">
          <div className="flex items-center justify-between rounded-md border px-4 py-2 hover:shadow-sm transition">
            <div className="flex items-center gap-3">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="flex flex-col space-y-1">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-32" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
