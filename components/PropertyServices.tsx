'use client';

import { ArrowUpRight } from 'lucide-react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

export default function PropertyServices() {
  const services = [
    {
      icon: '/icon1.png',
      title: 'Tì<PERSON> kiếm bất động sản',
      href: '#',
    },
    {
      icon: '/icon2.png',
      title: '<PERSON><PERSON><PERSON><PERSON> đáp thắc mắc',
      href: '#',
    },
    {
      icon: '/icon3.png',
      title: 'T<PERSON> vấn bất động sản',
      href: '#',
    },
    {
      icon: '/icon4.png',
      title: 'Tư vấn đầu tư',
      href: '#',
    },
  ];

  return (
    <section className="w-full max-w-[1440px] px-8 sm:px-6 md:px-8 xl:px-32 mx-auto py-8 sm:py-12 md:py-16 bg-background text-foreground ">
      <div className="max-w-screen mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {services.map((service, i) => (
          <a key={i} href={service.href} className="group">
            <Card className="relative h-full bg-white border border-red-400 shadow-sm hover:shadow-md transition-all duration-300 hover:border-red-300">
              <CardContent className="p-4 sm:p-6 flex flex-col items-center text-center space-y-3 sm:space-y-4">
                <div className="relative w-14 h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center">
                  <Image
                    src={service.icon}
                    alt={service.title}
                    fill
                    className="object-contain p-2 sm:p-3"
                    sizes="(max-width: 640px) 56px, 64px"
                  />
                </div>
                <h3 className="text-xs sm:text-sm font-medium text-gray-700 group-hover:text-red-600 transition-colors duration-300">
                  {service.title}
                </h3>
              </CardContent>

              <ArrowUpRight
                className={cn(
                  'absolute top-3 right-3 sm:top-4 sm:right-4 h-3 w-3 sm:h-4 sm:w-4 text-red-400 opacity-60 group-hover:text-red-600 group-hover:translate-x-1 group-hover:-translate-y-1 transition-all duration-300'
                )}
              />
            </Card>
          </a>
        ))}
      </div>
    </section>
  );
}
