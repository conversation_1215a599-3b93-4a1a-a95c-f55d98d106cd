'use client';

import { useState, useEffect, useRef } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
import { X, Send, MoreHorizontal, ChevronUp, MessageCircle } from 'lucide-react';
import Image from 'next/image';
import { Message, SenderType } from '@/lib/api/services/fetchChat';
import { signalRService } from '@/lib/realtime/signalR';
import { usePathname } from 'next/navigation';
import { useChat } from '@/hooks/useChat';
import Link from 'next/link';
import { PropertyInfo } from '@/lib/api/services/fetchProperty';
import { toast } from 'sonner';

// Add useIsMobile hook
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  return isMobile;
};

export const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasShownInitial, setHasShownInitial] = useState(false);
  const [closeCount, setCloseCount] = useState(0);
  const [lastClosedTime, setLastClosedTime] = useState<number | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  const prevConversationId = useRef<string | null>(null);
  const lastAutoOpenConversationId = useRef<string | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { refetchConversation, sendMessage } = useChat();
  const isMobile = useIsMobile();

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (pathname !== '/login') {
      if (!hasShownInitial) {
        timer = setTimeout(() => {
          setIsOpen(true);
          setHasShownInitial(true);
        }, 5000);
      }
      // else if (lastClosedTime && !isOpen && closeCount > 0 && closeCount < 2) {
      //   timer = setTimeout(() => {
      //     setIsOpen(true);
      //     setLastClosedTime(null);
      //   }, 30000);
      // }
    }

    return () => clearTimeout(timer);
  }, [hasShownInitial, lastClosedTime, isOpen, closeCount, pathname]);

  useEffect(() => {
    const ensureConversation = async () => {
      let cid = conversationId;
      let uid = userId;

      if (!cid || !uid) {
        const result = await refetchConversation();
        if (result.data) {
          cid = result.data?.id;
          uid = result.data?.platformUser?.id;
          setConversationId(cid);
          setUserId(uid);
          localStorage.setItem(
            'chat_conversation',
            JSON.stringify({ conversationId: cid, userId: uid })
          );
        }
      }
      if (!cid || !uid) {
        const stored = localStorage.getItem('chat_conversation');
        if (stored) {
          const parsed = JSON.parse(stored);
          cid = parsed.conversationId;
          uid = parsed.userId;
          setConversationId(cid);
          setUserId(uid);
        }
      }

      return { cid, uid };
    };

    const connectSignalR = async () => {
      const { cid, uid } = await ensureConversation();

      if (cid && uid) {
        await signalRService.connect();
        await signalRService.joinConversation(cid, uid);
        await signalRService.loadMoreMessages(cid, 1, 30);
      } else {
        await signalRService.connect();
      }

      signalRService.setMessageHandler(message => {
        setMessages(prev => {
          const existingMessage = prev.find(
            msg =>
              msg.content === message.content &&
              Math.abs(
                msg.timestamp.getTime() -
                  new Date(message.timestamp || message.createdAt || Date.now()).getTime()
              ) < 2000
          );

          if (existingMessage) {
            return prev;
          }

          const newMessage = {
            messageId: message.messageId || `${Date.now()}-${Math.random()}`,
            senderId: message.direction === 'outbound' ? 'admin' : uid || undefined,
            content: message.content,
            timestamp: new Date(message.timestamp || message.createdAt || Date.now()),
            senderType: message.direction === 'outbound' ? SenderType.Admin : SenderType.User,
            isRead: false,
            isAnonymous: !uid && message.direction !== 'outbound',
          };

          const otherMessages = prev.filter(
            msg =>
              !(
                msg.senderId === 'system' &&
                (msg.content === 'Xin chào !' || msg.content === 'Tôi có thể giúp gì cho bạn ?')
              )
          );

          // Set hasNewMessage to true if the message is from admin and chat is closed
          if (message.direction === 'outbound' && !isOpen) {
            setHasNewMessage(true);
          }

          return [...otherMessages, newMessage];
        });

        if (!isOpen && message.direction === 'outbound') {
          toast.success(`Bạn có tin nhắn mới: ${message.content}`);
        }
      });

      signalRService.setMessageHistoryHandler(historyMessages => {
        const formattedMessages = historyMessages.map(msg => {
          const isAdmin = msg.direction === 'outbound';
          return {
            messageId: msg.messageId || `${Date.now()}-${Math.random()}`,
            senderId: isAdmin ? 'admin' : uid || undefined,
            content: msg.content,
            timestamp: new Date(msg.timestamp || msg.createdAt || Date.now()),
            senderType: isAdmin ? SenderType.Admin : SenderType.User,
            isRead: true,
            isAnonymous: !uid && !isAdmin,
          };
        });

        setMessages(() => {
          const sortedHistory = formattedMessages.sort(
            (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
          );
          if (sortedHistory.length === 0) {
            return [
              {
                messageId: `${Date.now()}-greeting1`,
                senderId: 'system',
                content: 'Xin chào !',
                timestamp: new Date(),
              },
              {
                messageId: `${Date.now()}-greeting2`,
                senderId: 'system',
                content: 'Tôi có thể giúp gì cho bạn ?',
                timestamp: new Date(),
              },
            ];
          }

          return sortedHistory;
        });

        scrollToBottom();
      });

      signalRService.setNewConversationHandler(data => {
        setConversationId(data.conversationId);
        setUserId(data.userId);
        localStorage.setItem(
          'chat_conversation',
          JSON.stringify({ conversationId: data.conversationId, userId: data.userId })
        );
        signalRService.joinConversation(data.conversationId, data.userId);
      });

      signalRService.setNotificationHandler((title, body) => {
        console.log('title', title);
        if (!isOpen) {
          toast.success(`${title}: ${body}`);
        }
      });

      signalRService.setTypingHandler(_messageSessionId => {});

      signalRService.setReadHandler((_messageSessionId, messageIds) => {
        setMessages(prev =>
          prev.map(msg =>
            messageIds.includes(msg.messageId || '') ? { ...msg, isRead: true } : msg
          )
        );
      });

      signalRService.setOnlineStatusHandler(() => {
        // setAdminIsOnline(isOnline);
      });
    };

    connectSignalR();
    return () => {
      if (conversationId) {
        signalRService.leaveConversation(conversationId);
      }
    };
  }, [toast, conversationId, userId, isOpen]);

  useEffect(() => {
    if (isOpen) {
      setMessages(prev => {
        // Xoá greeting cũ nếu có
        const nonGreeting = prev.filter(
          msg =>
            !(
              msg.senderId === 'system' &&
              (msg.content === 'Xin chào !' || msg.content === 'Tôi có thể giúp gì cho bạn ?')
            )
        );
        // Thêm greeting vào cuối
        return [
          ...nonGreeting,
          {
            messageId: `${Date.now()}-greeting1`,
            senderId: 'system',
            content: 'Xin chào !',
            timestamp: new Date(),
          },
          {
            messageId: `${Date.now()}-greeting2`,
            senderId: 'system',
            content: 'Tôi có thể giúp gì cho bạn ?',
            timestamp: new Date(),
          },
        ];
      });
    }
  }, [isOpen]);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    const tempMessage: Message = {
      messageId: Date.now().toString(),
      senderId: userId || undefined,
      content: newMessage,
      timestamp: new Date(),
      senderType: SenderType.User,
      isRead: false,
      isAnonymous: !conversationId,
    };

    // Thêm tin nhắn mới vào cuối mảng
    setMessages(prev => [...prev, tempMessage]);
    setNewMessage('');

    try {
      await sendMessage({
        content: newMessage,
        senderId: userId || undefined,
        conversationId: conversationId || undefined,
      });

      // Nếu là tin nhắn đầu tiên và chưa có conversationId
      if (!conversationId) {
        // Lấy conversation từ localStorage sau khi gửi tin nhắn đầu tiên
        const stored = localStorage.getItem('chat_conversation');
        if (stored) {
          const { conversationId: storedCid, userId: storedUid } = JSON.parse(stored);
          if (storedCid && storedUid) {
            setConversationId(storedCid);
            setUserId(storedUid);
            // Join vào conversation đã lưu
            await signalRService.joinConversation(storedCid, storedUid);
            // Load tin nhắn cũ
            await signalRService.loadMoreMessages(storedCid, 1, 30);
          }
        }
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      setMessages(prev => prev.filter(msg => msg.messageId !== tempMessage.messageId));
      toast.error('Không thể gửi tin nhắn. Vui lòng thử lại.');
    }
  };

  const handleCloseChat = () => {
    setIsOpen(false);
    if (pathname !== '/login') {
      setCloseCount(prev => prev + 1);
      setLastClosedTime(Date.now());
    }
  };

  const getMessageStyle = (message: Message) => {
    if (
      message.isAnonymous ||
      message.senderId === 'user' ||
      message.senderType === SenderType.User
    ) {
      return 'bg-red-500 text-white';
    }
    return 'bg-gray-100 text-gray-800';
  };

  const isUserMessage = (message: Message) => {
    return (
      message.isAnonymous || message.senderId === 'user' || message.senderType === SenderType.User
    );
  };

  const renderMessage = (message: Message) => {
    // Kiểm tra nếu là message property info
    let propertyInfo: PropertyInfo | null = null;
    try {
      const messageType = (message as Message & { messageType: string }).messageType;
      if (
        messageType === 'property' ||
        (typeof message.content === 'string' && message.content.includes('address'))
      ) {
        propertyInfo = JSON.parse(message.content);
      }
    } catch (error) {
      console.error('Error parsing property info:', error);
    }

    if (propertyInfo) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-1 mb-2 flex flex-col gap-2">
          <div className="flex items-start gap-2 flex-col">
            <div className="flex items-center gap-2">
              {propertyInfo.imageUrls && propertyInfo.imageUrls.length > 0 && (
                <Image
                  src={propertyInfo.imageUrls[0]}
                  alt="Property Image"
                  width={70}
                  height={70}
                  className="rounded-lg "
                />
              )}
              <div className="flex flex-col gap-1">
                <span className="text-xs text-gray-700">Name: {propertyInfo.name}</span>
                <span className="text-xs text-gray-700">Trạng thái: {propertyInfo.status}</span>
                <span className="text-xs text-gray-700">
                  Diện tích: {propertyInfo.propertyDetails?.landArea}m²
                </span>
              </div>
            </div>
            <span className="text-xs text-red-600 font-semibold">ID:{propertyInfo.id}</span>
            <span className="text-xs text-gray-700">Loại: {propertyInfo.type}</span>
            <span className="text-xs text-gray-700">Địa chỉ: {propertyInfo.address}</span>
            {propertyInfo.propertyDetails && (
              <div className="flex flex-wrap gap-2 mt-1">
                {propertyInfo.propertyDetails.bedrooms !== undefined && (
                  <span className="text-xs text-gray-700 ">
                    Phòng ngủ: {propertyInfo.propertyDetails.bedrooms}
                  </span>
                )}
                {propertyInfo.propertyDetails.bathrooms !== undefined && (
                  <span className="text-xs text-gray-700 ">
                    Phòng tắm: {propertyInfo.propertyDetails.bathrooms}
                  </span>
                )}
                {propertyInfo.propertyDetails.livingRooms !== undefined && (
                  <span className="text-xs text-gray-700 ">
                    Phòng khách: {propertyInfo.propertyDetails.livingRooms}
                  </span>
                )}
                {propertyInfo.propertyDetails.kitchens !== undefined && (
                  <span className="text-xs text-gray-700 ">
                    Phòng bếp: {propertyInfo.propertyDetails.kitchens}
                  </span>
                )}
                {propertyInfo.propertyDetails.floorNumber !== undefined && (
                  <span className="text-xs text-gray-700 ">
                    Số tầng: {propertyInfo.propertyDetails.floorNumber}
                  </span>
                )}
                {propertyInfo.propertyDetails.landArea !== undefined && (
                  <span className="text-xs text-gray-700 ">
                    Năm Xây dựng: {propertyInfo.yearBuilt}
                  </span>
                )}
              </div>
            )}
          </div>
          <Link href={`/properties/${propertyInfo.id}`} legacyBehavior>
            <a
              onClick={() => setIsOpen(false)}
              className="mt-2 inline-block px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-xs font-medium text-center"
            >
              Preview
            </a>
          </Link>
        </div>
      );
    }
    // Mặc định: render message thường
    return <div className="text-800">{message.content}</div>;
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isOpen]);

  useEffect(() => {
    const handleLogout = () => {
      if (isOpen) {
        handleCloseChat();
      }
    };

    // Listen for logout event
    window.addEventListener('logout', handleLogout);

    return () => {
      window.removeEventListener('logout', handleLogout);
    };
  }, [isOpen]);

  useEffect(() => {
    if (pathname === '/login') {
      if (conversationId) {
        signalRService.leaveConversation(conversationId);
      }
      setIsOpen(false);
      setMessages([]);
      setConversationId(null);
      setUserId(null);
    }
  }, [pathname]);

  useEffect(() => {
    if (pathname !== '/login') {
      setMessages([]);
      refetchConversation().then(result => {
        if (result.data) {
          setConversationId(result.data.id);
          setUserId(result.data.platformUser?.id);
        } else {
          setConversationId(null);
          setUserId(null);
        }
      });
    }
  }, [pathname, refetchConversation]);

  useEffect(() => {
    if (
      conversationId &&
      userId &&
      pathname !== '/login' &&
      lastAutoOpenConversationId.current !== conversationId
    ) {
      signalRService.joinConversation(conversationId, userId);
      const timer = setTimeout(() => setIsOpen(true), 5000);
      lastAutoOpenConversationId.current = conversationId;
      return () => clearTimeout(timer);
    }
  }, [conversationId, userId, pathname]);

  useEffect(() => {
    if (conversationId && conversationId !== prevConversationId.current && pathname !== '/login') {
      setMessages([]); // clear messages khi chuyển conversation
      prevConversationId.current = conversationId;
    }
  }, [conversationId, pathname]);

  // Effect chặn scroll bubbling ra ngoài và chỉ cho phép cuộn trong chat
  useEffect(() => {
    const chatEl = chatContainerRef.current;
    if (!chatEl) return;

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      e.stopPropagation();
      chatEl.scrollTop += e.deltaY;
    };

    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    chatEl.addEventListener('wheel', handleWheel, { passive: false });
    chatEl.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      chatEl.removeEventListener('wheel', handleWheel);
      chatEl.removeEventListener('touchmove', handleTouchMove);
    };
  }, [isOpen]);

  // Reset hasNewMessage when chat is opened
  useEffect(() => {
    if (isOpen) {
      setHasNewMessage(false);
    }
  }, [isOpen]);

  return (
    <div
      style={{
        position: 'fixed',
        bottom: isMobile ? '20px' : '0',
        right: isMobile ? '20px' : '0',
        zIndex: 9999,
      }}
    >
      {/* <AnimatePresence> */}
      {isOpen && (
        <div
          // initial={{ opacity: 0, y: 20 }}
          // animate={{ opacity: 1, y: 0 }}
          // exit={{ opacity: 0, y: 20 }}
          className={`fixed ${isMobile ? 'inset-0' : 'bottom-0 right-0 w-[360px] h-[400px]'} bg-white shadow-xl flex flex-col cursor-default rounded-lg `}
          style={{
            pointerEvents: 'auto',
            zIndex: 9999,
          }}
        >
          <div className="bg-red-600 text-white p-4 flex justify-between items-center rounded-lg">
            <div className="flex items-center gap-2">
              <Image
                src="/logo_revoland_red.png"
                alt="Revoland Logo"
                width={33}
                height={33}
                className="rounded-full bg-white"
              />
              <div>
                <h3 className="font-semibold">Revoland</h3>
                <span className="text-xs flex items-center gap-1">Hãy chat với chúng tôi</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleCloseChat}
                className="text-white hover:text-gray-200 transition-colors"
                aria-label="Close chat"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          <div
            ref={chatContainerRef}
            className="flex-1 overflow-y-auto p-4 flex flex-col"
            id="chat-messages"
            style={{ transform: 'translateZ(0)', overscrollBehavior: 'contain' }}
          >
            {messages.map(message => {
              return (
                <div
                  key={message.messageId}
                  className={`flex items-end gap-2 pb-1 text-sm  ${
                    isUserMessage(message) ? 'justify-end' : 'justify-start'
                  }`}
                >
                  {/* {!isUserMessage(message) && (
                      <div className="flex-shrink-0" style={{ width: 24, height: 24 }}>
                        {isLastInGroup ? (
                          <Image
                            src="/logo_revoland_red.png"
                            alt="Revoland Logo"
                            width={24}
                            height={24}
                            className="rounded-full bg-gray-100"
                          />
                        ) : null}
                      </div>
                    )} */}
                  <div
                    className={`max-w-[70%] rounded-2xl ${isUserMessage(message) ? 'rounded-br-none' : 'rounded-bl-none'} p-2 ${getMessageStyle(message)}`}
                  >
                    {renderMessage(message)}
                    {isUserMessage(message) && (
                      <div className="text-xs text-right mt-1">
                        {/* trạng thái đã gửi/đã xem nếu cần */}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </div>

          <form onSubmit={handleSendMessage} className="p-3 border-t">
            <div className="flex gap-2">
              <input
                type="text"
                value={newMessage}
                onChange={handleMessageChange}
                placeholder="Nhập tin nhắn..."
                className="flex-1 rounded-full px-4 py-2 border focus:outline-none focus:border-red-600"
              />
              <button type="submit">
                <Send className="text-red-500" size={20} />
              </button>
            </div>
          </form>
        </div>
      )}
      {/* </AnimatePresence> */}

      {/* <AnimatePresence> */}
      {!isOpen && (
        <button
          // initial={{ opacity: 0, x: 40 }}
          // animate={{ opacity: 1, x: 0 }}
          // exit={{ opacity: 0, x: 40 }}
          // whileHover={{ scale: 1.03 }}
          // whileTap={{ scale: 0.98 }}
          onClick={() => setIsOpen(true)}
          className={`${isMobile ? 'w-14 h-14 rounded-full' : 'w-[360px] h-12 rounded-t-xl rounded-b-none'} flex items-center justify-center bg-red-500 shadow-xl transition-colors cursor-pointer border border-gray-200 relative`}
          aria-label="Open chat"
          style={{
            pointerEvents: 'auto',
            zIndex: 9998,
          }}
        >
          {isMobile ? (
            <>
              <MessageCircle size={28} className="text-white" />
              {hasNewMessage && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-600 rounded-full border-2 border-white animate-pulse" />
              )}
            </>
          ) : (
            <>
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Image
                    src="/logo_revoland_red.png"
                    alt="Avatar"
                    width={32}
                    height={32}
                    className="rounded-full bg-gray-100"
                  />
                  {hasNewMessage && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-600 rounded-full border-2 border-white animate-pulse" />
                  )}
                </div>
                <div className="flex flex-col">
                  <span className="font-semibold leading-none text-sm text-white">
                    Chat tư vấn-Giải đáp mọi thắc mắc
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-2 text-white">
                <MoreHorizontal size={20} />
                <ChevronUp size={20} />
              </div>
            </>
          )}
        </button>
      )}
      {/* </AnimatePresence> */}
    </div>
  );
};
