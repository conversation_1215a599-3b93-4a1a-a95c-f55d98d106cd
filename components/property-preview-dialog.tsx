'use client';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Property } from '@/lib/api/services/fetchProperty';

import { APIProvider } from '@vis.gl/react-google-maps';
import PropertyDetailsSection from '@/app/(user)/properties/[id]/component/PropertyDetailsSection';
import PropertyShowcase from '@/app/(user)/properties/[id]/component/PropertiesShowcase';

interface PropertyPreviewDialogProps {
  property: Property;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PropertyPreviewDialog({
  property,
  open,
  onOpenChange,
}: PropertyPreviewDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[120rem] max-h-[90vh] p-0">
        <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
          <ScrollArea className="h-[90vh]">
            <div className="bg-background text-foreground">
              <div className="w-full max-w-screen px-4 md:px-8 xl:px-8 py-4 mx-auto bg-background text-foreground">
                <PropertyShowcase property={property} />
                <div className="xl:px-32">
                  <PropertyDetailsSection property={property} />
                  {/* <TopPropertiesSection currentPropertyId={property.id} /> */}
                  {/* <PropertyPriceDetails property={property} />
                  <ContactForm property={property} /> */}
                </div>
              </div>
            </div>
          </ScrollArea>
        </APIProvider>
      </DialogContent>
    </Dialog>
  );
}
