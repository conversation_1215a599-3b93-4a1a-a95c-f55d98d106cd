import { useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { GoogleCredentialResponse } from '@/lib/api/services/fetchAuth';

interface GoogleAuthButtonProps {
  mode: 'login' | 'register';
  onError: (error: string) => void;
  className?: string;
}

export function GoogleAuthButton({ mode, onError }: GoogleAuthButtonProps) {
  const { googleLogin } = useAuth();

  const handleGoogleAuth = useCallback(
    async (credentialResponse: GoogleCredentialResponse) => {
      const idToken = credentialResponse.credential;
      if (idToken) {
        googleLogin(idToken);
      } else {
        onError('Không nhận được token từ Google');
      }
    },
    [googleLogin, onError]
  );

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Remove any existing Google Sign-In script to prevent duplicates
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      );
      if (existingScript) {
        document.head.removeChild(existingScript);
      }

      // Add new script
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        interface GoogleWindow extends Window {
          google?: {
            accounts: {
              id: {
                initialize: (config: {
                  client_id: string;
                  callback: (response: GoogleCredentialResponse) => void;
                  auto_select?: boolean;
                  cancel_on_tap_outside?: boolean;
                  context?: string;
                }) => void;
                renderButton: (
                  element: HTMLElement | null,
                  options: {
                    type?: 'standard' | 'icon';
                    theme?: 'outline' | 'filled_blue' | 'filled_black';
                    size?: 'large' | 'medium' | 'small';
                    text?: string;
                    shape?: 'rectangular' | 'pill' | 'circle' | 'square';
                    logo_alignment?: 'left' | 'center';
                    width?: number;
                    locale?: string;
                  }
                ) => void;
              };
            };
          };
        }
        const googleWindow = window as unknown as GoogleWindow;
        if (googleWindow.google) {
          googleWindow.google.accounts.id.initialize({
            client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
            callback: handleGoogleAuth,
            auto_select: false,
            cancel_on_tap_outside: true,
            context: 'signin',
          });
          const googleButtonElement = document.getElementById('googleSignInButton');
          if (googleButtonElement) {
            googleWindow.google.accounts.id.renderButton(googleButtonElement, {
              type: 'standard',
              theme: 'outline',
              size: 'large',
              text: mode === 'login' ? 'login_with' : 'signup_with',
              shape: 'rectangular',
              logo_alignment: 'left',
              width: googleButtonElement.offsetWidth,
              locale: 'vi',
            });
          }
        }
      };
      document.head.appendChild(script);

      return () => {
        // Cleanup script when component unmounts
        const scriptToRemove = document.querySelector(
          'script[src="https://accounts.google.com/gsi/client"]'
        );
        if (scriptToRemove) {
          document.head.removeChild(scriptToRemove);
        }
      };
    }
  }, [handleGoogleAuth, mode]);

  return <div id="googleSignInButton" className="w-full flex justify-center" />;
}
