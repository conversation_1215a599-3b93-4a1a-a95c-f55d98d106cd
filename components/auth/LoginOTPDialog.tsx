'use client';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { OTPVerification } from './OTPVerification';

interface LoginOTPDialogProps {
  isOpen: boolean;
  onClose: () => void;
  verifyKey: string | null;
}

export function LoginOTPDialog({ isOpen, onClose, verifyKey }: LoginOTPDialogProps) {
  if (!verifyKey) return null;

  const handleVerificationSuccess = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md p-0">
        <OTPVerification
          keyRegister={verifyKey}
          onClose={onClose}
          onVerificationSuccess={handleVerificationSuccess}
        />
      </DialogContent>
    </Dialog>
  );
}
