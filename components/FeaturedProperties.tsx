'use client';

import { useState } from 'react';
import { useProperties } from '@/hooks/useProperty';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { PropertyCard } from '@/components/PropertyCard';
import { Card } from '@/components/ui/card';
import Link from 'next/link';

export default function FeaturedProperties() {
  const [page] = useState(1);

  const { properties, isLoading, isError } = useProperties();
  const featuredProperties = Array.isArray(properties) ? properties.filter(p => p.isFeatured) : [];

  const pageSize = 12;
  const paginated = featuredProperties.slice((page - 1) * pageSize, page * pageSize);

  if (isLoading) {
    return (
      <section className="flex justify-center items-center min-h-[500px] bg-background text-foreground ">
        <SkeletonGrid />
      </section>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center min-h-[500px] text-red-500">
        Error loading properties
      </div>
    );
  }

  return (
    <section className="w-full max-w-[1440px] px-8 md:px-8 xl:px-32 mx-auto py-16 bg-background text-foreground ">
      <div className="mb-10 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-3xl md:text-4xl font-semibold">Bất động sản nổi bật</h2>
          <p className="text-muted-foreground text-sm md:text-base mt-2 max-w-5xl">
            Bất động sản nổi bật
          </p>
        </div>
        <Link href="/properties">
          <Button
            variant="outline"
            className="bg-red-50 text-red-600 border-red-200 hover:bg-red-100 hover:text-red-600 hover:border-red-200"
          >
            Xem tất cả bất động sản
          </Button>
        </Link>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {paginated.map((property, index) => (
          <PropertyCard key={property.id} property={property} priority={index < 2} />
        ))}
      </div>
    </section>
  );
}

// Skeleton component for loading state
function SkeletonGrid() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-screen-xl px-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i} className="bg-background border-muted-foreground/20">
          <Skeleton className="w-full h-64 rounded-t-lg" />
          <div className="p-6 space-y-4">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <div className="flex gap-4 mt-4">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="flex justify-between items-center mt-6">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-10 w-28 rounded-md" />
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
