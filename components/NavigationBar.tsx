'use client';

import * as React from 'react';
import Link from 'next/link';
import {
  Home,
  Building2,
  LandPlot,
  Building,
  Store,
  HomeIcon,
  PiggyBankIcon,
  UserSearchIcon,
  PhoneCallIcon,
  CalendarIcon,
  UsersIcon,
  HousePlus,
  StoreIcon,
  BedDouble,
  Warehouse,
  SearchIcon,
  BookmarkIcon,
  EyeIcon,
  SettingsIcon,
  BellIcon,
  Send,
  ScaleIcon,
  ClipboardListIcon,
  TruckIcon,
  Armchair,
} from 'lucide-react';

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { cn } from '@/lib/utils';

const saleProperties = [
  {
    title: 'Chung cư bán',
    href: '/properties?propertyType=apartment&transactionType=ForSale',
    description: 'Tìm mua căn hộ với giá tốt nhất',
    icon: Building2,
    color: 'text-blue-500',
  },
  {
    title: 'Nhà riêng bán',
    href: '/properties?propertyType=shop_house&transactionType=ForSale',
    description: 'Tìm mua nhà riêng phù hợp',
    icon: Home,
    color: 'text-green-500',
  },
  {
    title: 'Đất bán',
    href: '/properties?propertyType=land_plot&transactionType=ForSale',
    description: 'Tìm mua đất với vị trí đắc địa',
    icon: LandPlot,
    color: 'text-amber-500',
  },
  {
    title: 'Văn phòng bán',
    href: '/properties?propertyType=apartment&transactionType=ForSale',
    description: 'Tìm mua văn phòng làm việc',
    icon: Building,
    color: 'text-purple-500',
  },
];

const rentProperties = [
  {
    title: 'Chung cư thuê',
    href: '/properties?propertyType=apartment&transactionType=ForRent',
    description: 'Tìm thuê căn hộ với giá tốt nhất',
    icon: StoreIcon,
    color: 'text-indigo-500',
  },
  {
    title: 'Nhà riêng thuê',
    href: '/properties?propertyType=shop_house&transactionType=ForRent',
    description: 'Tìm thuê nhà riêng phù hợp',
    icon: HousePlus,
    color: 'text-emerald-500',
  },
  {
    title: 'Văn phòng thuê',
    href: '/properties?propertyType=apartment&transactionType=ForRent',
    description: 'Tìm thuê văn phòng làm việc',
    icon: Warehouse,
    color: 'text-rose-500',
  },
  {
    title: 'Phòng trọ thuê',
    href: '/properties?propertyType=shop_house&transactionType=ForRent',
    description: 'Tìm thuê phòng trọ giá rẻ',
    icon: BedDouble,
    color: 'text-orange-500',
  },
];

const myProperties = [
  {
    title: 'Nhà của bạn',
    href: '/myrevo',
    description: 'Xem bất động sản của tôi',
    icon: HomeIcon,
    color: 'text-red-500',
  },
  {
    title: 'Nhà đã lưu',
    href: '/myrevo',
    description: 'Xem nhà đã lưu của bạn',
    icon: BookmarkIcon,
    color: 'text-yellow-500',
  },
  {
    title: 'Đã xem gần đây',
    href: '/myrevo',
    description: 'Xem bất động sản đã xem',
    icon: EyeIcon,
    color: 'text-green-500',
  },
  {
    title: 'Quản lý lịch hẹn',
    href: '/myrevo',
    description: 'Xem lịch hẹn của bạn',
    icon: CalendarIcon,
    color: 'text-blue-500',
  },
  {
    title: 'Tìm kiếm đã lưu',
    href: '/myrevo',
    description: 'Xem tìm kiếm đã lưu của bạn',
    icon: SearchIcon,
    color: 'text-purple-500',
  },
  {
    title: 'Tin nhắn của bạn',
    href: '/myrevo',
    description: 'Xem tin nhắn của bạn',
    icon: Send,
    color: 'text-orange-500',
  },
  {
    title: 'Thông báo',
    href: '/myrevo',
    description: 'Xem thông báo của bạn',
    icon: BellIcon,
    color: 'text-pink-500',
  },
  {
    title: 'Cài đặt tài khoản',
    href: '/myrevo',
    description: 'Quản lý cài đặt tài khoản của bạn',
    icon: SettingsIcon,
    color: 'text-teal-500',
  },
];
const services = [
  {
    title: 'Vay mua nhà',
    href: '/services/loan',
    description: 'Hỗ trợ vay mua nhà với lãi suất ưu đãi',
    icon: PiggyBankIcon,
    color: 'text-purple-500',
  },
  {
    title: 'Tìm người bán',
    href: '/services/find-seller',
    description: 'Kết nối với chủ bất động sản',
    icon: UserSearchIcon,
    color: 'text-indigo-500',
  },
  {
    title: 'Tư vấn nhà ở',
    href: '/services/consultation',
    description: 'Được tư vấn bởi chuyên gia bất động sản',
    icon: PhoneCallIcon,
    color: 'text-teal-500',
  },
  {
    title: 'Tìm bạn ở ghép',
    href: '/services/find-roommate',
    description: 'Tìm bạn ở ghép để chia tiền phòng',
    icon: UsersIcon,
    color: 'text-green-500',
  },
  {
    title: 'Định giá nhà',
    href: '/services/property-valuation',
    description: 'Định giá bất động sản chính xác và nhanh chóng',
    icon: ScaleIcon,
    color: 'text-red-500',
  },
  {
    title: 'Quản lý nhà',
    href: '/services/property-management',
    description: 'Dịch vụ quản lý tài sản chuyên nghiệp',
    icon: ClipboardListIcon,
    color: 'text-blue-500',
  },
  {
    title: 'Dịch vụ chuyển nhà',
    href: '/services/moving',
    description: 'Dịch vụ chuyển nhà nhanh chóng và an toàn',
    icon: TruckIcon,
    color: 'text-yellow-500',
  },
  {
    title: 'Thiết kế nội thất',
    href: '/services/interior-design',
    description: 'Thiết kế nội thất sáng tạo và hiện đại',
    icon: Armchair,
    color: 'text-orange-500',
  },
];

export function NavigationBar() {
  return (
    <NavigationMenu viewport={false}>
      <NavigationMenuList className="gap-1 font-mann">
        <NavigationMenuItem>
          <NavigationMenuTrigger className="gap-2">
            <Building className="h-4 w-4" />
            Bất động sản
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[400px] gap-3 p-2 md:w-[600px] md:grid-cols-2 lg:w-[700px] max-w-[90vw]">
              <div className="grid grid-cols-2 gap-6 row-span-4">
                {/* Sale Column */}
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold text-foreground mb-3 ml-3">Mua bán</h3>
                  {saleProperties.map(item => (
                    <ListItem
                      key={item.title}
                      title={item.title}
                      href={item.href}
                      icon={item.icon}
                      color={item.color}
                    >
                      {/* {item.description} */}
                    </ListItem>
                  ))}
                </div>

                {/* Rent Column */}
                <div className="space-y-3">
                  <h3 className="text-sm font-semibold text-foreground mb-3 ml-3">Cho thuê</h3>
                  {rentProperties.map(item => (
                    <ListItem
                      key={item.title}
                      title={item.title}
                      href={item.href}
                      icon={item.icon}
                      color={item.color}
                    >
                      {/* {item.description} */}
                    </ListItem>
                  ))}
                </div>
              </div>
              <li className="row-span-4">
                <NavigationMenuLink asChild>
                  <Link
                    className="relative flex h-full w-full flex-col justify-end rounded-md bg-cover bg-center p-6 no-underline outline-hidden select-none focus:shadow-md"
                    href="/properties"
                    style={{
                      backgroundImage:
                        "url('https://images.unsplash.com/photo-1580587771525-78b9dba3b914?q=80&w=3474&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')",
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 rounded-md"></div>
                    <div className="relative mt-4 mb-2 text-lg font-medium text-white">
                      Bất động sản
                    </div>
                    <p className="relative text-white text-sm leading-tight">
                      Tìm kiếm bất động sản phù hợp với nhu cầu của bạn
                    </p>
                  </Link>
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuTrigger className="gap-2">
            <Store className="h-4 w-4" />
            Dịch vụ
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[400px] gap-3 p-2 md:w-[600px] md:grid-cols-2 lg:w-[800px] max-w-[90vw]">
              <div className="grid grid-cols-2 gap-3 row-span-4">
                {services.map(item => (
                  <ListItem
                    key={item.title}
                    title={item.title}
                    href={item.href}
                    icon={item.icon}
                    color={item.color}
                  >
                    {/* {item.description} */}
                  </ListItem>
                ))}
              </div>
              <li className="row-span-4">
                <NavigationMenuLink asChild>
                  <Link
                    className="relative flex h-full w-full flex-col justify-end rounded-md bg-cover bg-center p-6 no-underline outline-hidden select-none focus:shadow-md"
                    href="/services"
                    style={{
                      backgroundImage:
                        "url('https://images.unsplash.com/photo-1613977257592-4871e5fcd7c4?q=80&w=3540&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')",
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 rounded-md"></div>
                    <div className="relative mt-4 mb-2 text-lg font-medium text-white">Dịch vụ</div>
                    <p className="relative text-white text-sm leading-tight">
                      Khám phá các dịch vụ bất động sản chuyên nghiệp
                    </p>
                  </Link>
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuTrigger className="gap-2">
            <HomeIcon className="h-4 w-4" />
            Bất động sản của tôi
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[400px] gap-3 p-2 md:w-[600px] md:grid-cols-2 lg:w-[700px] max-w-[90vw]">
              <div className="grid grid-cols-2 gap-3 row-span-4">
                {myProperties.map(item => (
                  <ListItem
                    key={item.title}
                    title={item.title}
                    href={item.href}
                    icon={item.icon}
                    color={item.color}
                  >
                    {/* {item.description} */}
                  </ListItem>
                ))}
              </div>
              <li className="row-span-4">
                <NavigationMenuLink asChild>
                  <Link
                    className="relative flex h-full w-full flex-col justify-end rounded-md bg-cover bg-center p-6 no-underline outline-hidden select-none focus:shadow-md"
                    href="/myrevo"
                    style={{
                      backgroundImage:
                        "url('https://images.unsplash.com/photo-1613490493576-7fde63acd811?q=80&w=3542&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')",
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 rounded-md"></div>
                    <div className="relative mt-4 mb-2 text-lg font-medium text-white">
                      Bất động sản của tôi
                    </div>
                    <p className="relative text-white text-sm leading-tight">
                      Quản lý bất động sản và lịch hẹn của bạn
                    </p>
                  </Link>
                </NavigationMenuLink>
              </li>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
}

interface ListItemProps extends React.ComponentPropsWithoutRef<'li'> {
  href: string;
  title: string;
  icon?: React.ElementType;
  color?: string;
}

function ListItem({ title, children, href, icon: Icon, color, ...props }: ListItemProps) {
  return (
    <li {...props}>
      <NavigationMenuLink asChild>
        <Link
          href={href}
          className="group block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
        >
          <div className="flex items-center gap-2">
            {Icon && (
              <Icon className={cn('h-4 w-4 transition-colors group-hover:scale-110', color)} />
            )}
            <div className="text-sm font-medium leading-none">{title}</div>
          </div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground mt-2">{children}</p>
        </Link>
      </NavigationMenuLink>
    </li>
  );
}
