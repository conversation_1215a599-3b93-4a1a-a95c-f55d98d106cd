'use client';

export default function CircleButton() {
  return (
    <div className="w-[100px] h-[100px] relative">
      <svg
        viewBox="0 0 100 100"
        width="100"
        height="100"
        className="fill-primary text-primary drop-shadow-lg"
      >
        {/* Center circle with glowing border */}
        <circle cx="50" cy="50" r="20" stroke="currentColor" strokeWidth="1.2" fill="transparent" />

        {/* Arrow pointing outward */}
        <g transform="translate(50,50) rotate(45)">
          <path d="M0,-5 L0,5" stroke="white" strokeWidth="1.5" />
          <path d="M0,-5 L3,-3" stroke="white" strokeWidth="1.5" />
          <path d="M0,-5 L-3,-3" stroke="white" strokeWidth="1.5" />
        </g>

        {/* Rotating outer circle */}
        <g
          style={{
            transformOrigin: 'center',
            animation: 'rotateCircle 10s linear infinite',
          }}
        >
          <circle
            cx="50"
            cy="50"
            r="48"
            stroke="currentColor"
            strokeWidth="1.2"
            fill="transparent"
            className="opacity-70"
          />

          <defs>
            <path id="circlePath" d="M50,50 m-35,0 a35,35 0 1,1 70,0 a35,35 0 1,1 -70,0" />
          </defs>

          <text fontSize="10" letterSpacing="0.5" className="uppercase tracking-wider text-primary">
            <textPath
              xlinkHref="#circlePath"
              startOffset="50%"
              textAnchor="middle"
              dominantBaseline="middle"
            >
              Discover Your Dream Property ✨
            </textPath>
          </text>
        </g>
      </svg>

      <style jsx>{`
        @keyframes rotateCircle {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
}
