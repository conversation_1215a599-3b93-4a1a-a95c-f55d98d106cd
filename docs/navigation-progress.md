# Navigation Progress System

A YouTube-style navigation progress bar that shows a progress line at the top of the page when navigating between pages. This system provides a smooth user experience by giving visual feedback during page transitions.

## Features

- **Automatic Detection**: Detects all internal link clicks automatically
- **Smart Filtering**: Ignores external links, anchors, and downloads
- **Realistic Progress**: Simulates realistic loading progress
- **Smooth Animations**: Beautiful shimmer effects and transitions
- **Customizable**: Manual control and configurable behavior
- **Performance Optimized**: Lightweight with no impact on page load

## Quick Start

The navigation progress system is already integrated into your app. It works automatically with all internal links. Visit `/navigation-demo` to see it in action.

## Components

### NavigationProgress

The main progress bar component that appears at the top of the page.

```tsx
import { NavigationProgress } from '@/components/ui/navigation-progress';

<NavigationProgress isNavigating={true} progress={75} />;
```

### NavigationProgressAdvanced

Enhanced version with more sophisticated animations and effects.

```tsx
import { NavigationProgressAdvanced } from '@/components/ui/navigation-progress';

<NavigationProgressAdvanced isNavigating={true} progress={75} />;
```

## Hooks

### useNavigationProgress

Basic hook for managing navigation state.

```tsx
import { useNavigationProgress } from '@/hooks/useNavigationProgress';

const { isNavigating, progress, startNavigation, completeNavigation } = useNavigationProgress();
```

### useNavigationProgressEnhanced

Enhanced hook with more sophisticated tracking and timing.

```tsx
import { useNavigationProgressEnhanced } from '@/hooks/useNavigationProgress';

const { isNavigating, progress, startNavigation, completeNavigation } =
  useNavigationProgressEnhanced();
```

### useNavigationProgressContext

Context hook for accessing navigation state from anywhere in the app.

```tsx
import { useNavigationProgressContext } from '@/components/providers/navigationProgressProvider';

const { isNavigating, progress, startNavigation, completeNavigation } =
  useNavigationProgressContext();
```

## Custom Link Components

### NavigationLink

Enhanced Link component that integrates with the navigation progress system.

```tsx
import { NavigationLink } from '@/components/ui/navigation-link';

<NavigationLink href="/properties" showProgress={true} progressDelay={0}>
  Go to Properties
</NavigationLink>;
```

### EnhancedNavigationLink

Advanced Link component with more configuration options.

```tsx
import { EnhancedNavigationLink } from '@/components/ui/navigation-link';

<EnhancedNavigationLink
  href="/login"
  progressDelay={500}
  progressType="on-hover"
  onNavigationStart={() => console.log('Starting...')}
  onNavigationComplete={() => console.log('Complete!')}
>
  Login with Hover Progress
</EnhancedNavigationLink>;
```

### NavigationButton

Button component that can trigger navigation progress.

```tsx
import { NavigationButton } from '@/components/ui/navigation-link';

<NavigationButton
  onClick={() => router.push('/properties')}
  progressDelay={1000}
  onNavigationStart={() => console.log('Starting...')}
>
  Navigate to Properties
</NavigationButton>;
```

## Configuration Options

### NavigationLink Props

| Prop            | Type      | Default | Description                         |
| --------------- | --------- | ------- | ----------------------------------- |
| `showProgress`  | `boolean` | `true`  | Whether to show progress bar        |
| `progressDelay` | `number`  | `0`     | Delay before starting progress (ms) |

### EnhancedNavigationLink Props

| Prop                   | Type                                     | Default       | Description                         |
| ---------------------- | ---------------------------------------- | ------------- | ----------------------------------- |
| `showProgress`         | `boolean`                                | `true`        | Whether to show progress bar        |
| `progressDelay`        | `number`                                 | `0`           | Delay before starting progress (ms) |
| `progressType`         | `'immediate' \| 'delayed' \| 'on-hover'` | `'immediate'` | When to start progress              |
| `onNavigationStart`    | `() => void`                             | -             | Callback when navigation starts     |
| `onNavigationComplete` | `() => void`                             | -             | Callback when navigation completes  |

### NavigationButton Props

| Prop                | Type         | Default | Description                         |
| ------------------- | ------------ | ------- | ----------------------------------- |
| `showProgress`      | `boolean`    | `true`  | Whether to show progress bar        |
| `progressDelay`     | `number`     | `0`     | Delay before starting progress (ms) |
| `onNavigationStart` | `() => void` | -       | Callback when navigation starts     |

## Usage Examples

### Basic Usage

The system works automatically with all internal links:

```tsx
import Link from 'next/link';

// This will automatically trigger the progress bar
<Link href="/properties">Go to Properties</Link>;
```

### Manual Control

```tsx
import { useNavigationProgressContext } from '@/components/providers/navigationProgressProvider';

function MyComponent() {
  const { startNavigation, completeNavigation } = useNavigationProgressContext();

  const handleCustomNavigation = () => {
    startNavigation();

    // Do some async work
    setTimeout(() => {
      router.push('/properties');
      completeNavigation();
    }, 2000);
  };

  return <button onClick={handleCustomNavigation}>Custom Navigation</button>;
}
```

### Conditional Progress

```tsx
import { NavigationLink } from '@/components/ui/navigation-link';

<NavigationLink href="/properties" showProgress={shouldShowProgress}>
  Go to Properties
</NavigationLink>;
```

### Delayed Navigation

```tsx
import { EnhancedNavigationLink } from '@/components/ui/navigation-link';

<EnhancedNavigationLink
  href="/login"
  progressDelay={1000}
  onNavigationStart={() => {
    // Show loading state
    setIsLoading(true);
  }}
>
  Login with Delay
</EnhancedNavigationLink>;
```

### Hover-triggered Progress

```tsx
import { EnhancedNavigationLink } from '@/components/ui/navigation-link';

<EnhancedNavigationLink href="/properties" progressType="on-hover">
  Hover to Start Progress
</EnhancedNavigationLink>;
```

## Customization

### Styling

You can customize the progress bar appearance by modifying the CSS classes in `components/ui/navigation-progress.tsx`:

```tsx
// Change colors
className = 'fixed top-0 left-0 right-0 z-[9999] h-1 bg-gradient-to-r from-blue-500 to-purple-500';

// Change height
className = 'fixed top-0 left-0 right-0 z-[9999] h-2 bg-gradient-to-r from-primary to-primary/80';

// Add custom animations
className =
  'fixed top-0 left-0 right-0 z-[9999] h-1 bg-gradient-to-r from-primary to-primary/80 animate-pulse';
```

### Progress Simulation

You can customize the progress simulation by modifying the hook:

```tsx
// In useNavigationProgress.ts
const interval = setInterval(() => {
  setProgress(prev => {
    if (prev >= 90) return prev;
    // Customize the increment logic
    return prev + Math.random() * 20 + 10; // Faster progress
  });
}, 50); // Faster updates
```

## Performance Considerations

- The system uses passive event listeners for better performance
- Progress intervals are properly cleaned up to prevent memory leaks
- The progress bar only renders when needed
- Event listeners are added/removed efficiently

## Browser Compatibility

- Works with all modern browsers
- Gracefully degrades in older browsers
- No polyfills required
- Compatible with Next.js App Router

## Troubleshooting

### Progress bar not showing

1. Check if the `NavigationProgressProvider` is wrapping your app
2. Verify that the link is internal (not external)
3. Ensure the link doesn't have `target="_blank"`

### Progress bar stuck

1. Check for JavaScript errors in the console
2. Verify that the progress interval is being cleared
3. Ensure the route change is being detected

### Performance issues

1. Check if multiple progress intervals are running
2. Verify that event listeners are being cleaned up
3. Ensure the progress bar is not re-rendering unnecessarily

## API Reference

### NavigationProgressState

```tsx
interface NavigationProgressState {
  isNavigating: boolean;
  progress: number;
  startNavigation: () => void;
  completeNavigation: () => void;
}
```

### NavigationProgressProps

```tsx
interface NavigationProgressProps {
  isNavigating: boolean;
  progress: number;
  className?: string;
}
```

## Contributing

To extend the navigation progress system:

1. Add new props to the components
2. Update the hooks with new functionality
3. Add new progress types or behaviors
4. Update the documentation

## License

This navigation progress system is part of the RevoLand project and follows the same licensing terms.
