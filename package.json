{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --hostname 0.0.0.0", "prebuild": "npm run format", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint . --fix --max-warnings=0", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "commit": "better-commits", "branch": "better-branch"}, "dependencies": {"@aws-sdk/client-s3": "^3.774.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@googlemaps/js-api-loader": "^1.16.8", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-three/drei": "^9.120.4", "@react-three/fiber": "^8.17.10", "@react-three/postprocessing": "^2.16.5", "@sinclair/typebox": "^0.34.33", "@studio-freight/lenis": "^1.0.42", "@studio-freight/react-lenis": "^0.0.47", "@tanstack/react-query": "^5.69.0", "@tanstack/react-query-devtools": "^5.69.0", "@tanstack/react-table": "^8.21.2", "@types/google.maps": "^3.58.1", "@vis.gl/react-google-maps": "^1.5.3", "animejs": "^4.0.2", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^4.3.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.15.0", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "i18next-resources-to-backend": "^1.2.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lenis": "^1.1.18", "lucide-react": "^0.465.0", "mongodb": "^6.12.0", "next": "^14.2.6", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.5.1", "react-icons": "^5.4.0", "react-quill": "^2.0.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^6.28.1", "react-syntax-highlighter": "^15.6.1", "react-use-downloader": "^1.2.8", "recharts": "^2.15.1", "sonner": "^2.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.170.0", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.17.10", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "better-commits": "^1.16.1", "eslint": "^8.57.1", "eslint-config-next": "^14.2.6", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "husky": "^9.1.7", "lint-staged": "^15.5.1", "postcss": "^8.4.49", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}