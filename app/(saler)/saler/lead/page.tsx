'use client';

import { SiteHeader } from '@/components/common/siteHeader';
import Image from 'next/image';
import { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { FilterX, RefreshCw } from 'lucide-react';
import { Lead } from '@/lib/api/services/fetchLead';
import { useSellerLeads } from '@/hooks/useLead';
import { leadColumns, OptionItem } from './components/leadColumns';
import { DataTable } from './components/dataTable';

const LEAD_FILTERS_KEY = 'leadFilters';

const useScoreOptions = (): OptionItem[] => {
  return [
    { value: 'HIGH', label: 'Cao', icon: null },
    { value: 'MEDIUM', label: 'Trung bình', icon: null },
    { value: 'LOW', label: 'Thấp', icon: null },
  ];
};

const useColumnTranslations = () => {
  return {
    task: '<PERSON><PERSON><PERSON> việc',
    name: '<PERSON><PERSON><PERSON>',
    email: '<PERSON><PERSON>',
    phone: '<PERSON><PERSON> điện thoại',
    address: 'Địa chỉ',
    source: '<PERSON>uồn',
    score: '<PERSON>iểm',
    assignedTo: 'Gán cho',
    createdAt: 'Ngày tạo',
    all: 'Tất cả',
  };
};

export default function LeadPage() {
  const isResettingFilters = useRef(false);
  const [filters, setFilters] = useState<Partial<Lead>>({});
  const isInitialMount = useRef(true);

  const scoreOptions = useScoreOptions();
  const translations = useColumnTranslations();

  useEffect(() => {
    if (isInitialMount.current) {
      try {
        const savedFilters = localStorage.getItem(LEAD_FILTERS_KEY);
        if (savedFilters) {
          setFilters(JSON.parse(savedFilters));
        }
      } catch (error) {
        console.error('Lỗi khi đọc bộ lọc đã lưu:', error);
        localStorage.removeItem(LEAD_FILTERS_KEY);
      }
      isInitialMount.current = false;
    }
  }, []);

  const { data, isLoading, isError, error, refetch, isFetching } = useSellerLeads();
  const [dataFetched, setDataFetched] = useState(false);
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    if (data?.leads) {
      const newTotalItems = data.leads.length;
      setTotalItems(newTotalItems);
      setDataFetched(true);
    }
  }, [data?.leads]);

  const tableData = useMemo(
    () =>
      data?.leads?.map((lead: Lead) => ({
        id: lead.id,
        name: lead.name,
        email: lead.email,
        phone: lead.phone,
        address: lead.address,
        source: lead.source,
        score: lead.score,
        assignedTo: lead.assignedTo,
      })) || [],
    [data?.leads]
  );

  const hasActiveFilters = useMemo(
    () => Object.keys(filters).some(key => filters[key as keyof Lead] !== undefined),
    [filters]
  );

  useEffect(() => {
    if (isResettingFilters.current) return;
    if (Object.keys(filters).length === 0) {
      localStorage.removeItem(LEAD_FILTERS_KEY);
      return;
    }
    localStorage.setItem(LEAD_FILTERS_KEY, JSON.stringify(filters));
  }, [filters]);

  const resetFilters = useCallback(() => {
    isResettingFilters.current = true;
    localStorage.removeItem(LEAD_FILTERS_KEY);
    setFilters({});
    setTimeout(() => (isResettingFilters.current = false), 500);
  }, []);

  // const handleFilterChange = useCallback((newFilters: Partial<Lead>) => {
  //   setFilters(newFilters);
  // }, []);

  const handleLoginRedirect = () => {
    localStorage.removeItem('authToken');
    window.location.href = '/login';
  };

  const showEmptyState = dataFetched && !isLoading && !isError && tableData.length === 0;
  const showFilterEmptyState = showEmptyState && hasActiveFilters;
  const showNoLeadsEmptyState = showEmptyState && !hasActiveFilters;

  const leadColumnDefs = useMemo(
    () => leadColumns(translations, scoreOptions),
    [translations, scoreOptions]
  );

  return (
    <div className="flex flex-col h-full">
      <SiteHeader title="Khách hàng tiềm năng" />
      <div className="flex-1 overflow-auto scrollbar-hide">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-1 md:gap-6 md:py-2">
            <div className="md:hidden">
              <Image
                src="/hero.png"
                width={1280}
                height={998}
                alt="Bảng điều khiển khách hàng tiềm năng"
                className="block dark:hidden"
                priority
              />
              <Image
                src="/hero.png"
                width={1280}
                height={998}
                alt="Bảng điều khiển khách hàng tiềm năng"
                className="hidden dark:block"
                priority
              />
            </div>
            <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex">
              <div className="flex items-center justify-between space-y-2">
                <div>
                  <h2 className="text-2xl font-bold tracking-tight">Khách hàng tiềm năng</h2>
                  <p className="text-muted-foreground">
                    {isLoading && !dataFetched
                      ? 'Đang tải dữ liệu...'
                      : isError
                        ? 'Lỗi khi tải dữ liệu'
                        : showFilterEmptyState
                          ? 'Không có khách hàng tiềm năng nào phù hợp'
                          : `Quản lý khách hàng tiềm năng ở đây${
                              isFetching ? ` (Đang làm mới)` : ''
                            }`}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => refetch()}
                    disabled={isFetching}
                    className="border border-dashed"
                  >
                    <RefreshCw className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
                  </Button>
                </div>
              </div>
              {isError ? (
                <div className="rounded-lg border border-destructive bg-destructive/10 p-8 text-center">
                  <h3 className="text-lg font-medium">
                    {error?.message?.includes('403')
                      ? 'Quyền truy cập không đủ'
                      : 'Lỗi khi tải dữ liệu'}
                  </h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    {error?.message?.includes('403')
                      ? 'Bạn không có quyền truy cập vào trang này'
                      : error instanceof Error
                        ? error.message
                        : 'Đã xảy ra lỗi không xác định'}
                  </p>
                  <Button variant="outline" className="mt-4" onClick={() => refetch()}>
                    Thử lại
                  </Button>
                  {error?.message?.includes('403') && (
                    <Button variant="link" className="mt-2" onClick={handleLoginRedirect}>
                      Đăng nhập với quyền admin
                    </Button>
                  )}
                </div>
              ) : (
                <DataTable
                  data={tableData}
                  columns={leadColumnDefs}
                  isLoading={isLoading || isFetching}
                  error={isError ? error : null}
                  totalItems={totalItems}
                  noResultsFoundMessage={
                    showNoLeadsEmptyState ? (
                      <div className="flex flex-col items-center justify-center">
                        <h3 className="text-sm font-bold">Không có khách hàng tiềm năng nào</h3>
                        <p className="text-sm text-muted-foreground">
                          Không có khách hàng tiềm năng nào
                        </p>
                      </div>
                    ) : showFilterEmptyState ? (
                      <div className="flex flex-col items-center justify-center">
                        <h3 className="text-sm font-bold">Không có kết quả</h3>
                        <Button
                          className="border border-dashed text-center my-4 bg-transparent text-muted-foreground hover:bg-transparent hover:text-muted-foreground"
                          onClick={resetFilters}
                        >
                          <FilterX className="mx-2" />
                          Xóa bộ lọc
                        </Button>
                      </div>
                    ) : null
                  }
                  scoreOptions={scoreOptions}
                  assignedUsers={[]}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
