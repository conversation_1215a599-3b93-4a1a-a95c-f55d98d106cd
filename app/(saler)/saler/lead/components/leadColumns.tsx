'use client';

import { ColumnDef } from '@tanstack/react-table';
import { DataTableColumnHeaderLead } from './dataTableColumnHeaderLead';
import { Checkbox } from '@radix-ui/react-checkbox';

interface AssignedUser {
  id: string;
  name: string;
}

export interface OptionItem {
  value: string;
  label: string;
  icon: React.ComponentType<{ className?: string }> | null;
}

export interface LeadTableRow {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  source: string;
  score: string;
  assignedTo: AssignedUser[] | undefined; // Allow undefined
}

interface ColumnTranslations {
  task: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  source: string;
  score: string;
  assignedTo: string;
  createdAt: string;
}

export const leadColumns = (
  translations: ColumnTranslations,
  scoreOptions: OptionItem[]
): ColumnDef<LeadTableRow>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={value => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeaderLead column={column} title={translations.name} />,
    cell: ({ row }) => <div>{row.getValue('name') as string}</div>,
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeaderLead column={column} title={translations.email} />
    ),
    cell: ({ row }) => <div>{row.getValue('email') as string}</div>,
  },
  {
    accessorKey: 'phone',
    header: ({ column }) => (
      <DataTableColumnHeaderLead column={column} title={translations.phone} />
    ),
    cell: ({ row }) => <div>{row.getValue('phone') as string}</div>,
  },
  {
    accessorKey: 'address',
    header: ({ column }) => (
      <DataTableColumnHeaderLead column={column} title={translations.address} />
    ),
    cell: ({ row }) => <div>{row.getValue('address') as string}</div>,
  },
  {
    accessorKey: 'score',
    header: ({ column }) => (
      <DataTableColumnHeaderLead column={column} title={translations.score} />
    ),
    cell: ({ row }) => {
      const score = row.getValue('score') as string;
      const option = scoreOptions.find(opt => opt.value === score);
      return <div>{option?.label || score}</div>;
    },
    filterFn: (row, id, filterValue) => !filterValue || row.getValue(id) === filterValue,
  },
];
