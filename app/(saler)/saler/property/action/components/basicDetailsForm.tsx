import React from 'react';
import { Building2, Info } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { PropertyType, PropertyStatus, TransactionType } from '@/lib/api/services/fetchProperty';

interface FormData {
  title: string;
  name: string;
  description: string;
  transactionType: TransactionType;
  type: PropertyType;
  status: PropertyStatus;
  adminNote: string | null;
  code: string;
  yearBuilt: number | null;
}

interface BasicDetailsFormProps {
  formData: FormData;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (id: string, value: string) => void;
  handleNext: () => void;
}

const BasicDetailsForm: React.FC<BasicDetailsFormProps> = ({
  formData,
  handleChange,
  handleSelectChange,
  handleNext,
}) => {
  return (
    <div className="max-w-4xl mx-auto space-y-6 md:space-y-8">
      {/* Header Section */}
      <div className="space-y-3 px-4 md:px-0">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <Building2 className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-xl md:text-2xl font-semibold tracking-tight">
              Thông tin bất động sản
            </h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Nhập các thông tin cơ bản của bất động sản
            </p>
          </div>
        </div>
      </div>

      {/* Basic Information Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4 md:pb-6">
          <CardTitle className="text-base md:text-lg font-medium">Thông tin bất động sản</CardTitle>
          <CardDescription className="text-sm">
            Nhập các thông tin cơ bản của bất động sản
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 md:space-y-6 lg:space-y-8">
          {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            <div className="space-y-3">
              <Label htmlFor="code" className="text-sm font-medium">
                Mã căn hộ
              </Label>
              <Input
                id="code"
                placeholder="Nhập mã căn hộ"
                value={formData.code}
                onChange={handleChange}
                className="h-11"
                required
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="name" className="text-sm font-medium">
                Tên bất động sản
              </Label>
              <Input
                id="name"
                placeholder="Nhập tên bất động sản"
                value={formData.name}
                onChange={handleChange}
                className="h-11"
                required
              />
            </div>
          </div> */}

          {/* Transaction Type & Property Type Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Label htmlFor="transactionType" className="text-sm font-medium">
                  Loại giao dịch
                </Label>
                <span className="text-destructive text-sm">*</span>
              </div>
              <Select
                value={formData.transactionType}
                onValueChange={value => handleSelectChange('transactionType', value)}
              >
                <SelectTrigger id="transactionType" className="h-11">
                  <SelectValue placeholder="Chọn loại giao dịch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={TransactionType.FOR_RENT}>Cho thuê</SelectItem>
                  <SelectItem value={TransactionType.FOR_SALE}>Bán</SelectItem>
                  <SelectItem value={TransactionType.PROJECT}>Dự án</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Label htmlFor="type" className="text-sm font-medium">
                  Loại bất động sản
                </Label>
                <span className="text-destructive text-sm">*</span>
              </div>
              <Select
                value={formData.type}
                onValueChange={value => handleSelectChange('type', value)}
              >
                <SelectTrigger id="type" className="h-11">
                  <SelectValue placeholder="Chọn loại bất động sản" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={PropertyType.APARTMENT}>Căn hộ chung cư</SelectItem>
                  <SelectItem value={PropertyType.HOUSE}>Nhà</SelectItem>
                  <SelectItem value={PropertyType.LAND_PLOT}>Đất nền</SelectItem>
                  <SelectItem value={PropertyType.TOWNHOUSE}>Nhà phố</SelectItem>
                  <SelectItem value={PropertyType.SHOP_HOUSE}>Shop house</SelectItem>

                  <SelectItem value={PropertyType.VILLA}>Biệt thự</SelectItem>
                  <SelectItem value={PropertyType.SOCIAL_HOUSING}>Nhà ở xã hội</SelectItem>
                  <SelectItem value={PropertyType.COMMERCIAL_TOWNHOUSE}>
                    Nhà phố thương mại
                  </SelectItem>
                  <SelectItem value={PropertyType.MINI_SERVICE_APARTMENT}>Căn hộ mini</SelectItem>
                  <SelectItem value={PropertyType.MOTEL}>Nhà nghỉ</SelectItem>
                  <SelectItem value={PropertyType.AIRBNB}>Airbnb</SelectItem>

                  <SelectItem value={PropertyType.PROJECT_LAND}>Đất dự án</SelectItem>
                  <SelectItem value={PropertyType.OFFICE}>Văn phòng</SelectItem>
                  <SelectItem value={PropertyType.WAREHOUSE}>Nhà kho</SelectItem>
                  <SelectItem value={PropertyType.FACTORY}>Xưởng</SelectItem>
                  <SelectItem value={PropertyType.INDUSTRIAL}>Nhà máy</SelectItem>
                  <SelectItem value={PropertyType.HOTEL}>Khách sạn</SelectItem>

                  <SelectItem value={PropertyType.NEW_URBAN_AREA}>Khu đô thị mới</SelectItem>
                  <SelectItem value={PropertyType.ECO_RESORT}>Khu resort</SelectItem>
                  <SelectItem value={PropertyType.OTHER}>Khác</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <Separator />
          {/* Title, Property Name & Code Row */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Label htmlFor="title" className="text-sm font-medium">
                Tiêu đề
              </Label>
              <span className="text-destructive text-sm">*</span>
            </div>
            <Input
              id="title"
              placeholder="Nhập tiêu đề"
              value={formData.title}
              onChange={handleChange}
              className="h-11"
              required
            />
          </div>
          <Separator />

          {/* Description Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Mô tả bất động sản
              </Label>
              <span className="text-destructive text-sm">*</span>
            </div>
            <Textarea
              id="description"
              placeholder="Nhập mô tả bất động sản"
              className="min-h-32 resize-none"
              value={formData.description}
              onChange={handleChange}
            />
          </div>
        </CardContent>
      </Card>

      {/* Help Card */}
      <Card className="border-dashed bg-muted/30">
        <CardContent className="p-4 md:p-6">
          <div className="flex items-start gap-3">
            <Info className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium">Cần trợ giúp về thông tin bất động sản?</p>
              <p className="text-xs text-muted-foreground">
                Đảm bảo cung cấp thông tin chính xác vì nó sẽ được hiển thị cho khách hàng tiềm
                năng. Tất cả các trường bắt buộc phải được hoàn thành trước khi tiếp tục đến bước
                tiếp theo.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end pt-4 md:pt-6 border-t">
        <Button onClick={handleNext} size="lg" className="px-6 md:px-8">
          Tiếp theo
        </Button>
      </div>
    </div>
  );
};

export default BasicDetailsForm;
