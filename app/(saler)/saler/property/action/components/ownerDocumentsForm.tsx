import React, { useState, useCallback } from 'react';
import {
  X,
  FileText,
  Image as ImageIcon,
  Video,
  Plus,
  Eye,
  Check,
  Wind,
  Trees,
  Building,
  Car,
  Shield,
  Power,
  PlayCircle,
  Dumbbell,
  Waves,
  GripVertical,
  Info,
} from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Amenity, PropertyDetailRequest, PropertyType } from '@/lib/api/services/fetchProperty';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Checkbox } from '@/components/ui/checkbox';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface OwnerDocumentsFormProps {
  formData: Partial<PropertyDetailRequest>;
  handleCheckboxChange: (id: string, checked: boolean) => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  imageInputRef: React.RefObject<HTMLInputElement>;
  documentInputRef: React.RefObject<HTMLInputElement>;
  floorPlanInputRef: React.RefObject<HTMLInputElement>;
  videoInputRef: React.RefObject<HTMLInputElement>;
  imageFiles: File[];
  documentFiles: File[];
  floorPlanFiles: File[];
  videoFiles: File[];
  uploadedImageUrls: string[];
  uploadedDocumentUrls: string[];
  uploadedFloorPlanUrls: string[];
  uploadedVideoUrls: string[];
  previewImage: string | null;
  handleImageClick: () => void;
  handleDocumentClick: () => void;
  handleFloorPlanClick: () => void;
  handleVideoClick: () => void;
  handleImageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleDocumentChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleFloorPlanChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleVideoChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleImagePreview: (file: File) => void;
  setPreviewImage: (preview: string | null) => void;
  handleRemoveImage: (file: File) => void;
  handleRemoveDocument: (file: File) => void;
  handleRemoveFloorPlan: (file: File) => void;
  handleRemoveVideo: (file: File) => void;
  handleReorderAllImages: (startIndex: number, endIndex: number) => void;
  handleReorderAllFloorPlans: (startIndex: number, endIndex: number) => void;
  isPending: boolean;
  handleSubmit: () => void;
  isEditMode?: boolean;
  existingImages?: Array<{ url: string; name: string; isExisting: boolean }>;
  existingDocuments?: Array<{ url: string; name: string; isExisting: boolean }>;
  existingFloorPlans?: Array<{ url: string; name: string; isExisting: boolean }>;
  existingVideos?: Array<{ url: string; name: string; isExisting: boolean }>;
  handleRemoveExistingImage?: (url: string) => void;
  handleRemoveExistingDocument?: (url: string) => void;
  handleRemoveExistingFloorPlan?: (url: string) => void;
  handleRemoveExistingVideo?: (url: string) => void;
  onUploadProgress?: (progress: number) => void;
}

// Add More Card Component
const AddMoreCard = ({
  onClick,
  title,
  description,
  icon: Icon,
}: {
  onClick: () => void;
  title: string;
  description: string;
  icon: React.ElementType;
}) => {
  return (
    <div
      onClick={onClick}
      className="aspect-square relative border-2 border-dashed border-muted-foreground/25 hover:border-primary/50 rounded-lg cursor-pointer bg-muted/10 hover:bg-muted/25 transition-all duration-200 group flex flex-col items-center justify-center p-4"
    >
      <Icon className="h-8 w-8 text-muted-foreground group-hover:text-primary transition-colors mb-2" />
      <p className="text-sm font-medium text-center mb-1">{title}</p>
      <p className="text-xs text-muted-foreground text-center">{description}</p>
    </div>
  );
};

// Video Preview Dialog Component
const VideoPreviewDialog = ({
  isOpen,
  onClose,
  videoUrl,
  videoName,
}: {
  isOpen: boolean;
  onClose: () => void;
  videoUrl: string;
  videoName: string;
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-[90vw] h-[60vh] p-0">
        <DialogHeader className="p-4 pb-2">
          <DialogTitle className="text-lg font-medium truncate">{videoName}</DialogTitle>
        </DialogHeader>
        <div className="flex-1 p-4 pt-0">
          <div className="w-full h-full bg-black rounded-lg overflow-hidden">
            <video
              src={videoUrl}
              className="w-full h-full object-contain"
              controls
              autoPlay
              preload="metadata"
            >
              Your browser does not support the video tag.
            </video>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// File Grid Item Component
const FileGridItem = ({
  file,
  onRemove,
  isExisting = false,
  url,
  type = 'image',
  isDragging = false,
  index,
  showOrder = false,
  isUploading = false,
  onPreview,
}: {
  file?: File;
  onRemove: () => void;
  isExisting?: boolean;
  url?: string;
  type?: 'image' | 'document' | 'video';
  isDragging?: boolean;
  index?: number;
  showOrder?: boolean;
  isUploading?: boolean;
  onPreview?: () => void;
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const mediaUrl = file ? URL.createObjectURL(file) : url;
  const fileName = file?.name || url?.split('/').pop() || 'Unknown';

  if (type === 'document') {
    const fileSize = file ? (file.size / (1024 * 1024)).toFixed(1) : null;
    const fileType = file?.type || fileName.split('.').pop()?.toUpperCase();

    return (
      <div
        className={cn(
          'group relative border rounded-lg bg-card hover:shadow-md transition-all duration-200 p-4',
          isDragging && 'shadow-lg rotate-2 scale-105',
          isUploading && 'bg-primary/5 border-primary/30'
        )}
      >
        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-primary/10 rounded-lg">
            <div className="flex items-center gap-2 text-primary">
              <div className="w-4 h-4 border-2 border-primary/50 border-t-primary animate-spin rounded-full" />
              <span className="text-sm font-medium">Đang tải...</span>
            </div>
          </div>
        )}
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
              <FileText className="h-6 w-6 text-primary" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="font-medium truncate text-sm" title={fileName}>
              {fileName}
            </p>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              {fileType && (
                <Badge variant="outline" className="text-xs">
                  {fileType}
                </Badge>
              )}
              {fileSize && <span>{fileSize} MB</span>}
              {isExisting && (
                <Badge variant="secondary" className="text-xs">
                  Đã tồn tại
                </Badge>
              )}
            </div>
          </div>
        </div>

        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            size="icon"
            variant="destructive"
            className="h-6 w-6"
            onClick={e => {
              e.stopPropagation();
              onRemove();
            }}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  }

  if (type === 'video') {
    return (
      <div
        className={cn(
          'group relative aspect-video overflow-hidden rounded-lg border bg-card hover:shadow-md transition-all duration-200',
          isDragging && 'shadow-lg rotate-2 scale-105',
          isUploading && 'bg-primary/5 border-primary/30'
        )}
      >
        {isUploading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-primary/10 rounded-lg">
            <div className="flex items-center gap-2 text-primary">
              <div className="w-6 h-6 border-2 border-primary/50 border-t-primary animate-spin rounded-full" />
              <span className="text-sm font-medium">Đang tải...</span>
            </div>
          </div>
        )}
        {mediaUrl && (
          <video
            src={mediaUrl}
            className="w-full h-full object-cover"
            controls={false}
            muted
            preload="metadata"
            onLoadedData={() => setIsLoading(false)}
          />
        )}

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted">
            <div className="w-6 h-6 border-2 border-primary/50 border-t-primary animate-spin rounded-full" />
          </div>
        )}

        {/* Drag Handle */}
        <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-black/70 rounded-md p-1 cursor-grab">
            <GripVertical className="h-4 w-4 text-white" />
          </div>
        </div>

        {/* Order Badge */}
        {showOrder && typeof index === 'number' && (
          <Badge className="absolute top-2 right-12 bg-primary text-white">
            {index === 0 ? '1' : `${index + 1}`}
          </Badge>
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="flex gap-2">
            <Button
              size="icon"
              variant="secondary"
              className="h-8 w-8 bg-white/90 hover:bg-white"
              onClick={e => {
                e.stopPropagation();
                onPreview?.();
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              size="icon"
              variant="destructive"
              className="h-8 w-8"
              onClick={e => {
                e.stopPropagation();
                onRemove();
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* File type badge */}
        {isExisting && (
          <Badge className="absolute bottom-2 left-2 text-xs" variant="secondary">
            Đã tồn tại
          </Badge>
        )}

        {/* Video icon overlay */}
        <div className="absolute bottom-2 right-2">
          <div className="bg-black/70 rounded-md p-1">
            <Video className="h-4 w-4 text-white" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'group relative aspect-square overflow-hidden rounded-lg border bg-card hover:shadow-md transition-all duration-200',
        isDragging && 'shadow-lg rotate-2 scale-105',
        isUploading && 'bg-primary/5 border-primary/30'
      )}
    >
      {isUploading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-primary/10 rounded-lg">
          <div className="flex items-center gap-2 text-primary">
            <div className="w-6 h-6 border-2 border-primary/50 border-t-primary animate-spin rounded-full" />
            <span className="text-sm font-medium">Đang tải...</span>
          </div>
        </div>
      )}
      {mediaUrl && (
        <Image
          src={mediaUrl}
          alt={fileName}
          className={cn('object-cover transition-all duration-300', isLoading && 'blur-sm')}
          fill
          sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
          onLoad={() => setIsLoading(false)}
        />
      )}

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <div className="w-6 h-6 border-2 border-primary/50 border-t-primary animate-spin rounded-full" />
        </div>
      )}

      {/* Drag Handle */}
      <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="bg-black/70 rounded-md p-1 cursor-grab">
          <GripVertical className="h-4 w-4 text-white" />
        </div>
      </div>

      {/* Order Badge */}
      {showOrder && typeof index === 'number' && (
        <Badge className="absolute top-2 right-12 bg-primary text-white">
          {index === 0 ? '1' : `${index + 1}`}
        </Badge>
      )}

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
        <div className="flex gap-2">
          <Button
            size="icon"
            variant="secondary"
            className="h-8 w-8 bg-white/90 hover:bg-white"
            onClick={e => {
              e.stopPropagation();
              // Handle preview
            }}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="icon"
            variant="destructive"
            className="h-8 w-8"
            onClick={e => {
              e.stopPropagation();
              onRemove();
            }}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* File type badge */}
      {isExisting && (
        <Badge className="absolute bottom-2 left-2 text-xs" variant="secondary">
          Đã tồn tại
        </Badge>
      )}
    </div>
  );
};

const FileDropZone = ({
  onDrop,
  children,
  className,
}: {
  onDrop: (files: FileList) => void;
  children: React.ReactNode;
  accept?: string;
  multiple?: boolean;
  className?: string;
}) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      onDrop(files);
    }
  };

  return (
    <div
      className={cn(
        'relative transition-all duration-200',
        isDragging && 'bg-primary/5 rounded-lg',
        className
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {isDragging && (
        <div className="absolute inset-0 rounded-lg flex items-center justify-center z-10">
          <div className="bg-white/90 rounded-lg p-4 shadow-lg">
            <p className="text-sm font-medium">Thả tệp vào đây</p>
          </div>
        </div>
      )}
      {children}
    </div>
  );
};

export default function OwnerDocumentsForm({
  formData,
  handleCheckboxChange,
  imageInputRef,
  documentInputRef,
  floorPlanInputRef,
  videoInputRef,
  imageFiles,
  documentFiles,
  floorPlanFiles,
  videoFiles,
  uploadedImageUrls,
  uploadedDocumentUrls,
  uploadedFloorPlanUrls,
  uploadedVideoUrls,
  handleImageClick,
  handleDocumentClick,
  handleFloorPlanClick,
  handleVideoClick,
  handleImageChange,
  handleDocumentChange,
  handleFloorPlanChange,
  handleVideoChange,
  handleRemoveDocument,
  handleRemoveImage,
  handleRemoveFloorPlan,
  handleRemoveVideo,
  handleReorderAllImages,
  handleReorderAllFloorPlans,
  isPending,
  handleSubmit,
  isEditMode = false,
  existingImages = [],
  existingDocuments = [],
  existingFloorPlans = [],
  existingVideos = [],
  handleRemoveExistingImage = () => {},
  handleRemoveExistingDocument = () => {},
  handleRemoveExistingFloorPlan = () => {},
  handleRemoveExistingVideo = () => {},
  onUploadProgress = () => {},
}: OwnerDocumentsFormProps) {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([]);
  const [currentUploadStep, setCurrentUploadStep] = useState('');

  // Video preview state
  const [videoPreview, setVideoPreview] = useState<{
    isOpen: boolean;
    url: string;
    name: string;
  }>({
    isOpen: false,
    url: '',
    name: '',
  });

  // Function to get amenities based on property type
  const getAmenitiesByPropertyType = useCallback(() => {
    const propertyType = formData.type;

    // Land plot and special properties
    if (
      propertyType === PropertyType.LAND_PLOT ||
      propertyType === PropertyType.PROJECT_LAND ||
      propertyType === PropertyType.NEW_URBAN_AREA ||
      propertyType === PropertyType.ECO_RESORT
    ) {
      return [
        { id: 'airConditioning', label: 'Góc 2 mặt tiền', icon: Wind },
        { id: 'balcony', label: 'Đất mặt tiền kinh doanh', icon: Building },
        { id: 'swimmingPool', label: 'Đất phân lô', icon: Waves },
        { id: 'garden', label: 'Đất khu dân cư', icon: Trees },
        { id: 'gym', label: 'Đất tái định cư', icon: Dumbbell },
        { id: 'playground', label: 'Đất thổ cư', icon: PlayCircle },
        { id: 'backupGenerator', label: 'Đất vườn', icon: Power },
        { id: 'parking', label: 'Đất nông nghiệp', icon: Car },
        { id: 'securitySystem', label: 'Đất công nghiệp', icon: Shield },
      ];
    }

    // Commercial properties (Office, Warehouse, Factory, Industrial)
    if (
      propertyType === PropertyType.OFFICE ||
      propertyType === PropertyType.WAREHOUSE ||
      propertyType === PropertyType.FACTORY ||
      propertyType === PropertyType.INDUSTRIAL
    ) {
      return [
        { id: 'airConditioning', label: 'Hệ thống điều hòa trung tâm', icon: Wind },
        { id: 'balcony', label: 'Hệ thống thông gió', icon: Building },
        { id: 'swimmingPool', label: 'Hệ thống phòng cháy chữa cháy', icon: Waves },
        { id: 'garden', label: 'Khu vực bảo trì', icon: Trees },
        { id: 'gym', label: 'Phòng họp', icon: Dumbbell },
        { id: 'playground', label: 'Khu vực tiếp khách', icon: PlayCircle },
        { id: 'backupGenerator', label: 'Máy phát điện dự phòng', icon: Power },
        { id: 'parking', label: 'Bãi đỗ xe tải', icon: Car },
        { id: 'securitySystem', label: 'Hệ thống an ninh 24/7', icon: Shield },
      ];
    }

    // Hotel and Airbnb properties
    if (propertyType === PropertyType.HOTEL || propertyType === PropertyType.AIRBNB) {
      return [
        { id: 'airConditioning', label: 'Điều hòa không khí', icon: Wind },
        { id: 'balcony', label: 'Ban công/Loggia', icon: Building },
        { id: 'swimmingPool', label: 'Hồ bơi', icon: Waves },
        { id: 'garden', label: 'Khu vườn', icon: Trees },
        { id: 'gym', label: 'Phòng tập gym', icon: Dumbbell },
        { id: 'playground', label: 'Khu vui chơi trẻ em', icon: PlayCircle },
        { id: 'backupGenerator', label: 'Máy phát điện dự phòng', icon: Power },
        { id: 'parking', label: 'Bãi đỗ xe', icon: Car },
        { id: 'securitySystem', label: 'An ninh 24/7', icon: Shield },
      ];
    }

    // Shop house and commercial townhouse
    if (
      propertyType === PropertyType.SHOP_HOUSE ||
      propertyType === PropertyType.COMMERCIAL_TOWNHOUSE
    ) {
      return [
        { id: 'airConditioning', label: 'Điều hòa không khí', icon: Wind },
        { id: 'balcony', label: 'Mặt tiền kinh doanh', icon: Building },
        { id: 'swimmingPool', label: 'Khu vực trưng bày', icon: Waves },
        { id: 'garden', label: 'Khu vực lưu trữ', icon: Trees },
        { id: 'gym', label: 'Phòng làm việc', icon: Dumbbell },
        { id: 'playground', label: 'Khu vực tiếp khách', icon: PlayCircle },
        { id: 'backupGenerator', label: 'Máy phát điện dự phòng', icon: Power },
        { id: 'parking', label: 'Bãi đỗ xe', icon: Car },
        { id: 'securitySystem', label: 'Hệ thống an ninh', icon: Shield },
      ];
    }

    // Default amenities for residential properties
    return [
      { id: 'airConditioning', label: 'Điều hòa không khí', icon: Wind },
      { id: 'balcony', label: 'Ban công', icon: Building },
      { id: 'swimmingPool', label: 'Hồ bơi', icon: Waves },
      { id: 'garden', label: 'Khu vườn', icon: Trees },
      { id: 'gym', label: 'Phòng tập gym', icon: Dumbbell },
      { id: 'playground', label: 'Khu vui chơi', icon: PlayCircle },
      { id: 'backupGenerator', label: 'Máy phát điện dự phòng', icon: Power },
      { id: 'parking', label: 'Bãi đỗ xe', icon: Car },
      { id: 'securitySystem', label: 'An ninh 24/7', icon: Shield },
    ];
  }, [formData.type]);

  // Get amenities list based on property type
  const amenitiesList = getAmenitiesByPropertyType();

  // Check if all amenities are selected
  const areAllAmenitiesSelected = useCallback(() => {
    if (!formData.amenities) return false;
    return amenitiesList.every(
      amenityId => formData.amenities?.[amenityId.id as keyof Amenity] === true
    );
  }, [formData.amenities, amenitiesList]);

  // Check if some amenities are selected (for indeterminate state)
  const areSomeAmenitiesSelected = useCallback(() => {
    if (!formData.amenities) return false;
    const selectedCount = amenitiesList.filter(
      amenityId => formData.amenities?.[amenityId.id as keyof Amenity] === true
    ).length;
    return selectedCount > 0 && selectedCount < amenitiesList.length;
  }, [formData.amenities, amenitiesList]);

  const setUploadProgressSafe = useCallback(
    (progress: number) => {
      const safeProgress = Math.max(0, Math.min(100, Math.round(progress)));
      setUploadProgress(safeProgress);
      onUploadProgress(safeProgress);
    },
    [onUploadProgress]
  );

  const totalFiles =
    existingImages.length +
    existingDocuments.length +
    existingFloorPlans.length +
    existingVideos.length +
    uploadedImageUrls.length +
    uploadedDocumentUrls.length +
    uploadedFloorPlanUrls.length +
    uploadedVideoUrls.length;

  const handleImageDragEnd = (result: DropResult) => {
    if (!result.destination) return;
    handleReorderAllImages(result.source.index, result.destination.index);
  };

  const handleFloorPlanDragEnd = (result: DropResult) => {
    if (!result.destination) return;
    handleReorderAllFloorPlans(result.source.index, result.destination.index);
  };

  const handleEnhancedImageChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleImageChange(e);
    },
    [handleImageChange]
  );

  const handleEnhancedDocumentChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleDocumentChange(e);
    },
    [handleDocumentChange]
  );

  const handleEnhancedFloorPlanChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleFloorPlanChange(e);
    },
    [handleFloorPlanChange]
  );

  const handleEnhancedVideoChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      handleVideoChange(e);
    },
    [handleVideoChange]
  );

  const handleEnhancedSubmit = useCallback(async () => {
    if (totalFiles === 0) return;

    setCurrentUploadStep('Đang chuẩn bị gửi dữ liệu...');
    setUploadProgressSafe(10);

    // Set uploading files
    const allFiles = [
      ...imageFiles.map(f => f.name),
      ...documentFiles.map(f => f.name),
      ...floorPlanFiles.map(f => f.name),
      ...videoFiles.map(f => f.name),
    ];
    setUploadingFiles(allFiles);

    await new Promise(resolve => setTimeout(resolve, 500));
    setUploadProgressSafe(30);
    setCurrentUploadStep('Đang xử lý dữ liệu...');

    await new Promise(resolve => setTimeout(resolve, 800));
    setUploadProgressSafe(60);
    setCurrentUploadStep('Đang gửi thông tin bất động sản...');

    await new Promise(resolve => setTimeout(resolve, 1000));
    setUploadProgressSafe(90);
    setCurrentUploadStep('Đang hoàn tất...');

    await handleSubmit();

    setUploadProgressSafe(100);
    setCurrentUploadStep('Gửi thành công!');
    setUploadingFiles([]); // Clear uploading files

    setTimeout(() => {
      setUploadProgress(0);
      setCurrentUploadStep('');
    }, 2000);
  }, [
    totalFiles,
    handleSubmit,
    setUploadProgressSafe,
    imageFiles,
    documentFiles,
    floorPlanFiles,
    videoFiles,
  ]);

  const handleImageDrop = useCallback(
    (files: FileList) => {
      const dataTransfer = new DataTransfer();
      Array.from(files).forEach(file => dataTransfer.items.add(file));
      const event = {
        target: { files: dataTransfer.files },
      } as React.ChangeEvent<HTMLInputElement>;
      handleImageChange(event);
    },
    [handleImageChange]
  );

  const handleFloorPlanDrop = useCallback(
    (files: FileList) => {
      const dataTransfer = new DataTransfer();
      Array.from(files).forEach(file => dataTransfer.items.add(file));
      const event = {
        target: { files: dataTransfer.files },
      } as React.ChangeEvent<HTMLInputElement>;
      handleFloorPlanChange(event);
    },
    [handleFloorPlanChange]
  );

  const handleDocumentDrop = useCallback(
    (files: FileList) => {
      const dataTransfer = new DataTransfer();
      Array.from(files).forEach(file => dataTransfer.items.add(file));
      const event = {
        target: { files: dataTransfer.files },
      } as React.ChangeEvent<HTMLInputElement>;
      handleDocumentChange(event);
    },
    [handleDocumentChange]
  );

  const handleVideoDrop = useCallback(
    (files: FileList) => {
      const dataTransfer = new DataTransfer();
      Array.from(files).forEach(file => dataTransfer.items.add(file));
      const event = {
        target: { files: dataTransfer.files },
      } as React.ChangeEvent<HTMLInputElement>;
      handleVideoChange(event);
    },
    [handleVideoChange]
  );

  const handleVideoPreview = useCallback((url: string, name: string) => {
    setVideoPreview({
      isOpen: true,
      url,
      name,
    });
  }, []);

  const closeVideoPreview = useCallback(() => {
    setVideoPreview(prev => ({ ...prev, isOpen: false }));
  }, []);

  return (
    <div className="max-w-4xl mx-auto space-y-6 md:space-y-8">
      {/* Video Preview Dialog */}
      <VideoPreviewDialog
        isOpen={videoPreview.isOpen}
        onClose={closeVideoPreview}
        videoUrl={videoPreview.url}
        videoName={videoPreview.name}
      />

      <div className="space-y-3 px-4 md:px-0">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary/10">
            <ImageIcon className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-xl md:text-2xl font-semibold tracking-tight">
              Hình ảnh và tài liệu
            </h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Tải lên hình ảnh, video và tài liệu pháp lý
            </p>
          </div>
        </div>

        {(isPending || uploadProgress > 0) && (
          <Card className="border-0 shadow-sm">
            <CardContent className="pt-4 md:pt-6">
              <div className="space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="font-medium">{currentUploadStep || 'Đang tải lên tệp...'}</span>
                  <Badge variant="secondary" className="text-xs">
                    {Math.max(0, Math.min(100, uploadProgress))}%
                  </Badge>
                </div>
                <Progress value={Math.max(0, Math.min(100, uploadProgress))} className="h-2" />
                {uploadingFiles.length > 0 && (
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">
                      Đang xử lý {uploadingFiles.length} tệp:
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {uploadingFiles.slice(0, 3).map((fileName, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {fileName.length > 20 ? `${fileName.substring(0, 20)}...` : fileName}
                        </Badge>
                      ))}
                      {uploadingFiles.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{uploadingFiles.length - 3} khác
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4 md:pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Hình ảnh bất động sản
            <span className="text-destructive text-sm">*</span>
            {uploadedImageUrls.length + existingImages.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {uploadedImageUrls.length + existingImages.length}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Tải lên hình ảnh chất lượng cao để thu hút khách hàng tiềm năng
          </CardDescription>
          {uploadedImageUrls.length + existingImages.length > 0 && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <GripVertical className="h-4 w-4" />
              <span>Kéo thả để thay đổi thứ tự hiển thị</span>
            </div>
          )}
        </CardHeader>
        <CardContent className="space-y-4 md:space-y-6 lg:space-y-8">
          <FileDropZone onDrop={handleImageDrop} accept="image/*" multiple>
            <DragDropContext onDragEnd={handleImageDragEnd}>
              <Droppable droppableId="all-images" direction="horizontal">
                {(provided, snapshot) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className={cn(
                      'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4',
                      snapshot.isDraggingOver && 'bg-primary/5 rounded-lg p-2'
                    )}
                  >
                    {existingImages.map((image, index) => (
                      <Draggable
                        key={`existing-img-${image.url}`}
                        draggableId={`existing-img-${image.url}`}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            style={provided.draggableProps.style}
                          >
                            <FileGridItem
                              url={image.url}
                              onRemove={() => handleRemoveExistingImage(image.url)}
                              isExisting
                              type="image"
                              isDragging={snapshot.isDragging}
                              index={index}
                              showOrder={true}
                            />
                          </div>
                        )}
                      </Draggable>
                    ))}

                    {/* Uploaded Images */}
                    {uploadedImageUrls.map((url, index) => {
                      const globalIndex = existingImages.length + index;
                      return (
                        <Draggable
                          key={`uploaded-img-${url}`}
                          draggableId={`uploaded-img-${url}`}
                          index={globalIndex}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={provided.draggableProps.style}
                            >
                              <FileGridItem
                                url={url}
                                onRemove={() => {
                                  const fileToRemove = imageFiles[index];
                                  if (fileToRemove) {
                                    handleRemoveImage(fileToRemove);
                                  }
                                }}
                                type="image"
                                isDragging={snapshot.isDragging}
                                index={globalIndex}
                                showOrder={true}
                              />
                            </div>
                          )}
                        </Draggable>
                      );
                    })}

                    <AddMoreCard
                      onClick={handleImageClick}
                      title="Thêm hình ảnh"
                      description="Chọn ảnh chất lượng cao"
                      icon={Plus}
                    />

                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </FileDropZone>

          <input
            type="file"
            ref={imageInputRef}
            onChange={handleEnhancedImageChange}
            accept="image/*"
            multiple
            className="hidden"
          />
        </CardContent>
      </Card>

      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4 md:pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Building className="h-5 w-5" />
            Bản vẽ thiết kế
            {uploadedFloorPlanUrls.length + existingFloorPlans.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {uploadedFloorPlanUrls.length + existingFloorPlans.length}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Tải lên bản vẽ mặt bằng và thiết kế kiến trúc của bất động sản
          </CardDescription>
          {uploadedFloorPlanUrls.length + existingFloorPlans.length > 0 && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <GripVertical className="h-4 w-4" />
              <span>Kéo thả để thay đổi thứ tự hiển thị</span>
            </div>
          )}
        </CardHeader>
        <CardContent className="space-y-4 md:space-y-6 lg:space-y-8">
          <FileDropZone onDrop={handleFloorPlanDrop} accept="image/*" multiple>
            <DragDropContext onDragEnd={handleFloorPlanDragEnd}>
              <Droppable droppableId="all-floor-plans" direction="horizontal">
                {(provided, snapshot) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className={cn(
                      'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4',
                      snapshot.isDraggingOver && 'bg-primary/5 rounded-lg p-2'
                    )}
                  >
                    {existingFloorPlans.map((floorPlan, index) => (
                      <Draggable
                        key={`existing-floor-${floorPlan.url}`}
                        draggableId={`existing-floor-${floorPlan.url}`}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            style={provided.draggableProps.style}
                          >
                            <FileGridItem
                              url={floorPlan.url}
                              onRemove={() => handleRemoveExistingFloorPlan(floorPlan.url)}
                              isExisting
                              type="image"
                              isDragging={snapshot.isDragging}
                              index={index}
                              showOrder={true}
                            />
                          </div>
                        )}
                      </Draggable>
                    ))}

                    {uploadedFloorPlanUrls.map((url, index) => {
                      const globalIndex = existingFloorPlans.length + index;
                      return (
                        <Draggable
                          key={`uploaded-floor-${url}`}
                          draggableId={`uploaded-floor-${url}`}
                          index={globalIndex}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={provided.draggableProps.style}
                            >
                              <FileGridItem
                                url={url}
                                onRemove={() => {
                                  const fileToRemove = floorPlanFiles[index];
                                  if (fileToRemove) {
                                    handleRemoveFloorPlan(fileToRemove);
                                  }
                                }}
                                type="image"
                                isDragging={snapshot.isDragging}
                                index={globalIndex}
                                showOrder={true}
                              />
                            </div>
                          )}
                        </Draggable>
                      );
                    })}

                    <AddMoreCard
                      onClick={handleFloorPlanClick}
                      title="Thêm bản vẽ"
                      description="Tải lên bản vẽ thiết kế"
                      icon={Plus}
                    />

                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </FileDropZone>

          <input
            type="file"
            ref={floorPlanInputRef}
            onChange={handleEnhancedFloorPlanChange}
            accept="image/*"
            multiple
            className="hidden"
          />
        </CardContent>
      </Card>

      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4 md:pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Video className="h-5 w-5" />
            Video giới thiệu
            {uploadedVideoUrls.length + existingVideos.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {uploadedVideoUrls.length + existingVideos.length}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Tải lên video giới thiệu bất động sản để tăng sức hấp dẫn
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 md:space-y-6 lg:space-y-8">
          <FileDropZone onDrop={handleVideoDrop} accept="video/*" multiple>
            <div className="w-fit">
              {existingVideos.length > 0 || uploadedVideoUrls.length > 0 ? (
                <>
                  {existingVideos.map((video, index) => (
                    <FileGridItem
                      key={`existing-video-${index}`}
                      url={video.url}
                      onRemove={() => handleRemoveExistingVideo(video.url)}
                      onPreview={() => handleVideoPreview(video.url, video.name)}
                      isExisting
                      type="video"
                    />
                  ))}

                  {uploadedVideoUrls.map((url, index) => {
                    const fileToPreview = videoFiles[index];
                    return (
                      <FileGridItem
                        key={`uploaded-video-${index}`}
                        url={url}
                        onRemove={() => {
                          const fileToRemove = videoFiles[index];
                          if (fileToRemove) {
                            handleRemoveVideo(fileToRemove);
                          }
                        }}
                        onPreview={() => {
                          if (fileToPreview) {
                            handleVideoPreview(
                              URL.createObjectURL(fileToPreview),
                              fileToPreview.name
                            );
                          } else {
                            handleVideoPreview(url, url.split('/').pop() || 'Video');
                          }
                        }}
                        type="video"
                      />
                    );
                  })}
                </>
              ) : (
                <AddMoreCard
                  onClick={handleVideoClick}
                  title="Thêm video"
                  description="Tải lên video giới thiệu"
                  icon={Plus}
                />
              )}
            </div>
          </FileDropZone>

          <input
            type="file"
            ref={videoInputRef}
            onChange={handleEnhancedVideoChange}
            accept="video/*"
            multiple
            className="hidden"
          />
        </CardContent>
      </Card>

      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4 md:pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Tài liệu pháp lý
            {uploadedDocumentUrls.length + existingDocuments.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {uploadedDocumentUrls.length + existingDocuments.length}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Tải lên các tài liệu pháp lý quan trọng như sổ đỏ, giấy phép xây dựng
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 md:space-y-6 lg:space-y-8">
          <FileDropZone onDrop={handleDocumentDrop} accept=".pdf,.doc,.docx,.xls,.xlsx" multiple>
            <div className="grid grid-cols-3 gap-4">
              {existingDocuments.map((doc, index) => (
                <FileGridItem
                  key={`existing-doc-${index}`}
                  url={doc.url}
                  onRemove={() => handleRemoveExistingDocument(doc.url)}
                  isExisting
                  type="document"
                />
              ))}

              {uploadedDocumentUrls.map((url, index) => (
                <FileGridItem
                  key={`uploaded-doc-${index}`}
                  url={url}
                  onRemove={() => {
                    const fileToRemove = documentFiles[index];
                    if (fileToRemove) {
                      handleRemoveDocument(fileToRemove);
                    }
                  }}
                  type="document"
                />
              ))}
            </div>
          </FileDropZone>

          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={handleDocumentClick}
              className="w-full h-20 border-dashed border-2 hover:border-primary/50 hover:bg-primary/5"
            >
              <div className="flex items-center gap-3">
                <Plus className="h-6 w-6" />
                <div className="text-center">
                  <p className="font-medium">Tải lên tài liệu</p>
                  <p className="text-xs text-muted-foreground">PDF, DOC, DOCX, XLS, XLSX</p>
                </div>
              </div>
            </Button>
          </div>

          <input
            type="file"
            ref={documentInputRef}
            onChange={handleEnhancedDocumentChange}
            accept=".pdf,.doc,.docx,.xls,.xlsx"
            multiple
            className="hidden"
          />
        </CardContent>
      </Card>

      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4 md:pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Check className="h-5 w-5" />
            {formData.type === PropertyType.LAND_PLOT ||
            formData.type === PropertyType.PROJECT_LAND ||
            formData.type === PropertyType.NEW_URBAN_AREA ||
            formData.type === PropertyType.ECO_RESORT
              ? 'Đặc điểm đất'
              : formData.type === PropertyType.OFFICE ||
                  formData.type === PropertyType.WAREHOUSE ||
                  formData.type === PropertyType.FACTORY ||
                  formData.type === PropertyType.INDUSTRIAL
                ? 'Tiện ích thương mại'
                : 'Tiện ích bất động sản'}
          </CardTitle>
          <CardDescription>
            {formData.type === PropertyType.LAND_PLOT ||
            formData.type === PropertyType.PROJECT_LAND ||
            formData.type === PropertyType.NEW_URBAN_AREA ||
            formData.type === PropertyType.ECO_RESORT
              ? 'Chọn các đặc điểm và loại đất phù hợp'
              : formData.type === PropertyType.OFFICE ||
                  formData.type === PropertyType.WAREHOUSE ||
                  formData.type === PropertyType.FACTORY ||
                  formData.type === PropertyType.INDUSTRIAL
                ? 'Chọn các tiện ích có sẵn tại bất động sản thương mại'
                : 'Chọn các tiện ích có sẵn tại bất động sản của bạn'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 md:space-y-6 lg:space-y-8">
          {/* Select All Checkbox */}
          <div className="flex items-center space-x-3 p-4 border rounded-lg bg-muted/30">
            <Checkbox
              id="select-all-amenities"
              checked={areAllAmenitiesSelected()}
              onCheckedChange={checked => {
                amenitiesList.forEach(amenityId => {
                  handleCheckboxChange(`amenities.${amenityId.id}`, checked === true);
                });
              }}
              className="mt-1"
            />
            <Label htmlFor="select-all-amenities" className="text-sm font-medium cursor-pointer">
              Chọn tất cả{' '}
              {formData.type === PropertyType.LAND_PLOT ||
              formData.type === PropertyType.PROJECT_LAND ||
              formData.type === PropertyType.NEW_URBAN_AREA ||
              formData.type === PropertyType.ECO_RESORT
                ? 'đặc điểm'
                : 'tiện ích'}
              {areSomeAmenitiesSelected() && (
                <span className="text-xs text-muted-foreground ml-2">
                  (
                  {
                    amenitiesList.filter(
                      amenityId => formData.amenities?.[amenityId.id as keyof Amenity] === true
                    ).length
                  }
                  /{amenitiesList.length})
                </span>
              )}
            </Label>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {amenitiesList.map(amenity => (
              <div
                key={amenity.id}
                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
              >
                <Checkbox
                  id={`amenities.${amenity.id}`}
                  checked={formData.amenities?.[amenity.id as keyof Amenity]}
                  onCheckedChange={checked =>
                    handleCheckboxChange(`amenities.${amenity.id}`, checked === true)
                  }
                  className="mt-1"
                />
                <div className="flex items-center space-x-2 flex-1">
                  <amenity.icon className="h-4 w-4 text-muted-foreground" />
                  <Label
                    htmlFor={`amenities.${amenity.id}`}
                    className="text-sm font-medium cursor-pointer"
                  >
                    {amenity.label}
                  </Label>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-sm bg-muted/20">
        <CardContent className="pt-4 md:pt-6">
          <div className="flex gap-3">
            <Info className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium">Lưu ý quan trọng</p>
              <p className="text-xs text-muted-foreground">
                Hình ảnh chất lượng cao và tài liệu pháp lý đầy đủ sẽ giúp tăng độ tin cậy và thu
                hút khách hàng. Video giới thiệu có thể giúp bạn nổi bật hơn so với các tin đăng
                khác. Đảm bảo tất cả tài liệu đều hợp lệ và cập nhật.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-0 shadow-sm">
        <CardContent className="pt-4 md:pt-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {totalFiles > 0 ? (
                <span>{totalFiles} tệp đã sẵn sàng để gửi</span>
              ) : (
                <span>Chưa có tệp nào được chọn</span>
              )}
            </div>
            <Button
              onClick={handleEnhancedSubmit}
              disabled={isPending || totalFiles === 0 || uploadProgress > 0}
              size="lg"
              className="px-8"
            >
              {isPending || uploadProgress > 0 ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/50 border-t-white animate-spin rounded-full mr-2" />
                  {uploadProgress === 100 ? 'Hoàn tất' : 'Đang xử lý...'}
                </>
              ) : (
                <>{isEditMode ? 'Cập nhật bất động sản' : 'Gửi thông tin'}</>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
