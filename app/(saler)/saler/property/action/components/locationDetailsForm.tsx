'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { MapPin, Map, Building, Info, Navigation, Search, Crosshair } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import {
  APIProvider,
  Map as GoogleMap,
  AdvancedMarker,
  Pin,
  useMap,
  useMapsLibrary,
} from '@vis.gl/react-google-maps';

interface Province {
  code: number;
  name: string;
  districts?: District[];
}

interface District {
  code: number;
  name: string;
  wards?: Ward[];
}

interface Ward {
  code: number;
  name: string;
}

interface LocationFormData {
  location: {
    address: string;
    latitude: number;
    longitude: number;
    city: string;
    district: string;
    ward: string;
  };
}

interface LocationDetailsFormProps {
  formData: LocationFormData;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (id: string, value: string) => void;
  handleNext: () => void;
}

export default function LocationDetailsForm({
  formData,
  handleChange,
  handleSelectChange,
  handleNext,
}: LocationDetailsFormProps) {
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<Ward[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [selectedDistrict, setSelectedDistrict] = useState<string>('');
  const [selectedWard, setSelectedWard] = useState<string>('');
  const [locationTab, setLocationTab] = useState<string>('province');

  // Google Maps state
  const [mapCenter, setMapCenter] = useState({
    lat: formData.location.latitude || 10.842935416604869,
    lng: formData.location.longitude || 106.84182012230411,
  });
  const [markerPosition, setMarkerPosition] = useState({
    lat: formData.location.latitude || 10.842935416604869,
    lng: formData.location.longitude || 106.84182012230411,
  });
  const [searchAddress, setSearchAddress] = useState('');
  const [mapError, setMapError] = useState<string | null>(null);
  const [placeSelected, setPlaceSelected] = useState(false);
  const [markerKey, setMarkerKey] = useState(0);
  const [autocompleteReady, setAutocompleteReady] = useState(false);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const searchInputRef = useRef<HTMLInputElement | null>(null);
  const autocompleteInitializedRef = useRef(false);
  const defaultCoordinatesSetRef = useRef(false);

  // Update map center when form data changes
  useEffect(() => {
    if (formData.location.latitude && formData.location.longitude) {
      const newCenter = {
        lat: formData.location.latitude,
        lng: formData.location.longitude,
      };
      setMapCenter(newCenter);
      setMarkerPosition(newCenter);
      setMarkerKey(prev => prev + 1); // Force marker re-render when form data changes
    } else if (!defaultCoordinatesSetRef.current) {
      // Set default coordinates in form data if none exist (only once)
      const defaultLat = 10.842935416604869;
      const defaultLng = 106.84182012230411;

      handleSelectChange('location.latitude', defaultLat.toString());
      handleSelectChange('location.longitude', defaultLng.toString());

      const newCenter = {
        lat: defaultLat,
        lng: defaultLng,
      };
      setMapCenter(newCenter);
      setMarkerPosition(newCenter);
      defaultCoordinatesSetRef.current = true;
    }
  }, [formData.location.latitude, formData.location.longitude]);

  // Reset default coordinates flag when formData changes (e.g., when editing existing property)
  useEffect(() => {
    if (formData.location.latitude && formData.location.longitude) {
      defaultCoordinatesSetRef.current = true;
    }
  }, [formData.location.latitude, formData.location.longitude]);

  // Handle manual coordinate updates
  // const handleCoordinateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   handleChange(e);

  //   // Update map if both coordinates are available
  //   const { id, value } = e.target;
  //   const numValue = parseFloat(value);

  //   if (!isNaN(numValue)) {
  //     if (id === 'location.latitude') {
  //       const newLat = numValue;
  //       const currentLng = formData.location.longitude || markerPosition.lng;
  //       const newCenter = { lat: newLat, lng: currentLng };
  //       setMapCenter(newCenter);
  //       setMarkerPosition(newCenter);
  //     } else if (id === 'location.longitude') {
  //       const newLng = numValue;
  //       const currentLat = formData.location.latitude || markerPosition.lat;
  //       const newCenter = { lat: currentLat, lng: newLng };
  //       setMapCenter(newCenter);
  //       setMarkerPosition(newCenter);
  //     }
  //   }
  // };

  // Set initial values from formData if present
  useEffect(() => {
    if (formData.location.city) {
      const province = provinces.find(p => p.name === formData.location.city);
      if (province) {
        setSelectedProvince(province.code.toString());
      }
    }
  }, [formData.location.city, provinces]);

  // Fetch provinces on component mount
  useEffect(() => {
    const fetchProvinces = async () => {
      setLoading(true);
      try {
        const response = await fetch('https://provinces.open-api.vn/api/p/');
        if (!response.ok) throw new Error('Failed to fetch provinces');
        const data = await response.json();
        setProvinces(data);
      } catch (error) {
        console.error('Error fetching provinces:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProvinces();
  }, []);

  // Fetch districts when province changes
  useEffect(() => {
    if (!selectedProvince) {
      setDistricts([]);
      return;
    }

    const fetchDistricts = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://provinces.open-api.vn/api/p/${selectedProvince}?depth=2`
        );
        if (!response.ok) throw new Error('Failed to fetch districts');
        const data = await response.json();
        setDistricts(data.districts || []);

        // Set selected district if it exists in formData
        if (formData.location.district) {
          const district = data.districts?.find(
            (d: District) => d.name === formData.location.district
          );
          if (district) {
            setSelectedDistrict(district.code.toString());
          }
        }
      } catch (error) {
        console.error('Error fetching districts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDistricts();
  }, [selectedProvince, formData.location.district]);

  // Fetch wards when district changes
  useEffect(() => {
    if (!selectedDistrict) {
      setWards([]);
      return;
    }

    const fetchWards = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://provinces.open-api.vn/api/d/${selectedDistrict}?depth=2`
        );
        if (!response.ok) throw new Error('Failed to fetch wards');
        const data = await response.json();
        setWards(data.wards || []);

        // Set selected ward if it exists in formData
        if (formData.location.ward) {
          const ward = data.wards?.find((w: Ward) => w.name === formData.location.ward);
          if (ward) {
            setSelectedWard(ward.code.toString());
          }
        }
      } catch (error) {
        console.error('Error fetching wards:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWards();
  }, [selectedDistrict, formData.location.ward]);

  const handleLocationChange = (type: 'province' | 'district' | 'ward', value: string) => {
    if (type === 'province') {
      const province = provinces.find(p => p.code.toString() === value);
      if (province) {
        setSelectedProvince(value);
        handleSelectChange('location.city', province.name);
        // Reset district and ward when province changes
        setSelectedDistrict('');
        setSelectedWard('');
        handleSelectChange('location.district', '');
        handleSelectChange('location.ward', '');
      }
    } else if (type === 'district') {
      const district = districts.find(d => d.code.toString() === value);
      if (district) {
        setSelectedDistrict(value);
        handleSelectChange('location.district', district.name);
        // Reset ward when district changes
        setSelectedWard('');
        handleSelectChange('location.ward', '');
      }
    } else if (type === 'ward') {
      const ward = wards.find(w => w.code.toString() === value);
      if (ward) {
        setSelectedWard(value);
        handleSelectChange('location.ward', ward.name);
      }
    }
  };

  const LocationPath = () => (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      <Navigation className="h-4 w-4" />
      <span>
        {formData.location.city && (
          <>
            {formData.location.city}
            {formData.location.district && (
              <>
                {' → '}
                {formData.location.district}
                {formData.location.ward && (
                  <>
                    {' → '}
                    {formData.location.ward}
                  </>
                )}
              </>
            )}
          </>
        )}
      </span>
    </div>
  );

  // Google Maps component with autocomplete
  function MapContent() {
    const map = useMap();
    const placesLib = useMapsLibrary('places');
    const geocodingLib = useMapsLibrary('geocoding');

    // Memoized callback for place selection
    const handlePlaceSelected = useCallback(
      (place: google.maps.places.PlaceResult) => {
        if (place.geometry && place.geometry.location) {
          const newLat = place.geometry.location.lat();
          const newLng = place.geometry.location.lng();

          // Update form data first
          handleSelectChange('location.latitude', newLat.toString());
          handleSelectChange('location.longitude', newLng.toString());

          // Update map center and marker position
          setMapCenter({ lat: newLat, lng: newLng });
          setMarkerPosition({ lat: newLat, lng: newLng });
          setMarkerKey(prev => prev + 1); // Force marker re-render

          if (place.formatted_address) {
            setSearchAddress(place.formatted_address);
            // Update the address input
            const addressEvent = {
              target: { id: 'location.address', value: place.formatted_address },
            } as React.ChangeEvent<HTMLInputElement>;
            handleChange(addressEvent);
          }

          // Pan map to new location immediately
          if (map) {
            map.panTo({ lat: newLat, lng: newLng });
            map.setZoom(16);
          }

          setMapError(null);
          setPlaceSelected(true);

          // Clear the search input after a short delay to show the selected address
          setTimeout(() => {
            if (searchInputRef.current) {
              searchInputRef.current.value = '';
            }
          }, 100);
        } else {
          console.error('Place has no geometry:', place);
          setMapError('Không thể lấy tọa độ từ địa chỉ đã chọn. Vui lòng thử lại.');
        }
      },
      [map]
    );

    // Set up Autocomplete when places library is loaded
    useEffect(() => {
      if (!placesLib || !searchInputRef.current || autocompleteInitializedRef.current) {
        return;
      }

      const setupAutocomplete = () => {
        try {
          // Clear any existing autocomplete
          if (autocompleteRef.current) {
            google.maps.event.clearInstanceListeners(autocompleteRef.current);
            autocompleteRef.current = null;
          }

          if (!searchInputRef.current) {
            return;
          }

          autocompleteRef.current = new google.maps.places.Autocomplete(searchInputRef.current, {
            fields: ['address_components', 'geometry', 'formatted_address'],
            componentRestrictions: { country: 'vn' }, // Restrict to Vietnam
          });

          autocompleteRef.current.addListener('place_changed', () => {
            const place = autocompleteRef.current!.getPlace();
            handlePlaceSelected(place);
          });

          setAutocompleteReady(true);
          autocompleteInitializedRef.current = true;
        } catch (error) {
          console.error('Error setting up autocomplete:', error);
          setMapError('Không thể tải tính năng tìm kiếm địa chỉ. Vui lòng thử lại.');
          setAutocompleteReady(false);
        }
      };

      // Setup autocomplete once
      setupAutocomplete();

      return () => {
        if (autocompleteRef.current) {
          google.maps.event.clearInstanceListeners(autocompleteRef.current);
          autocompleteRef.current = null;
        }
        setAutocompleteReady(false);
        autocompleteInitializedRef.current = false;
      };
    }, [placesLib, handlePlaceSelected]);

    // Handle marker drag end to update address and coordinates
    const handleMarkerDragEnd = useCallback(
      (event: google.maps.MapMouseEvent) => {
        if (!event.latLng) return;

        const newLat = event.latLng.lat();
        const newLng = event.latLng.lng();

        // Update form data first
        handleSelectChange('location.latitude', newLat.toString());
        handleSelectChange('location.longitude', newLng.toString());

        // Update marker position and map center
        setMarkerPosition({ lat: newLat, lng: newLng });
        setMapCenter({ lat: newLat, lng: newLng });
        setMarkerKey(prev => prev + 1); // Force marker re-render

        // Reverse geocode to update address using the geocoding library
        if (geocodingLib) {
          const geocoder = new geocodingLib.Geocoder();
          geocoder.geocode(
            { location: { lat: newLat, lng: newLng } },
            (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
              if (status === 'OK' && results && results[0]) {
                const formattedAddress = results[0].formatted_address;
                setSearchAddress(formattedAddress);

                // Update the address input
                const addressEvent = {
                  target: { id: 'location.address', value: formattedAddress },
                } as React.ChangeEvent<HTMLInputElement>;
                handleChange(addressEvent);

                setMapError(null);
              } else {
                console.error('Geocode failed:', status);
                setMapError('Không thể lấy địa chỉ từ tọa độ. Vui lòng thử lại.');
              }
            }
          );
        } else {
          // Fallback when geocoding library is not available
          console.warn('Geocoding library not available');
          setMapError(
            'Tính năng lấy địa chỉ tạm thời không khả dụng. Vui lòng nhập địa chỉ thủ công.'
          );
        }
      },
      [geocodingLib]
    );

    return (
      <AdvancedMarker
        key={`marker-${markerKey}-${placeSelected ? 'selected' : 'default'}`}
        position={markerPosition}
        draggable={true}
        onDragEnd={handleMarkerDragEnd}
      >
        <Pin
          background={placeSelected ? '#00ff00' : '#ff0000'}
          glyphColor={'#ffffff'}
          borderColor={'#ffffff'}
        />
      </AdvancedMarker>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6 md:space-y-8">
      {/* Header Section */}
      <div className="space-y-3 px-4 md:px-0">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <MapPin className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-xl md:text-2xl font-semibold tracking-tight">Thông tin địa chỉ</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Cung cấp địa chỉ cụ thể và tọa độ
            </p>
          </div>
        </div>
      </div>

      {/* Location Selection Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Building className="h-5 w-5" />
            Địa chỉ quận huyện
          </CardTitle>
          <CardDescription>Chọn địa chỉ quận huyện cụ thể cho bất động sản của bạn</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Location Path */}
          {(formData.location.city || formData.location.district || formData.location.ward) && (
            <div className="p-4 bg-muted/30 rounded-lg">
              <LocationPath />
            </div>
          )}

          <Tabs
            value={locationTab}
            onValueChange={value => {
              setLocationTab(value);
            }}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="province" className="relative">
                Tỉnh/Thành phố
                {formData.location.city && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                    ✓
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="district" disabled={!selectedProvince} className="relative">
                Quận/Huyện
                {formData.location.district && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                    ✓
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="ward" disabled={!selectedDistrict} className="relative">
                Phường/Xã
                {formData.location.ward && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                    ✓
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            {/* Province Tab */}
            <TabsContent value="province" className="space-y-4">
              {loading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-[60vh] md:max-h-80 overflow-y-auto">
                  {provinces
                    .sort((a, b) => {
                      const popularCities = ['Thành phố Hồ Chí Minh', 'Thành phố Hà Nội'];
                      if (popularCities.includes(a.name) && !popularCities.includes(b.name)) {
                        return -1;
                      }
                      if (!popularCities.includes(a.name) && popularCities.includes(b.name)) {
                        return 1;
                      }
                      return 0;
                    })
                    .map(province => (
                      <button
                        key={province.code}
                        onClick={() => {
                          handleLocationChange('province', province.code.toString());
                          setLocationTab('district');
                        }}
                        className={`p-3 text-left rounded-lg border hover:border-primary transition-colors ${
                          selectedProvince === province.code.toString()
                            ? 'border-primary bg-primary/5'
                            : 'border-border'
                        }`}
                      >
                        <span className="font-medium text-sm">{province.name}</span>
                      </button>
                    ))}
                </div>
              )}
            </TabsContent>

            {/* District Tab */}
            <TabsContent value="district" className="space-y-4">
              {loading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-[60vh] md:max-h-80 overflow-y-auto">
                  {districts.map(district => (
                    <button
                      key={district.code}
                      onClick={() => {
                        handleLocationChange('district', district.code.toString());
                        setLocationTab('ward');
                      }}
                      className={`p-3 text-left rounded-lg border hover:border-primary transition-colors ${
                        selectedDistrict === district.code.toString()
                          ? 'border-primary bg-primary/5'
                          : 'border-border'
                      }`}
                    >
                      <span className="font-medium text-sm">{district.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </TabsContent>

            {/* Ward Tab */}
            <TabsContent value="ward" className="space-y-4">
              {loading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-[60vh] md:max-h-80 overflow-y-auto">
                  {wards.map(ward => (
                    <button
                      key={ward.code}
                      onClick={() => handleLocationChange('ward', ward.code.toString())}
                      className={`p-3 text-left rounded-lg border hover:border-primary transition-colors ${
                        selectedWard === ward.code.toString()
                          ? 'border-primary bg-primary/5'
                          : 'border-border'
                      }`}
                    >
                      <span className="font-medium text-sm">{ward.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      {/* Google Maps Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Crosshair className="h-5 w-5" />
            Định vị chính xác trên bản đồ
          </CardTitle>
          <CardDescription>
            Tìm kiếm địa chỉ và kéo thả pin để định vị chính xác tọa độ bất động sản
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search Input */}
          <div className="space-y-3">
            <Label htmlFor="search-address" className="text-sm font-medium">
              Tìm kiếm địa chỉ
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search-address"
                ref={searchInputRef}
                placeholder={
                  autocompleteReady
                    ? 'Nhập địa chỉ để tìm kiếm...'
                    : 'Đang tải tính năng tìm kiếm...'
                }
                value={searchAddress}
                onChange={e => {
                  setSearchAddress(e.target.value);
                  setPlaceSelected(false);
                }}
                className="h-11 pl-10"
                // disabled={!autocompleteReady}
                disabled={true}
              />
              {!autocompleteReady && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                </div>
              )}
            </div>
            {placeSelected && (
              <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <p className="text-xs text-green-700">
                  Đã chọn địa chỉ thành công! Pin đã được cập nhật.
                </p>
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              {autocompleteReady
                ? 'Nhập địa chỉ và chọn từ danh sách gợi ý để di chuyển bản đồ'
                : 'Vui lòng đợi tính năng tìm kiếm được tải xong...'}
            </p>
          </div>

          {/* Map */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Bản đồ tương tác</Label>
            <div className="relative h-[500px] w-full rounded-lg border overflow-hidden">
              {mapError && (
                <div className="absolute inset-0 bg-red-50 border-2 border-red-200 rounded-lg flex items-center justify-center z-10">
                  <div className="text-center p-4">
                    <p className="text-sm text-red-600 font-medium">Lỗi bản đồ</p>
                    <p className="text-xs text-red-500 mt-1">{mapError}</p>
                  </div>
                </div>
              )}

              <APIProvider
                apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}
                libraries={['places', 'geocoding']}
              >
                <GoogleMap
                  defaultZoom={15}
                  center={mapCenter}
                  gestureHandling="greedy"
                  disableDefaultUI={false}
                  mapId={process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID}
                  className="w-full h-full"
                >
                  <MapContent />
                </GoogleMap>
              </APIProvider>

              {/* Map Instructions Overlay */}
              <div className="absolute bottom-8 left-2 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm border">
                <p className="text-xs text-muted-foreground">💡 Kéo thả pin để định vị chính xác</p>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Kéo thả pin để định vị chính xác vị trí bất động sản. Tọa độ sẽ được tự động cập nhật.
              Pin màu đỏ = vị trí mặc định, Pin màu xanh = đã chọn từ tìm kiếm.
            </p>
          </div>

          {/* Current Coordinates Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/30 rounded-lg">
            <div className="space-y-1">
              <Label className="text-xs font-medium text-muted-foreground">Vĩ độ hiện tại</Label>
              <p className="text-sm font-mono">{markerPosition.lat.toFixed(6)}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-xs font-medium text-muted-foreground">Kinh độ hiện tại</Label>
              <p className="text-sm font-mono">{markerPosition.lng.toFixed(6)}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Address Details Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Map className="h-5 w-5" />
            Thông tin địa chỉ
          </CardTitle>
          <CardDescription>
            Cung cấp địa chỉ cụ thể và tọa độ cho bất động sản của bạn
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Label htmlFor="location.address" className="text-sm font-medium">
                Địa chỉ
              </Label>
              <span className="text-destructive text-sm">*</span>
            </div>
            <Input
              id="location.address"
              placeholder="Nhập địa chỉ cụ thể của bất động sản"
              value={formData.location.address}
              onChange={handleChange}
              className="h-11"
              required
            />
            <p className="text-xs text-muted-foreground">Nhập địa chỉ cụ thể của bất động sản</p>
          </div>

          <Separator />
          {/* 
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            <div className="space-y-3">
              <Label htmlFor="location.latitude" className="text-sm font-medium">
                Tọa độ GPS vĩ độ
              </Label>
              <Input
                id="location.latitude"
                type="number"
                step="any"
                placeholder="Nhập tọa độ GPS vĩ độ"
                value={formData.location.latitude || ''}
                onChange={handleCoordinateChange}
                className="h-11"
                disabled={true}
              />
              <p className="text-xs text-muted-foreground">Tọa độ GPS vĩ độ (tùy chọn)</p>
            </div>

            <div className="space-y-3">
              <Label htmlFor="location.longitude" className="text-sm font-medium">
                Tọa độ GPS kinh độ
              </Label>
              <Input
                id="location.longitude"
                type="number"
                step="any"
                placeholder="Nhập tọa độ GPS kinh độ"
                value={formData.location.longitude || ''}
                onChange={handleCoordinateChange}
                className="h-11"
                disabled={true}
              />
              <p className="text-xs text-muted-foreground">Tọa độ GPS kinh độ (tùy chọn)</p>
            </div>
          </div> */}
        </CardContent>
      </Card>

      {/* Help Card */}
      <Card className="border-dashed bg-muted/30">
        <CardContent className="p-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium">Hướng dẫn sử dụng bản đồ</p>
              <p className="text-xs text-muted-foreground">
                1. <strong>Tìm kiếm địa chỉ:</strong> Nhập địa chỉ vào ô tìm kiếm và chọn từ danh
                sách gợi ý để di chuyển bản đồ đến vị trí đó. Pin sẽ chuyển sang màu xanh khi đã
                chọn.
                <br />
                2. <strong>Định vị chính xác:</strong> Kéo thả pin để định vị chính xác vị trí bất
                động sản. Tọa độ GPS sẽ được tự động cập nhật.
                <br />
                3. <strong>Thông tin địa chỉ:</strong> Sau khi định vị, địa chỉ chi tiết sẽ được tự
                động điền vào form. Bạn có thể chỉnh sửa thêm nếu cần thiết.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end pt-6 border-t">
        <Button onClick={handleNext} size="lg" className="px-8">
          Tiếp theo
        </Button>
      </div>
    </div>
  );
}
