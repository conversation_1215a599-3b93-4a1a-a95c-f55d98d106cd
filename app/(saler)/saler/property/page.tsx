'use client';

import { SiteHeader } from '@/components/common/siteHeader';
import { useEffect, useState, useCallback, memo, useMemo, useRef } from 'react';
import { usePropertiesBySaler, useDeleteProperty } from '@/hooks/useProperty';
import { PropertySearchParams, Property } from '@/lib/api/services/fetchProperty';
import { Building, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, SheetTitle } from '@/components/ui/sheet';

import { PropertyCard } from './components/propertyCard';
import { PropertyFilters } from './components/propertyFilters';
import { PropertyToolbar } from './components/propertyToolbar';
import { PropertyPagination } from './components/propertyPagination';

const PROPERTY_FILTERS_KEY = 'propertyFilters';
const FILTER_RESET_EVENT = 'property-filters-reset';

const DeletePropertyDialog = ({
  isOpen,
  onOpenChange,
  onConfirm,
}: {
  propertyId: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa bất động sản</AlertDialogTitle>
          <AlertDialogDescription>
            Bạn có chắc chắn muốn xóa bất động sản này? Hành động này không thể hoàn tác.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Hủy</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm}>Xóa</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default memo(function PropertyPage() {
  const isResettingFilters = useRef(false);
  const { mutate: deleteProperty } = useDeleteProperty();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [propertyToDelete, setPropertyToDelete] = useState<string | null>(null);
  const [mobileSheetOpen, setMobileSheetOpen] = useState(false);
  const [searchParams, setSearchParams] = useState<PropertySearchParams>({
    pageNumber: 1,
    pageSize: 12,
    sortBy: 'createdAt',
    isDescending: true, // Default to newest first
  });

  const isInitialMount = useRef(true);

  useEffect(() => {
    const checkScreenSize = () => {
      const isLarge = window.innerWidth >= 1024;
      setIsLargeScreen(isLarge);
      if (isLarge) {
        setShowFilters(true);
      } else {
        setShowFilters(false);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined' && isInitialMount.current) {
      isInitialMount.current = false;

      try {
        const savedFilters = localStorage.getItem(PROPERTY_FILTERS_KEY);
        if (savedFilters) {
          const parsedFilters = JSON.parse(savedFilters) as PropertySearchParams;
          setSearchParams(prev => ({
            ...prev,
            ...parsedFilters,
            pageNumber: 1,
            pageSize: 12,
          }));
        }
      } catch (error) {
        console.error('Error parsing saved filters:', error);
        localStorage.removeItem(PROPERTY_FILTERS_KEY);
      }
    }
  }, []);

  useEffect(() => {
    const handleFilterReset = () => {
      isResettingFilters.current = true;

      setSearchParams({
        pageNumber: 1,
        pageSize: 12,
        sortBy: 'createdAt',
        isDescending: true, // Default to newest first
      });

      setTimeout(() => {
        isResettingFilters.current = false;
      }, 500);
    };

    window.addEventListener(FILTER_RESET_EVENT, handleFilterReset);
    return () => {
      window.removeEventListener(FILTER_RESET_EVENT, handleFilterReset);
    };
  }, []);

  const { data, isLoading, isError, error, refetch, isFetching } =
    usePropertiesBySaler(searchParams);

  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    if (data?.properties) {
      const newTotalItems = (() => {
        if (data.properties.length < searchParams.pageSize!) {
          return (searchParams.pageNumber! - 1) * searchParams.pageSize! + data.properties.length;
        } else if (data.properties.length === searchParams.pageSize!) {
          return data.count || searchParams.pageNumber! * searchParams.pageSize! + 10;
        }
        return totalItems;
      })();

      if (newTotalItems !== totalItems) {
        setTotalItems(newTotalItems);
      }
    }
  }, [data?.properties, data?.count, searchParams.pageNumber, searchParams.pageSize, totalItems]);

  const properties = useMemo(() => {
    if (!data?.properties || !Array.isArray(data.properties)) return [];
    return data.properties;
  }, [data?.properties]);

  const hasActiveFilters = useMemo(() => {
    return Object.keys(searchParams).some(key => {
      if (key === 'pageNumber' || key === 'pageSize') {
        return false;
      }
      return searchParams[key as keyof PropertySearchParams] !== undefined;
    });
  }, [searchParams]);

  useEffect(() => {
    if (isResettingFilters.current) {
      return;
    }

    if (typeof window !== 'undefined') {
      const isDefaultState =
        Object.keys(searchParams).length <= 3 && !searchParams.type && !searchParams.searchTerm;

      if (isDefaultState) {
        localStorage.removeItem(PROPERTY_FILTERS_KEY);
        return;
      }

      const saveTimeout = setTimeout(() => {
        const filtersToSave = { ...searchParams };
        delete filtersToSave.pageNumber;
        delete filtersToSave.pageSize;

        if (Object.keys(filtersToSave).length > 1) {
          localStorage.setItem(PROPERTY_FILTERS_KEY, JSON.stringify(filtersToSave));
        } else {
          localStorage.removeItem(PROPERTY_FILTERS_KEY);
        }
      }, 300);

      return () => clearTimeout(saveTimeout);
    }
  }, [searchParams]);

  const handleFilterChange = useCallback((newParams: PropertySearchParams) => {
    const isResetOperation =
      Object.keys(newParams).length === 3 && newParams.pageNumber === 1 && 'pageSize' in newParams;

    if (isResetOperation) {
      isResettingFilters.current = true;
      setTimeout(() => {
        isResettingFilters.current = false;
      }, 500);
    }

    setSearchParams(newParams);
  }, []);

  const clearAllFilters = useCallback(() => {
    isResettingFilters.current = true;
    setSearchParams({
      pageNumber: 1,
      pageSize: 12,
      sortBy: 'createdAt',
      isDescending: true, // Default to newest first
    });
    setTimeout(() => {
      isResettingFilters.current = false;
    }, 500);
  }, []);

  const toggleFilters = useCallback(() => {
    if (isLargeScreen) {
      setShowFilters(prev => !prev);
    }
  }, [isLargeScreen]);

  const closeFilters = useCallback(() => {
    if (isLargeScreen) {
      setShowFilters(false);
    }
  }, [isLargeScreen]);

  const openMobileFilters = useCallback(() => {
    setMobileSheetOpen(true);
  }, []);

  const closeMobileFilters = useCallback(() => {
    setMobileSheetOpen(false);
  }, []);

  const handleDeleteClick = (propertyId: string) => {
    setPropertyToDelete(propertyId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (propertyToDelete) {
      deleteProperty(propertyToDelete);
      setDeleteDialogOpen(false);
      setPropertyToDelete(null);
    }
  };

  const LoadingSkeleton = () => (
    <div
      className={cn(
        'gap-4',
        viewMode === 'grid'
          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'
          : 'flex flex-col space-y-3'
      )}
    >
      {Array(12)
        .fill(0)
        .map((_, index) => (
          <div key={index} className="space-y-2">
            <Skeleton
              className={cn('w-full rounded-lg', viewMode === 'grid' ? 'aspect-[16/10]' : 'h-32')}
            />
            <div className="space-y-1 px-3 pb-3">
              <Skeleton className="h-3 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
              <Skeleton className="h-5 w-full" />
              <div className={cn(viewMode === 'grid' ? 'grid grid-cols-3 gap-1' : 'flex gap-2')}>
                <Skeleton className="h-6 w-full" />
                <Skeleton className="h-6 w-full" />
                <Skeleton className="h-6 w-full" />
              </div>
            </div>
          </div>
        ))}
    </div>
  );

  const EmptyState = ({ hasFilters }: { hasFilters: boolean }) => (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <Building className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-medium mb-2">
        {hasFilters ? 'Không có bất động sản phù hợp' : 'Không có bất động sản'}
      </h3>
      <p className="text-muted-foreground mb-6 max-w-md">
        {hasFilters ? 'Vui lòng điều chỉnh bộ lọc' : 'Bắt đầu bằng cách thêm bất động sản'}
      </p>
      {hasFilters ? (
        <Button variant="outline" onClick={clearAllFilters}>
          Xóa bộ lọc
        </Button>
      ) : (
        <Button asChild>
          <a href="/saler/property/action">
            <Plus className="h-4 w-4 mr-2" />
            Thêm bất động sản
          </a>
        </Button>
      )}
    </div>
  );

  return (
    <div className="flex flex-col h-full">
      <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
        <SiteHeader title="Quản lý bất động sản" />
      </header>
      <div className="flex-1 flex flex-col min-h-0">
        {/* Toolbar Section - Fixed Height */}
        <div className="flex-shrink-0 p-4 lg:p-6 border-b bg-background relative">
          <PropertyToolbar
            searchParams={searchParams}
            onFilterChange={handleFilterChange}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            totalItems={totalItems}
            isLoading={isLoading || isFetching}
            hasFilters={hasActiveFilters}
            onToggleFilters={toggleFilters}
            onMobileFilterOpen={openMobileFilters}
          />
        </div>

        {/* Main Content Area - Takes remaining height */}
        <div className="flex-1 flex min-h-0">
          {/* Desktop Layout with Resizable Panels */}
          <div className="hidden lg:flex w-full h-full">
            <ResizablePanelGroup direction="horizontal" className="h-full w-full">
              {/* Property List Panel */}
              <ResizablePanel minSize={60} className="flex-1">
                <div className="h-full flex flex-col">
                  {/* Scrollable Property List */}
                  <div className="flex-1 overflow-auto scrollbar-hide">
                    <div className="p-4 lg:p-6">
                      {isError ? (
                        <div className="rounded-lg border border-destructive bg-destructive/10 p-8 text-center">
                          <h3 className="text-lg font-medium mb-2">Lỗi khi tải bất động sản</h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            {error instanceof Error
                              ? error.message
                              : 'Đã xảy ra lỗi không xác định'}
                          </p>
                          <Button variant="outline" onClick={() => refetch()}>
                            Thử lại
                          </Button>
                        </div>
                      ) : isLoading && !properties.length ? (
                        <LoadingSkeleton />
                      ) : properties.length === 0 ? (
                        <EmptyState hasFilters={hasActiveFilters} />
                      ) : (
                        <>
                          <div
                            className={cn(
                              'gap-3 lg:gap-4 mb-6',
                              viewMode === 'grid'
                                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 2xl:grid-cols-3'
                                : 'flex flex-col space-y-3'
                            )}
                          >
                            {properties.map((property: Property) => (
                              <PropertyCard
                                key={property.id}
                                property={property}
                                viewMode={viewMode}
                                onDelete={() => handleDeleteClick(property.id)}
                              />
                            ))}
                          </div>

                          {totalItems > searchParams.pageSize! && (
                            <PropertyPagination
                              searchParams={searchParams}
                              onFilterChange={handleFilterChange}
                              totalItems={totalItems}
                              isLoading={isLoading || isFetching}
                            />
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </ResizablePanel>

              {/* Filter Sidebar - Desktop Only */}
              {showFilters && (
                <>
                  <ResizableHandle withHandle />
                  <ResizablePanel defaultSize={22} minSize={18} maxSize={40}>
                    <div className="h-full flex flex-col">
                      <PropertyFilters
                        searchParams={searchParams}
                        onFilterChange={handleFilterChange}
                        onClearAll={clearAllFilters}
                        isOpen={showFilters}
                        onClose={closeFilters}
                        className="flex-1 overflow-auto scrollbar-hide"
                      />
                    </div>
                  </ResizablePanel>
                </>
              )}
            </ResizablePanelGroup>
          </div>

          {/* Mobile Layout */}
          <div className="lg:hidden w-full h-full">
            <div className="h-full flex flex-col">
              {/* Scrollable Property List */}
              <div className="flex-1 overflow-auto scrollbar-hide">
                <div className="p-4">
                  {isError ? (
                    <div className="rounded-lg border border-destructive bg-destructive/10 p-8 text-center">
                      <h3 className="text-lg font-medium mb-2">Lỗi khi tải bất động sản</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        {error instanceof Error ? error.message : 'Đã xảy ra lỗi không xác định'}
                      </p>
                      <Button variant="outline" onClick={() => refetch()}>
                        Thử lại
                      </Button>
                    </div>
                  ) : isLoading && !properties.length ? (
                    <LoadingSkeleton />
                  ) : properties.length === 0 ? (
                    <EmptyState hasFilters={hasActiveFilters} />
                  ) : (
                    <>
                      <div
                        className={cn(
                          'gap-3 mb-6',
                          viewMode === 'grid'
                            ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 2xl:grid-cols-3'
                            : 'flex flex-col space-y-3'
                        )}
                      >
                        {properties.map((property: Property) => (
                          <PropertyCard
                            key={property.id}
                            property={property}
                            viewMode={viewMode}
                            onDelete={() => handleDeleteClick(property.id)}
                          />
                        ))}
                      </div>

                      {totalItems > searchParams.pageSize! && (
                        <PropertyPagination
                          searchParams={searchParams}
                          onFilterChange={handleFilterChange}
                          totalItems={totalItems}
                          isLoading={isLoading || isFetching}
                        />
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Filter Sheet */}
      <Sheet open={mobileSheetOpen} onOpenChange={setMobileSheetOpen}>
        <SheetContent side="right" className="w-[320px] sm:w-[400px] p-0">
          <SheetHeader className="p-4 border-b">
            <SheetTitle>Bộ lọc bất động sản</SheetTitle>
          </SheetHeader>
          <div className="flex-1 overflow-hidden">
            <PropertyFilters
              searchParams={searchParams}
              onFilterChange={handleFilterChange}
              onClearAll={clearAllFilters}
              isOpen={true}
              onClose={closeMobileFilters}
              className="h-full"
            />
          </div>
        </SheetContent>
      </Sheet>

      {propertyToDelete && (
        <DeletePropertyDialog
          propertyId={propertyToDelete}
          isOpen={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          onConfirm={handleDeleteConfirm}
        />
      )}
    </div>
  );
});
