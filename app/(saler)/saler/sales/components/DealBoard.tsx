'use client';

import DealColumn, { DealColumnProps } from './DealColumn';
import React, { useEffect, useState, useCallback } from 'react';
import {
  closestCorners,
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { toast } from 'sonner';
import DealCard from './DealCard';
import { Deal, DealSearchParams } from '@/lib/api/services/fetchDeal';
import { useUpdateDealVer2 } from '@/hooks/useDeals';
import { columnConfig } from '../config/configuration';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface DealBoardProps {
  data: { deals: Deal[]; count: number } | undefined;
  isLoading: boolean;
  error: Error | null;
  filters: DealSearchParams;
  pagination: { pageNumber: number; pageSize: number };
  handleFilterChange: (key: keyof DealSearchParams, value: string | boolean) => void;
  handlePageChange: (page: number) => void;
  handlePageSizeChange: (size: number) => void;
}

// Component Board (Bảng Kanban)
export default function DealBoard({
  data,
  isLoading,
  error,
  pagination,
  handlePageChange,
}: DealBoardProps) {
  const { mutate: updateDeal } = useUpdateDealVer2();

  const [columns, setColumns] = useState<DealColumnProps[]>(
    columnConfig.map(col => ({ ...col, deals: [] as Deal[] }))
  );
  const [originalColumnId, setOriginalColumnId] = useState<string | null>(null);
  const [activeDealForOverlay, setActiveDealForOverlay] = useState<Deal>();
  // const [isDragging, setIsDragging] = useState(false);
  // const [isUpdatingFromDrag, setIsUpdatingFromDrag] = useState(false);

  // Memoize categorizeDeals function
  const categorizeDeals = useCallback(() => {
    if (!data?.deals) {
      return;
    }

    // Initialize columns with empty deals
    const newColumns = columnConfig.map(col => ({ ...col, deals: [] as Deal[] }));

    // Categorize deals into columns
    data.deals.forEach(deal => {
      const columnIndex = newColumns.findIndex(col => col.id === deal.status);
      if (columnIndex !== -1) {
        newColumns[columnIndex].deals.push(deal);
      }
    });

    // Sort deals within each column by position
    newColumns.forEach(column => {
      column.deals.sort((a, b) => (a.position || 0) - (b.position || 0));
    });
    setColumns(newColumns);
  }, [data?.deals]);

  // Categorize deals when data changes
  useEffect(() => {
    categorizeDeals();
  }, [categorizeDeals]);

  // Find column containing deal or target column
  const findColumn = (unique: string | null) => {
    if (!unique) return null;

    // If unique is a column ID, return that column
    const columnById = columns.find(c => c.id === unique);
    if (columnById) {
      return columnById;
    }

    // If unique is a deal ID, find the column containing that deal
    const id = String(unique);
    for (const column of columns) {
      const dealInColumn = column.deals.find(deal => deal.id === id);
      if (dealInColumn) {
        return column;
      }
    }

    return null;
  };

  // Handle drag start event
  const handleDragStart = (event: DragStartEvent) => {
    // setIsDragging(true);
    const activeId = String(event.active.id);
    const activeColumn = findColumn(activeId);
    if (activeColumn) {
      setOriginalColumnId(activeColumn.id);
      const dealObj = activeColumn.deals.find((d: Deal) => d.id === activeId);
      setActiveDealForOverlay(dealObj);
    }
  };

  // Handle drag over between columns
  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    if (!over) return;

    const activeId = String(active.id);
    const overId = String(over.id);

    // Find source column and target column
    const activeColumn = findColumn(activeId);
    const overColumn = findColumn(overId);

    if (!activeColumn || !overColumn) return;

    // If dragging within the same column
    if (activeColumn.id === overColumn.id) {
      setColumns(prevState => {
        const column = prevState.find(c => c.id === activeColumn.id);
        if (!column) return prevState;

        const activeIndex = column.deals.findIndex(i => i.id === activeId);
        const overIndex = column.deals.findIndex(i => i.id === overId);
        if (activeIndex === overIndex) return prevState;

        const newDeals = arrayMove(column.deals, activeIndex, overIndex);

        return prevState.map(c => {
          if (c.id === activeColumn.id) {
            return { ...c, deals: newDeals };
          }
          return c;
        });
      });
      return;
    }

    // If dragging between different columns
    setColumns(prevState => {
      const activeItems = activeColumn.deals;
      const overItems = overColumn.deals;
      const activeIndex = activeItems.findIndex(i => i.id === activeId);

      // Check if overId exists in target column
      const isOverAcolumn = overItems.some(d => d.id === overId);
      const overIndex = isOverAcolumn ? -1 : overItems.findIndex(i => i.id === overId);

      // Create new deals list for source column
      const newActiveDeals = activeItems.filter(i => i.id !== activeId);

      // Create moved deal with new status
      const movedDeal = { ...activeItems[activeIndex], status: overColumn.id };

      // Create new deals list for target column
      const newOverDeals = [...overColumn.deals];

      // Determine insert position
      let insertIndex = 0;

      if (isOverAcolumn) {
        // If dropping into empty column or at the start
        insertIndex = 0;
      } else if (overIndex !== -1) {
        // If dropping between deals
        insertIndex = overIndex + 1;
      }

      // Insert deal at determined position
      newOverDeals.splice(insertIndex, 0, movedDeal);

      return prevState.map(c => {
        if (c.id === activeColumn.id) {
          return { ...c, deals: newActiveDeals };
        } else if (c.id === overColumn.id) {
          return { ...c, deals: newOverDeals };
        } else {
          return c;
        }
      });
    });
  };

  // Handle drag end event
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    // setIsDragging(false);

    if (!over || !originalColumnId) {
      setOriginalColumnId(null);
      return;
    }

    const activeId = String(active.id);
    const overId = String(over.id);

    // Determine target column using CURRENT columns state (not original)
    let overColumn = columns.find(col => col.id === overId);

    // If over.id is not a column, find the column containing the deal
    if (!overColumn) {
      overColumn = columns.find(col => col.deals.some(deal => deal.id === overId));
    }

    if (!overColumn) {
      setOriginalColumnId(null);
      return;
    }

    if (originalColumnId === overColumn.id) {
      // Same column reordering - use CURRENT column state, not original
      const currentColumn = columns.find(c => c.id === originalColumnId);

      if (currentColumn) {
        // Use the CURRENT visual order to find the final position
        const finalPosition = currentColumn.deals.findIndex(deal => deal.id === activeId);

        if (finalPosition !== -1) {
          try {
            // setIsUpdatingFromDrag(true);
            updateDeal(
              {
                id: activeId,
                position: finalPosition,
              },
              {
                onSuccess: response => {
                  if (response.status) {
                    toast.success('Deal position updated successfully.');
                  }
                  //setIsUpdatingFromDrag(false);
                },
                onError: _error => {
                  toast.error('Failed to update deal position');
                  categorizeDeals();
                  //setIsUpdatingFromDrag(false);
                },
              }
            );
          } catch (error) {
            console.error('Error updating deal position:', error);
            categorizeDeals();
            //setIsUpdatingFromDrag(false);
          }
        }
      }
    } else {
      // Different columns
      try {
        //setIsUpdatingFromDrag(true);

        // Calculate position in target column
        let newPosition = 0;

        if (over.data.current?.type === 'deal') {
          // Dropped on a deal
          const targetDealIndex = overColumn.deals.findIndex(deal => deal.id === overId);
          newPosition = targetDealIndex >= 0 ? targetDealIndex : 0;
        } else if (over.data.current?.sortable?.index !== undefined) {
          // Use sortable index
          newPosition = over.data.current.sortable.index;
        } else {
          // Dropped on column - add to end
          newPosition = overColumn.deals.length;
        }

        updateDeal(
          { id: activeId, status: overColumn.id, position: newPosition },
          {
            onSuccess: response => {
              if (response.status) {
                toast.success('Deal status updated successfully.');
              }
              //setIsUpdatingFromDrag(false);
            },
            onError: _error => {
              toast.error('Failed to update deal status');
              categorizeDeals();
              //setIsUpdatingFromDrag(false);
            },
          }
        );
      } catch (error) {
        console.error('Error updating deal status:', error);
        categorizeDeals();
        //setIsUpdatingFromDrag(false);
      }
    }

    setOriginalColumnId(null);
  };

  // Handle editing a deal
  const handleEditDeal = async (deal: Deal) => {
    try {
      console.log('Editing deal:', deal);
      toast.info('Edit functionality will be implemented soon.');
    } catch (error) {
      console.error('Error editing deal:', error);
      toast.error('Failed to edit deal. Please try again.');
    }
  };

  // Handle deleting a deal
  const handleDeleteDeal = async (dealId: string) => {
    try {
      console.log('Deleting deal:', dealId);

      // Remove the deal from the columns
      setColumns(prevColumns => {
        return prevColumns.map(column => {
          return {
            ...column,
            deals: column.deals.filter(deal => deal.id !== dealId),
          };
        });
      });

      toast.success('Deal has been deleted successfully.');
    } catch (error) {
      console.error('Error deleting deal:', error);
      toast.error('Failed to delete deal. Please try again.');
    }
  };

  // Configure drag sensors
  const pointerSensor = useSensor(PointerSensor, {
    activationConstraint: {
      // Only activate drag if moved > 5px
      distance: 5,
    },
  });
  const sensors = useSensors(
    pointerSensor,
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <p className="text-muted-foreground">Loading deals...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <p className="text-red-500">Error loading deals. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="flex-1 h-full overflow-x-hidden">
          <div className="overflow-x-auto h-full">
            <div className="inline-flex space-x-8 h-full px-4 py-2">
              {columns.map(column => (
                <DealColumn
                  key={column.id}
                  id={column.id}
                  title={column.title}
                  deals={column.deals}
                  onEditDeal={handleEditDeal}
                  onDeleteDeal={async (dealId: string) => await handleDeleteDeal(dealId)}
                />
              ))}
              {/* DragOverlay will portal to body, not affected by overflow */}
              <DragOverlay>
                {activeDealForOverlay ? (
                  <DealCard
                    deal={activeDealForOverlay}
                    // Pass props to simulate draggable but not listen to events
                  />
                ) : null}
              </DragOverlay>
            </div>
          </div>
        </div>
      </DndContext>

      {/* Pagination */}
      {data && Math.ceil(data.count / pagination.pageSize) > 1 && (
        <div className="flex justify-center mt-6 pb-4">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(pagination.pageNumber - 1)}
                  className={
                    pagination.pageNumber === 1
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>

              {Array.from(
                { length: Math.ceil(data.count / pagination.pageSize) },
                (_, i) => i + 1
              ).map(page => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => handlePageChange(page)}
                    isActive={pagination.pageNumber === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(pagination.pageNumber + 1)}
                  className={
                    pagination.pageNumber === Math.ceil(data.count / pagination.pageSize)
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
