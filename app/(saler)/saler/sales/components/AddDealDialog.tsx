'use client';
import * as React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { DealPriority, CreateDealRequest } from '@/lib/api/services/fetchDeal';
import { useLeads, useSellerLeads } from '@/hooks/useLead';
import { Lead } from '@/lib/api/services/fetchLead';
import { priorityConfig } from '../config/configuration';

// Create conditional schema based on user role
const createFormSchema = (isAdmin: boolean) => {
  const baseSchema = {
    leadId: z.string().min(1, 'Lead ID is required'),
    title: z.string().min(1, 'Title is required'),
    description: z.string().min(1, 'Description is required'),
    priority: z.nativeEnum(DealPriority, {
      required_error: 'Please select a priority level',
    }),
  };

  // Only require salesRepId for admin users
  if (isAdmin) {
    return z.object({
      ...baseSchema,
      salesRepId: z.string().min(1, 'Sales Rep ID is required'),
    });
  }

  return z.object({
    ...baseSchema,
    salesRepId: z.string().optional(), // Optional for sellers
  });
};

type FormValues = {
  leadId: string;
  salesRepId?: string;
  title: string;
  description: string;
  priority: DealPriority;
};

interface AddDealDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateDealRequest) => Promise<void>;
  isPending?: boolean;
  userRole: 'admin' | 'seller'; // Add user role prop
}

export function AddDealDialog({
  open,
  onOpenChange,
  onSubmit,
  isPending,
  userRole,
}: AddDealDialogProps) {
  const [openCombobox, setOpenCombobox] = React.useState(false);
  const [openSalesRepCombobox, setOpenSalesRepCombobox] = React.useState(false);
  const [selectedLead, setSelectedLead] = React.useState<Lead | null>(null);

  const isAdmin = userRole === 'admin';
  const formSchema = createFormSchema(isAdmin);

  // Use different hooks based on user role
  const adminLeadsQuery = useLeads();
  const sellerLeadsQuery = useSellerLeads();

  // Select the appropriate query result
  const {
    leads,
    isLoading: leadsLoading,
    isError: leadsError,
    error: leadsErrorData,
    refetch: refetchLeads,
  } = isAdmin ? adminLeadsQuery : sellerLeadsQuery;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      leadId: '',
      salesRepId: isAdmin ? '' : undefined,
      title: '',
      description: '',
      priority: DealPriority.Medium,
    },
  });

  // Handle error state
  React.useEffect(() => {
    if (leadsError && open) {
      console.error('Error fetching leads:', leadsErrorData);
      toast.error('Failed to load leads. Please try again.');
    }
  }, [leadsError, leadsErrorData, open, toast]);

  // Refetch leads when dialog opens if there was an error
  React.useEffect(() => {
    if (open && leadsError) {
      refetchLeads();
    }
  }, [open, leadsError, refetchLeads]);

  // Handle lead selection
  const handleLeadSelect = (leadId: string) => {
    const selectedLead = leads.find((lead: Lead) => lead.id === leadId);
    if (selectedLead) {
      setSelectedLead(selectedLead);
      form.setValue('leadId', selectedLead.id);

      if (isAdmin) {
        // Reset salesRepId when changing leads (admin only)
        form.setValue('salesRepId', '');

        // Auto-select the first sales rep if available
        if (selectedLead.assignedTo && selectedLead.assignedTo.length > 0) {
          form.setValue('salesRepId', selectedLead.assignedTo[0].id);
        }
      }

      // Auto-generate title
      form.setValue('title', `Deal for ${selectedLead.name}`);
    }
  };

  // Get sales rep name by ID (admin only)
  const getSalesRepName = (id: string) => {
    if (!isAdmin || !selectedLead || !id) return 'Select sales rep...';
    const salesRep = selectedLead.assignedTo?.find(rep => rep.id === id);
    return salesRep ? salesRep.name : 'Select sales rep...';
  };

  // Reset form when dialog closes
  React.useEffect(() => {
    if (!open) {
      form.reset({
        leadId: '',
        salesRepId: isAdmin ? '' : undefined,
        title: '',
        description: '',
        priority: DealPriority.Medium,
      });
      setSelectedLead(null);
      setOpenCombobox(false);
      setOpenSalesRepCombobox(false);
    }
  }, [open, form, isAdmin]);

  async function handleSubmit(data: FormValues) {
    try {
      // For sellers, don't include salesRepId in the request
      const submitData = isAdmin
        ? (data as CreateDealRequest)
        : ({
            leadId: data.leadId,
            title: data.title,
            description: data.description,
            priority: data.priority,
          } as CreateDealRequest);

      await onSubmit(submitData);
      form.reset();
      setSelectedLead(null);
      onOpenChange(false);
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Deal</DialogTitle>
          <DialogDescription>
            {isAdmin
              ? 'Create a new deal by selecting a lead and assigning a sales representative.'
              : 'Create a new deal from your assigned leads.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="leadId"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Select Lead</FormLabel>
                  <Popover open={openCombobox} onOpenChange={setOpenCombobox} modal={true}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={openCombobox}
                          className="w-full justify-between"
                          disabled={leadsLoading}
                        >
                          {leadsLoading ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Loading leads...
                            </>
                          ) : field.value ? (
                            leads.find((lead: Lead) => lead.id === field.value)?.name
                          ) : (
                            'Select lead...'
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[400px] p-0">
                      <Command>
                        <CommandInput placeholder="Search leads..." />
                        <CommandEmpty>
                          {leadsLoading ? 'Loading leads...' : 'No lead found.'}
                        </CommandEmpty>
                        <CommandGroup className="max-h-[300px] overflow-y-auto">
                          {leadsLoading ? (
                            <div className="flex items-center justify-center p-4">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span className="ml-2">Loading leads...</span>
                            </div>
                          ) : leadsError ? (
                            <div className="p-4 text-center">
                              <p className="text-sm text-red-500 mb-2">Failed to load leads</p>
                              <Button size="sm" variant="outline" onClick={() => refetchLeads()}>
                                Retry
                              </Button>
                            </div>
                          ) : (
                            leads.map((lead: Lead) => (
                              <CommandItem
                                key={lead.id}
                                value={lead.name}
                                onSelect={selectedValue => {
                                  const foundLead = leads.find(
                                    (l: Lead) =>
                                      l.name.toLowerCase() === selectedValue.toLowerCase()
                                  );
                                  if (foundLead) {
                                    handleLeadSelect(foundLead.id);
                                  }
                                  setOpenCombobox(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    'mr-2 h-4 w-4',
                                    lead.id === field.value ? 'opacity-100' : 'opacity-0'
                                  )}
                                />
                                <div className="flex flex-col">
                                  <span className="font-medium">{lead.name}</span>
                                  <span className="text-sm text-gray-500">{lead.email}</span>
                                </div>
                              </CommandItem>
                            ))
                          )}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                  <div className="text-xs text-gray-500">
                    {leadsLoading
                      ? 'Loading leads...'
                      : leadsError
                        ? 'Error loading leads'
                        : `${leads.length} ${isAdmin ? 'lead(s)' : 'assigned lead(s)'} available`}
                  </div>
                </FormItem>
              )}
            />

            {/* Sales Rep Selection - Only show for admin */}
            {isAdmin && (
              <FormField
                control={form.control}
                name="salesRepId"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Sales Rep</FormLabel>
                    <Popover
                      open={openSalesRepCombobox}
                      onOpenChange={setOpenSalesRepCombobox}
                      modal={true}
                    >
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={openSalesRepCombobox}
                            className="w-full justify-between"
                            disabled={
                              leadsLoading ||
                              !selectedLead ||
                              !selectedLead.assignedTo ||
                              selectedLead.assignedTo.length === 0
                            }
                          >
                            {getSalesRepName(field.value || '')}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-[400px] p-0">
                        <Command>
                          <CommandInput placeholder="Search sales reps..." />
                          <CommandEmpty>No sales rep found.</CommandEmpty>
                          <CommandGroup className="max-h-[300px] overflow-y-auto">
                            {selectedLead?.assignedTo?.map(salesRep => (
                              <CommandItem
                                key={salesRep.id}
                                value={salesRep.name}
                                onSelect={selectedValue => {
                                  const foundSalesRep = selectedLead.assignedTo.find(
                                    rep => rep.name.toLowerCase() === selectedValue.toLowerCase()
                                  );
                                  if (foundSalesRep) {
                                    form.setValue('salesRepId', foundSalesRep.id);
                                  }
                                  setOpenSalesRepCombobox(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    'mr-2 h-4 w-4',
                                    salesRep.id === field.value ? 'opacity-100' : 'opacity-0'
                                  )}
                                />
                                <div className="flex flex-col">
                                  <span className="font-medium">{salesRep.name}</span>
                                  <span className="text-sm text-gray-500">{salesRep.email}</span>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                    {selectedLead && (
                      <div className="text-xs text-gray-500">
                        {selectedLead.assignedTo?.length || 0} sales rep(s) available
                      </div>
                    )}
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter deal title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter deal description"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Priority</FormLabel>
                  <FormControl>
                    <div className="flex space-x-2">
                      {Object.entries(priorityConfig.badge).map(([key, config]) => (
                        <Button
                          key={key}
                          type="button"
                          variant={field.value === key ? 'default' : 'outline'}
                          size="sm"
                          className={cn(
                            'w-full',
                            field.value === key ? config.className : 'bg-transparent'
                          )}
                          onClick={() => field.onChange(key as DealPriority)}
                        >
                          {config.label}
                        </Button>
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isPending || leadsLoading}>
                {isPending ? 'Creating...' : 'Create Deal'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
