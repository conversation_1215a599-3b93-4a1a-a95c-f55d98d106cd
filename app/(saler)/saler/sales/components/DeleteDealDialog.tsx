// components/DeleteDealDialog.tsx
import { useEffect } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Deal } from '@/lib/api/services/fetchDeal';
import { Loader2 } from 'lucide-react';

interface DeleteDealDialogProps {
  deal: Deal | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting?: boolean;
}

export function DeleteDealDialog({
  deal,
  open,
  onOpenChange,
  onConfirm,
  isDeleting = false,
}: DeleteDealDialogProps) {
  // Force cleanup on unmount
  useEffect(() => {
    return () => {
      if (open) {
        onOpenChange(false);
      }
    };
  }, [onOpenChange, open]);

  // Emergency escape hatch
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open) {
        e.preventDefault();
        e.stopPropagation();
        onOpenChange(false);
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEscape, true);
      return () => {
        document.removeEventListener('keydown', handleEscape, true);
      };
    }
  }, [open, onOpenChange]);

  if (!deal) return null;

  const handleOpenChange = (newOpen: boolean) => {
    // Always allow closing
    onOpenChange(newOpen);
  };

  const handleCancel = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();
    onOpenChange(false);
  };

  const handleConfirm = async (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();
    if (!isDeleting) {
      await onConfirm();
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent
        className="sm:max-w-[425px]"
        onEscapeKeyDown={e => {
          e.preventDefault();
          onOpenChange(false);
        }}
      >
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Deal</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete &quot;{deal.title || 'this deal'}&quot;? This action
            cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={isDeleting}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
