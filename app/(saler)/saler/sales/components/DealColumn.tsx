'use client';
import React from 'react';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useDroppable } from '@dnd-kit/core';
import DealCard from './DealCard';
import { Deal } from '@/lib/api/services/fetchDeal';
import { Plus, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { statusConfig } from '../config/configuration';

export interface DealColumnProps {
  id: string;
  title: string;
  deals: Deal[];
  onEditDeal?: (deal: Deal) => void;
  onDeleteDeal?: (dealId: string) => void;
  onAddDeal?: (status: string) => void;
  isDragging?: boolean;
}

// Component Column (Cột giai đo<PERSON>n)
const DealColumn: React.FC<DealColumnProps> = ({
  id,
  title,
  deals,
  onEditDeal,
  onDeleteDeal,
  onAddDeal,
  isDragging,
}) => {
  const { setNodeRef } = useDroppable({ id });

  // Get header configuration from statusConfig
  const headerConfig = statusConfig[id as keyof typeof statusConfig] || {
    label: title.toUpperCase(),
    board: {
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-700',
      dotColor: 'bg-gray-500',
    },
  };

  const dealIds = deals.map(deal => deal.id);

  return (
    <div className="w-72 flex-shrink-0 bg-gray-50 rounded-lg">
      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${headerConfig.board.dotColor}`} />
            <h2
              className={`text-sm font-semibold uppercase tracking-wide ${headerConfig.board.textColor}`}
            >
              {headerConfig.label}
            </h2>
            <span className="inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-medium text-gray-600 bg-gray-200 rounded">
              {deals.length}
            </span>
          </div>

          <div className="flex items-center gap-1">
            {/* Add Task Button */}
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-gray-500 hover:text-gray-700 hover:bg-gray-200"
              onClick={() => onAddDeal?.(id)}
            >
              <Plus className="h-4 w-4" />
            </Button>

            {/* More Options */}
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-gray-500 hover:text-gray-700 hover:bg-gray-200"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => onAddDeal?.(id)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Deal
                </DropdownMenuItem>
                <DropdownMenuItem>Edit Column</DropdownMenuItem>
                <DropdownMenuItem>Archive Column</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Cards Container */}
      <SortableContext items={dealIds} strategy={verticalListSortingStrategy}>
        <div
          className={[
            'p-3 space-y-3 min-h-[400px]',
            isDragging ? 'overflow-visible' : 'overflow-y-auto max-h-[calc(100vh-200px)]',
          ].join(' ')}
          ref={setNodeRef}
        >
          {deals.map((deal: Deal, index) => (
            <DealCard
              index={index}
              key={deal.id}
              deal={deal}
              onEdit={onEditDeal}
              onDelete={onDeleteDeal}
            />
          ))}

          {/* Add Task Button at bottom */}
          <Button
            variant="ghost"
            className="w-full justify-start text-gray-500 hover:text-gray-700 hover:bg-gray-100 border-2 border-dashed border-gray-200 hover:border-gray-300 h-auto py-3"
            onClick={() => onAddDeal?.(id)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Deal
          </Button>
        </div>
      </SortableContext>
    </div>
  );
};

export default DealColumn;
