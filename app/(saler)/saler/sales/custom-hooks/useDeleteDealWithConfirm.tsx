// hooks/useDeleteDealWithConfirm.ts
import { useState, useCallback } from 'react';
import { Deal } from '@/lib/api/services/fetchDeal';
import { useDeleteDeal } from '@/hooks/useDeals';

export function useDeleteDealWithConfirm() {
  const [dealToDelete, setDealToDelete] = useState<Deal | null>(null);
  const deleteDeal = useDeleteDeal();

  const handleDeleteClick = useCallback((deal: Deal, e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setDealToDelete(deal);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!dealToDelete) return;

    try {
      await deleteDeal.mutateAsync(dealToDelete.id);
      // Force close dialog
      setDealToDelete(null);
    } catch (error) {
      console.error('Failed to delete deal:', error);
      // Don't close on error
    }
  }, [dealToDelete, deleteDeal]);

  const closeDeleteDialog = useCallback(() => {
    // Force close regardless of state
    setDealToDelete(null);
  }, []);

  return {
    dealToDelete,
    handleDeleteClick,
    confirmDelete,
    closeDeleteDialog,
    isDeleting: deleteDeal.isPending,
  };
}
