'use client';

import {
  Property,
  TransactionType,
  PropertyType,
  PropertyStatus,
  Currency,
  PaymentMethod,
  ApartmentOrientation,
} from '@/lib/api/services/fetchProperty';

import Image from 'next/image';
import { PropertyCardAuth } from './property-card-auth';
import { motion } from 'framer-motion';
import { User } from 'lucide-react';

export default function AuthImage() {
  const mockProperty: Property = {
    id: 'mock-1',
    saler: {
      id: 'mock-saler',
      fullName: 'Nguy<PERSON><PERSON>n <PERSON>',
      email: 'nguy<PERSON><PERSON>@example.com',
      phoneNumber: '**********',
    },
    title: '<PERSON> cư cao cấp The Manor',
    name: 'Chung cư cao cấp The Manor',
    description: 'Chung cư cao cấp với view đẹp, tiện ích đầy đủ',
    type: PropertyType.VILLA,
    transactionType: TransactionType.FOR_SALE,
    status: PropertyStatus.AVAILABLE,
    adminNote: '',
    code: 'PROP001',
    location: {
      address: '123 <PERSON><PERSON><PERSON><PERSON>, Quận 1',
      city: 'TP.HCM',
      district: 'Quận 1',
      ward: 'Phường Bến Nghé',
      latitude: 10.7769,
      longitude: 106.7009,
    },
    propertyDetails: {
      bedrooms: 3,
      bathrooms: 2,
      livingRooms: 1,
      kitchens: 1,
      landArea: 0,
      landWidth: 0,
      landLength: 0,
      buildingArea: 85,
      numberOfFloors: 15,
      hasBasement: false,
      floorNumber: 8,
      apartmentOrientation: ApartmentOrientation.SOUTHEAST,
      furnished: true,
    },
    priceDetails: {
      salePrice: **********, // 2.5 tỷ
      rentalPrice: 0,
      pricePerSquareMeter: ********,
      currency: Currency.VND,
      depositAmount: 0,
      maintenanceFee: 500000,
      paymentMethods: [PaymentMethod.CASH, PaymentMethod.BANK_TRANSFER],
    },
    amenities: {
      parking: true,
      elevator: true,
      swimmingPool: true,
      gym: true,
      securitySystem: true,
      airConditioning: true,
      balcony: true,
      garden: false,
      playground: true,
      backupGenerator: true,
    },
    imageUrls: ['/bg_hero.jpg', '/banner_auth.jpg', '/hero.jpg'],
    floorPlanUrls: [],
    videoUrls: [],
    yearBuilt: 2023,
    legalDocumentUrls: [],
    transactionHistory: [],
    isFeatured: true,
    isVerified: true,
    contactName: 'Nguyễn Văn A',
    contactPhone: '**********',
    contactEmail: '<EMAIL>',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isFavorite: false,
  };

  return (
    <div className="relative hidden bg-white lg:block lg:col-span-1">
      <div className="absolute inset-4 rounded-xl overflow-hidden">
        <Image
          src="/banner_auth.jpg"
          alt="Image"
          className="h-full w-full object-cover object-center"
          fill
          sizes="(max-width: 768px) 100vw, 50vw"
          priority
        />
      </div>

      {/* Messenger-style chat overlay */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none p-4">
        <motion.div
          className="space-y-2 max-w-sm w-full mx-2 xl:max-w-md xl:mx-4 2xl:max-w-lg 2xl:mx-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {/* First message */}
          <motion.div
            className="flex items-start gap-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="size-8 xl:size-10 rounded-full bg-white flex items-center justify-center text-white text-xs font-semibold flex-shrink-0">
              <Image
                src="/logo_revoland_black.png"
                alt="Revoland icon"
                width={24}
                height={24}
                className="rounded-md xl:w-7 xl:h-7"
                priority
              />
            </div>
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl rounded-tl-md px-3 py-2 xl:px-4 xl:py-3 shadow-lg">
              <p className="text-xs xl:text-sm text-gray-800">
                Chào bạn! Tôi có thể giúp gì cho bạn hôm nay?
              </p>
            </div>
          </motion.div>

          {/* Second message */}
          <motion.div
            className="flex items-start gap-2 justify-end"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1 }}
          >
            <div className="bg-red-500/90 backdrop-blur-sm rounded-2xl rounded-tr-md px-3 py-2 xl:px-4 xl:py-3 shadow-lg">
              <p className="text-xs xl:text-sm text-white">Tôi muốn tìm một căn hộ phù hợp</p>
            </div>
            <div className="size-8 xl:size-10 rounded-full bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center text-white text-xs font-semibold flex-shrink-0">
              <User className="size-3 xl:size-4" />
            </div>
          </motion.div>

          {/* Third message with property card */}
          <motion.div
            className="flex items-start gap-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1.6 }}
          >
            <div className="size-8 xl:size-10 rounded-full bg-white flex items-center justify-center text-white text-xs font-semibold flex-shrink-0">
              <Image
                src="/logo_revoland_black.png"
                alt="Revoland icon"
                width={24}
                height={24}
                className="rounded-md xl:w-7 xl:h-7"
                priority
              />
            </div>
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl rounded-tl-md px-3 py-2 xl:px-4 xl:py-3 shadow-lg">
              <p className="text-xs xl:text-sm text-gray-800">Hãy xem qua căn hộ này nhé</p>
            </div>
          </motion.div>

          {/* Fourth message with property card */}
          <motion.div
            className="flex items-start gap-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 2.2 }}
          >
            <div className="size-8 xl:size-10 rounded-full bg-white flex items-center justify-center text-white text-xs font-semibold flex-shrink-0">
              <Image
                src="/logo_revoland_black.png"
                alt="Revoland icon"
                width={24}
                height={24}
                className="rounded-md xl:w-7 xl:h-7"
                priority
              />
            </div>
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl rounded-tl-md p-1 xl:p-3 shadow-lg max-w-sm">
              <div className="pointer-events-auto w-48 xl:w-56 2xl:w-64">
                <PropertyCardAuth property={mockProperty} size="sm" priority={false} />
              </div>
            </div>
          </motion.div>

          {/* Fifth message */}
          <motion.div
            className="flex items-start gap-2 justify-end"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 2.8 }}
          >
            <div className="bg-red-500/90 backdrop-blur-sm rounded-2xl rounded-tr-md px-3 py-2 xl:px-4 xl:py-3 shadow-lg">
              <p className="text-xs xl:text-sm text-white">
                Tuyệt vời! Tôi muốn đặt lịch xem nhà này
              </p>
            </div>
            <div className="size-8 xl:size-10 rounded-full bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center text-white text-xs font-semibold flex-shrink-0">
              <User className="size-3 xl:size-4" />
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
