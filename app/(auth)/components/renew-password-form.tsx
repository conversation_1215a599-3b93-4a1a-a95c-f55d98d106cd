'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { useRenewPassword } from '@/hooks/useAuth';
import { useRouter, useSearchParams } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import Link from 'next/link';

const renewPasswordSchema = z
  .object({
    newPassword: z
      .string()
      .min(8, 'Mật khẩu phải có ít nhất 8 ký tự')
      .regex(/[A-Z]/, 'Mật khẩu phải có ít nhất 1 chữ cái viết hoa')
      .regex(/[a-z]/, '<PERSON><PERSON>t khẩu phải có ít nhất 1 chữ cái viết thường')
      .regex(/[0-9]/, '<PERSON>ật khẩu phải có ít nhất 1 số')
      .regex(/[^A-Za-z0-9]/, 'Mật khẩu phải có ít nhất 1 ký tự đặc biệt'),
    confirmPassword: z.string().min(1, 'Xác nhận mật khẩu là bắt buộc'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Mật khẩu không khớp',
    path: ['confirmPassword'],
  });

export function RenewPasswordForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const { renewPassword, isLoading } = useRenewPassword();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(renewPasswordSchema),
    mode: 'onChange',
  });

  // Validate token param, nếu không có thì redirect về /forgot-password
  React.useEffect(() => {
    if (!token) {
      router.push('/forgot-password');
    }
  }, [token, router]);

  const onSubmit = async (data: z.infer<typeof renewPasswordSchema>) => {
    try {
      if (!token) {
        router.push('/forgot-password');
        return;
      }

      await renewPassword({
        token,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
      });
    } catch (error) {
      console.error('Renew password error:', error);
      toast.error('Đã có lỗi xảy ra khi đổi mật khẩu. Vui lòng thử lại sau.');
    }
  };

  if (!token) {
    return null;
  }

  return (
    <div className="flex flex-col items-center justify-center font-mann">
      <div className="w-full space-y-6 rounded-xl">
        <div className="flex flex-col items-center gap-2 text-center">
          <h1 className="text-xl md:text-2xl font-bold tracking-tight text-red-500">
            Đặt lại mật khẩu
          </h1>
          <p className="text-balance text-xs md:text-sm text-muted-foreground">
            Nhập mật khẩu mới cho tài khoản của bạn
          </p>
        </div>

        <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="newPassword" className="text-xs md:text-sm">
                Mật khẩu mới
                <span className="text-destructive"> *</span>
              </Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  {...register('newPassword')}
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Nhập mật khẩu mới"
                  className={cn(
                    errors.newPassword && 'border-destructive pr-10 text-xs md:text-sm'
                  )}
                  aria-describedby="newPassword-error"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-8 w-8 px-0"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span className="sr-only">
                    {showPassword ? 'Ẩn mật khẩu' : 'Hiển thị mật khẩu'}
                  </span>
                </Button>
              </div>
              {errors.newPassword && (
                <p className="text-xs text-destructive" id="newPassword-error">
                  {errors.newPassword.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-xs md:text-sm">
                Xác nhận mật khẩu
                <span className="text-destructive"> *</span>
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  {...register('confirmPassword')}
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Nhập lại mật khẩu mới"
                  className={cn(
                    errors.confirmPassword && 'border-destructive pr-10 text-xs md:text-sm'
                  )}
                  aria-describedby="confirmPassword-error"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-8 w-8 px-0"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? 'Ẩn mật khẩu' : 'Hiển thị mật khẩu'}
                  </span>
                </Button>
              </div>
              {errors.confirmPassword && (
                <p className="text-xs text-destructive" id="confirmPassword-error">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>
          </div>

          <Button
            type="submit"
            className="w-full bg-red-500 hover:bg-red-600"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isLoading ? 'Đang đổi mật khẩu...' : 'Đổi mật khẩu'}
              </>
            ) : (
              'Đổi mật khẩu'
            )}
          </Button>
        </form>
        <div className="text-center">
          <p className="text-xs md:text-sm text-muted-foreground">
            Nhớ mật khẩu của bạn?{' '}
            <Link href="/login" className="text-primary hover:underline">
              Đăng nhập
            </Link>
          </p>
        </div>
        <div className="text-balance text-center text-xs md:text-sm text-muted-foreground">
          Bằng cách tiếp tục, bạn đồng ý với{' '}
          <a href="/terms" className="hover:text-primary hover:underline">
            Điều khoản, điều kiện
          </a>{' '}
          và{' '}
          <a href="/privacy" className="hover:text-primary hover:underline">
            Chính sách bảo mật
          </a>
          .
        </div>
      </div>
    </div>
  );
}
