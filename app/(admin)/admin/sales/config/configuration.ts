import { DealPriority } from '@/lib/api/services/fetchDeal';

// Priority configuration for different UI contexts
export const priorityConfig = {
  // For badges and labels
  badge: {
    High: { label: 'High', className: 'bg-red-500 text-white' },
    Medium: { label: 'Medium', className: 'bg-orange-500 text-white' },
    Low: { label: 'Low', className: 'bg-blue-500 text-white' },
  },
  // For cards and detailed views
  card: {
    HIGH: {
      label: 'HIGH',
      className: 'bg-red-100 text-red-700 border-red-200',
      dot: 'bg-red-500',
    },
    MEDIUM: {
      label: 'MEDIUM',
      className: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      dot: 'bg-yellow-500',
    },
    LOW: {
      label: 'LOW',
      className: 'bg-green-100 text-green-700 border-green-200',
      dot: 'bg-green-500',
    },
  },
  // For list view with icons
  list: {
    HIGH: { label: 'High', className: 'bg-red-500 text-white', icon: '🔥' },
    MEDIUM: { label: 'Medium', className: 'bg-orange-500 text-white', icon: '⚡' },
    LOW: { label: 'Normal', className: 'bg-blue-500 text-white', icon: '📋' },
  },
  // For form inputs and selects
  form: {
    [DealPriority.High]: { label: 'HIGH', className: 'bg-red-500 text-white' },
    [DealPriority.Medium]: { label: 'MEDIUM', className: 'bg-orange-500 text-white' },
    [DealPriority.Low]: { label: 'LOW', className: 'bg-blue-500 text-white' },
  },
} as const;

// Status configuration
export const statusConfig = {
  New: {
    label: 'NEW',
    className: 'bg-slate-200 text-slate-700',
    color: '#64748b',
    board: {
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-700',
      dotColor: 'bg-gray-500',
    },
  },
  Contacted: {
    label: 'CONTACTED',
    className: 'bg-blue-200 text-blue-700',
    color: '#3b82f6',
    board: {
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
      dotColor: 'bg-blue-500',
    },
  },
  Negotiation: {
    label: 'NEGOTIATION',
    className: 'bg-amber-200 text-amber-700',
    color: '#f59e0b',
    board: {
      bgColor: 'bg-amber-100',
      textColor: 'text-amber-700',
      dotColor: 'bg-amber-500',
    },
  },
  Closing: {
    label: 'CLOSING',
    className: 'bg-purple-200 text-purple-700',
    color: '#8b5cf6',
    board: {
      bgColor: 'bg-purple-100',
      textColor: 'text-purple-700',
      dotColor: 'bg-purple-500',
    },
  },
  Won: {
    label: 'WON',
    className: 'bg-green-200 text-green-700',
    color: '#10b981',
    board: {
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
      dotColor: 'bg-green-500',
    },
  },
  Lost: {
    label: 'LOST',
    className: 'bg-red-200 text-red-700',
    color: '#ef4444',
    board: {
      bgColor: 'bg-red-100',
      textColor: 'text-red-700',
      dotColor: 'bg-red-500',
    },
  },
} as const;

// Column configuration for board view
export const columnConfig = [
  { id: 'New', title: 'New', deals: [] },
  { id: 'Contacted', title: 'Contacted', deals: [] },
  { id: 'Negotiation', title: 'Negotiation', deals: [] },
  { id: 'Closing', title: 'Closing', deals: [] },
  { id: 'Won', title: 'Won', deals: [] },
  { id: 'Lost', title: 'Lost', deals: [] },
] as const;
