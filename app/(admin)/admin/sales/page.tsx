'use client';
import { But<PERSON> } from '@/components/ui/button';
import { SidebarInset } from '@/components/ui/sidebar';
import { useState } from 'react';
import DealBoard from './components/DealBoard';
import DealBoardList from './components/DealBoardList';
import { AddDealDialog } from './components/AddDealDialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { SiteHeader } from '@/components/common/siteHeader';
import Image from 'next/image';

import { ActionDealResponse, CreateDealRequest } from '@/lib/api/services/fetchDeal';
import DealTable from './components/DealTable';
import { useCreateDeal } from '@/hooks/useDeals';

import { DealFilters } from './components/DealFilters';
import { useDealsWithFilters } from './custom-hooks/useDealsWithFilters';

export default function Page() {
  const [isAddDealOpen, setIsAddDealOpen] = useState(false);
  const { mutate: createDeal, isPending } = useCreateDeal();
  const dealsWithFilters = useDealsWithFilters();

  const handleAddDeal = async (data: CreateDealRequest) => {
    createDeal(data, {
      onSuccess: (response: ActionDealResponse) => {
        toast.success(response.message);
        setIsAddDealOpen(false);
      },
      onError: (error: Error) => {
        const errorMessage = error.message || 'Something went wrong';
        toast.error(errorMessage);
      },
    });
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <SidebarInset>
        <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
          <SiteHeader title="Deals Management" />
        </header>
        <div className="flex flex-1 flex-col ">
          <div className="@container/main flex flex-1 flex-col">
            <div className="flex flex-col gap-2 md:gap-6 md:py-2">
              <div className="md:hidden">
                <Image
                  src="/hero.png"
                  width={1280}
                  height={998}
                  alt="Playground"
                  className="block dark:hidden"
                  priority
                />
                <Image
                  src="/hero.png"
                  width={1280}
                  height={998}
                  alt="Playground"
                  className="hidden dark:block"
                  priority
                />
              </div>
              <div className="hidden h-full flex-col space-y-8 md:flex">
                <div className="flex items-center justify-between space-y-2 p-4">
                  <div>
                    <h2 className="text-2xl font-bold tracking-tight">Deals Management</h2>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div>
                      <Button
                        onClick={() => {
                          setIsAddDealOpen(true);
                        }}
                      >
                        Add new deal
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex-1">
                <Tabs
                  defaultValue="board"
                  className="h-full"
                  onValueChange={value =>
                    dealsWithFilters.handleViewChange(value as 'board' | 'list' | 'table')
                  }
                >
                  <TabsList className="m-4">
                    <TabsTrigger value="board">Board View</TabsTrigger>
                    <TabsTrigger value="list">List View</TabsTrigger>
                    <TabsTrigger value="table">Table View</TabsTrigger>
                  </TabsList>

                  {/* Shared Filters */}
                  <div className="px-4">
                    <DealFilters
                      filters={dealsWithFilters.filters}
                      onFilterChange={dealsWithFilters.handleFilterChange}
                    />
                  </div>

                  <TabsContent value="board" className="flex-1 flex flex-col overflow-hidden">
                    <DealBoard {...dealsWithFilters} />
                  </TabsContent>
                  <TabsContent value="list" className="flex-1">
                    <DealBoardList {...dealsWithFilters} />
                  </TabsContent>
                  <TabsContent value="table" className="flex-1">
                    <DealTable {...dealsWithFilters} />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>
        </div>

        <AddDealDialog
          userRole="admin"
          open={isAddDealOpen}
          onOpenChange={setIsAddDealOpen}
          onSubmit={handleAddDeal}
          isPending={isPending}
        />
      </SidebarInset>
    </div>
  );
}
