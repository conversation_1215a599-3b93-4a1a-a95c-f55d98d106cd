import { useState } from 'react';

import {
  ChevronDown,
  ChevronUp,
  Plus,
  Calendar,
  User,
  MoreHorizontal,
  Bold,
  Italic,
  Underline,
  Link,
  Paperclip,
  Loader2,
} from 'lucide-react';
import { Deal, DealNote } from '@/lib/api/services/fetchDeal';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { useUpdateDealNote, useUpdateDealVer2 } from '@/hooks/useDeals';

interface NotesSectionProps {
  deal: Deal;
}

const NotesSection = ({ deal }: NotesSectionProps) => {
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [newNote, setNewNote] = useState({
    title: '',
    content: '',
  });

  // Use your existing hook
  const updateDealMutation = useUpdateDealVer2();
  const updateNoteMutation = useUpdateDealNote();

  const handleAddNote = async () => {
    if (!newNote.title.trim() || !newNote.content.trim()) return;

    try {
      await updateDealMutation.mutateAsync({
        id: deal.id,
        note: {
          title: newNote.title,
          content: newNote.content,
        },
      });

      // Reset form on success
      setNewNote({ title: '', content: '' });
      setIsAddingNote(false);
      toast.success('Note added successfully');
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error('Failed to add note');
    }
  };

  const handleUpdateNote = async (
    dealNoteId: string,
    noteData: { title: string; content: string }
  ) => {
    try {
      await updateNoteMutation.mutateAsync({
        dealId: deal.id,
        dealNoteId,
        ...noteData,
      });

      toast.success('Note updated successfully');
    } catch (error) {
      console.error('Error updating note:', error);
      toast.error('Note updated fail');
    }
  };

  return (
    <Card className="p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Notes</h3>
          <Button
            onClick={() => setIsAddingNote(!isAddingNote)}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            disabled={updateDealMutation.isPending}
          >
            <Plus className="h-4 w-4" />
            Add new note
          </Button>
        </div>

        {/* Add New Note Form */}
        <Collapsible open={isAddingNote} onOpenChange={setIsAddingNote}>
          <CollapsibleContent>
            <Card className="p-4 bg-gray-50 border-dashed">
              <div className="space-y-4">
                {/* Note Title */}
                <div>
                  <Input
                    placeholder="Note title (e.g., Summary Meeting 12 Jul, 2024)"
                    value={newNote.title}
                    onChange={e => setNewNote(prev => ({ ...prev, title: e.target.value }))}
                    className="font-medium"
                    disabled={updateDealMutation.isPending}
                  />
                </div>

                {/* Toolbar */}
                <div className="flex items-center gap-2 p-2 border rounded-md bg-white">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={updateDealMutation.isPending}
                  >
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={updateDealMutation.isPending}
                  >
                    <Italic className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={updateDealMutation.isPending}
                  >
                    <Underline className="h-4 w-4" />
                  </Button>
                  <div className="w-px h-6 bg-gray-300 mx-2"></div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={updateDealMutation.isPending}
                  >
                    <Link className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={updateDealMutation.isPending}
                  >
                    <Paperclip className="h-4 w-4" />
                  </Button>
                </div>

                {/* Note Content */}
                <Textarea
                  placeholder="This is |"
                  value={newNote.content}
                  onChange={e => setNewNote(prev => ({ ...prev, content: e.target.value }))}
                  className="min-h-[120px] resize-none"
                  disabled={updateDealMutation.isPending}
                />

                {/* Associated Records */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>Associated with 3 records</span>
                    <ChevronDown className="h-4 w-4" />
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddingNote(false);
                        setNewNote({ title: '', content: '' });
                      }}
                      disabled={updateDealMutation.isPending}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddNote}
                      disabled={
                        updateDealMutation.isPending ||
                        !newNote.title.trim() ||
                        !newNote.content.trim()
                      }
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {updateDealMutation.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        'Add note'
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </CollapsibleContent>
        </Collapsible>

        {/* Existing Notes */}
        <div className="space-y-4">
          {deal.notes && deal.notes.length > 0 ? (
            deal.notes.map(note => (
              <NoteCard
                key={note.id}
                note={note}
                onUpdateNote={noteData => handleUpdateNote(note.id, noteData)}
                isUpdating={updateDealMutation.isPending}
              />
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No notes yet. Add your first note to get started.</p>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

// Updated Note Card Component
interface NoteCardProps {
  note: DealNote;
  onUpdateNote: (noteData: { title: string; content: string }) => Promise<void>;
  isUpdating: boolean;
}

const NoteCard = ({ note, onUpdateNote, isUpdating }: NoteCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedNote, setEditedNote] = useState({
    title: note.title,
    content: note.content,
  });

  const handleSaveEdit = async () => {
    if (!editedNote.title.trim() || !editedNote.content.trim()) return;

    try {
      await onUpdateNote(editedNote);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating note:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditedNote({
      title: note.title,
      content: note.content,
    });
    setIsEditing(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card className="p-4 hover:shadow-md transition-shadow">
      <div className="space-y-3">
        {/* Note Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
                <CollapsibleTrigger className="flex items-center gap-2 hover:bg-gray-50 p-1 rounded">
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                  <span className="text-sm text-gray-600">Note by</span>
                  <span className="font-medium text-sm">{note.createdBy || 'Unknown'}</span>
                </CollapsibleTrigger>
              </Collapsible>
            </div>

            {isEditing ? (
              <Input
                value={editedNote.title}
                onChange={e => setEditedNote(prev => ({ ...prev, title: e.target.value }))}
                className="font-semibold mb-2"
                disabled={isUpdating}
              />
            ) : (
              <h4 className="font-semibold text-gray-900 mb-2">{note.title}</h4>
            )}

            {/* Note Preview */}
            {!isExpanded && !isEditing && (
              <div className="text-sm text-gray-700 line-clamp-2">{note.content}</div>
            )}
          </div>

          <div className="flex items-center gap-2 ml-4">
            <span className="text-xs text-gray-500">{formatDate(note.createdAt)}</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setIsEditing(!isEditing)}
              disabled={isUpdating}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Expanded Content */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent>
            <div className="pt-3 border-t space-y-3">
              {/* Full Content */}
              {isEditing ? (
                <div className="space-y-3">
                  <Textarea
                    value={editedNote.content}
                    onChange={e => setEditedNote(prev => ({ ...prev, content: e.target.value }))}
                    className="min-h-[120px] resize-none"
                    disabled={isUpdating}
                  />
                  <div className="flex gap-2 justify-end">
                    <Button variant="outline" onClick={handleCancelEdit} disabled={isUpdating}>
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSaveEdit}
                      disabled={
                        isUpdating || !editedNote.title.trim() || !editedNote.content.trim()
                      }
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isUpdating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Changes'
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-sm text-gray-700 whitespace-pre-wrap">{note.content}</div>
              )}

              {/* Key Points Section (if it's a meeting note) */}
              {!isEditing && note.title.toLowerCase() && (
                <div className="space-y-2">
                  <h5 className="font-medium text-sm">1. Key Points Discussed</h5>
                  <ul className="text-sm text-gray-700 space-y-1 ml-4">
                    <li>
                      • <strong>Products/Services of Interest:</strong> Premium Website Development
                      Package
                    </li>
                    <li>
                      • <strong>Specific Needs:</strong> Custom design with e-commerce integration
                    </li>
                    <li>
                      • <strong>Best Price Request:</strong> Looking for a competitive quote for the
                      package
                    </li>
                  </ul>
                </div>
              )}

              {/* Footer Actions */}
              {!isEditing && (
                <div className="flex items-center justify-between pt-2 border-t">
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <User className="h-4 w-4" />4 Comment
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {3} Associations
                    </span>
                  </div>

                  <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                    View Details
                  </Button>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </Card>
  );
};

export default NotesSection;
