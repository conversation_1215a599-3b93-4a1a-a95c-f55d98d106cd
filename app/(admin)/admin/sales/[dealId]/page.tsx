'use client';
import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ArrowLeft,
  MoreHorizontal,
  Calendar,
  Clock,
  User,
  FileText,
  Upload,
  Paperclip,
  Loader2,
  FileCheck,
  Flame,
  MessageCircle,
  Users,
  Settings,
  Globe,
  Edit2,
  Mail,
  Plus,
  Phone,
  Hash,
  ExternalLink,
  MapPin,
  Target,
  Shield,
  ContactRound,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';

import { Deal, DealPriority } from '@/lib/api/services/fetchDeal';
import { toast } from 'sonner';
import { formatTimeAgo } from '@/utils/dates/formatDate';
import NotesSection from './components/NotesSection';
import { useDeal, useUpdateDealVer2 } from '@/hooks/useDeals';
import DealLogs from './components/DealLogs';
import { priorityConfig, statusConfig } from '../config/configuration';
import { UserInfoTooltip } from '@/components/UserInfoTooltip';
import { SidebarInset } from '@/components/ui/sidebar';
import { SiteHeader } from '@/components/common/siteHeader';

// Mock activities - replace with your actual activity data
const mockActivities = [
  {
    id: '1',
    user: { name: 'Sales Manager', role: 'Manager', avatar: 'SM' },
    action: "The deal's pricing structure could benefit from a more flexible approach.",
    timestamp: '01 Day ago',
    type: 'comment',
  },
  {
    id: '2',
    user: { name: 'John White', role: 'Sales Rep', avatar: 'JW' },
    action:
      "Thank you for the suggestion! We're already exploring ways to enhance the pricing model.",
    timestamp: '50 minutes ago',
    type: 'comment',
  },
  {
    id: '3',
    user: { name: 'Current User', role: 'me', avatar: 'CU' },
    action: 'Please check now and give me feedback, if you have any improvement.',
    timestamp: '30 minutes ago',
    type: 'comment',
  },
  {
    id: '4',
    user: { name: 'Current User', role: 'me', avatar: 'CU' },
    action: 'You changed status from Contacted to Negotiation',
    timestamp: '06 minutes ago',
    type: 'status_change',
  },
];

export default function DealDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const dealId = params.dealId as string;

  // Keep your existing data fetching logic
  const { data, isLoading, error } = useDeal(dealId);
  const { mutate: updateDeal, isPending: isUpdating } = useUpdateDealVer2();
  const [deal, setDeal] = useState<Deal | null | undefined>(null);

  // Keep your existing state management
  const [activeTab, setActiveTab] = useState('customer');
  const [activityFilter, setActivityFilter] = useState('all');
  const [newComment, setNewComment] = useState('');
  const [description, setDescription] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    const fetchDealDetails = async () => {
      setDeal(data?.deal);
    };
    if (dealId) {
      fetchDealDetails();
    }
  }, [data?.deal, dealId]);
  // Update description when deal data loads
  useEffect(() => {
    if (deal?.description) {
      setDescription(deal.description);
    }
  }, [deal?.description]);

  // Keep your existing handlers - Fixed
  const handleStatusChange = async (newStatus: string) => {
    if (!deal) return;

    updateDeal(
      {
        id: dealId,
        status: newStatus,
      },
      {
        onSuccess: response => {
          if (response.status) {
            toast.success('Deal status updated successfully.');
          }
        },
        onError: _error => {
          toast.error('Failed to update deal status');
        },
      }
    );
  };

  const handlePriorityChange = async (newPriority: string) => {
    if (!deal) return;

    updateDeal(
      {
        id: dealId,
        priority: newPriority as DealPriority,
      },
      {
        onSuccess: response => {
          if (response.status) {
            toast.success('Deal priority updated successfully.');
          }
        },
        onError: _error => {
          toast.error('Failed to update deal priority');
        },
      }
    );
  };

  const handleDescriptionSave = async () => {
    if (!deal) return;

    updateDeal(
      {
        id: dealId,
        description: description,
      },
      {
        onSuccess: response => {
          if (response.status) {
            toast.success('Deal description updated successfully.');
            setIsEditing(false);
          }
        },
        onError: _error => {
          toast.error('Failed to update deal description');
        },
      }
    );
  };

  const handleFieldUpdate = async (field: string, value: unknown) => {
    if (!deal) return;

    updateDeal(
      {
        id: dealId,
        [field]: value,
      },
      {
        onSuccess: response => {
          if (response.status) {
            toast.success(`Deal ${field} updated successfully.`);
          }
        },
        onError: _error => {
          toast.error(`Failed to update deal ${field}`);
        },
      }
    );
  };
  const getSourceIcons = (source: string) => {
    const iconMap = {
      Zalo: (
        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
          Z
        </div>
      ),
      Facebook: (
        <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center text-white text-xs font-bold">
          f
        </div>
      ),
      Web: <Globe className="w-6 h-6 text-gray-600" />,
      Form: <FileText className="w-6 h-6 text-gray-600" />,
      Message: <MessageCircle className="w-6 h-6 text-gray-600" />,
      Appointment: <Calendar className="w-6 h-6 text-gray-600" />,
      ExternalSeller: <Users className="w-6 h-6 text-gray-600" />,
      Admin: <Settings className="w-6 h-6 text-gray-600" />,
    };

    return (
      <div className="flex items-center gap-2">
        {iconMap[source as keyof typeof iconMap] || <Hash className="w-6 h-6 text-gray-600" />}
        <span className="font-medium">{source}</span>
      </div>
    );
  };

  const filteredActivities = mockActivities.filter(activity => {
    if (activityFilter === 'all') return true;
    if (activityFilter === 'comment') return activity.type === 'comment';
    if (activityFilter === 'history') return activity.type !== 'comment';
    return true;
  });

  // Keep your existing loading and error states
  if (isLoading) {
    return (
      <div className="flex-1 flex flex-col h-screen items-center justify-center overflow-hidden">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading deal details...</span>
        </div>
      </div>
    );
  }

  if (error || !deal) {
    return (
      <div className="flex-1 flex flex-col h-screen items-center justify-center overflow-hidden">
        <div className="text-center">
          <p className="text-red-500 mb-4">Error loading deal details</p>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </div>
    );
  }
  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <SidebarInset>
        <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
          <SiteHeader title="Deals Management" />
        </header>
        {/* Fixed Header */}
        <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="hover:bg-gray-100"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">
                Deal Details - {deal.title || 'Untitled Deal'}
              </h1>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4 mr-2" />
                Action
              </Button>
            </div>
          </div>
        </div>

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto">
            <div className="flex gap-6 p-6">
              {/* Main Content */}
              <div className="flex-1 space-y-6">
                {/* Deal Properties - UPDATED: Display only, no inputs */}
                <Card className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                    {/* Status - Giữ nguyên vì cần edit */}
                    <div className="flex items-center gap-4 ">
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <FileCheck className="w-4 h-4" />
                        Status
                      </label>
                      <Select
                        value={deal.status || 'New'}
                        onValueChange={handleStatusChange}
                        disabled={isUpdating}
                      >
                        <SelectTrigger className="w-auto h-6 border-0 bg-transparent p-0 focus:ring-0">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(statusConfig).map(([key, config]) => (
                            <SelectItem key={key} value={key}>
                              <Badge className={config.className}>{config.label}</Badge>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Priority - Giữ nguyên vì cần edit */}
                    <div className="flex items-center gap-2 ">
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <Flame className="w-4 h-4" />
                        Priority
                      </label>
                      <Select
                        value={deal.priority || 'Low'}
                        onValueChange={handlePriorityChange}
                        disabled={isUpdating}
                      >
                        <SelectTrigger className="w-auto h-6 border-0 bg-transparent p-0 focus:ring-0">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(priorityConfig.badge).map(([key, config]) => (
                            <SelectItem key={key} value={key}>
                              <div className="flex items-center gap-2">
                                <Badge className={config.className}>{config.label}</Badge>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    {/* Seller Info */}
                    <div className="flex items-center gap-2">
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <User className="w-4 h-4" />
                        Seller
                      </label>
                      <div className="flex items-center gap-2">
                        <UserInfoTooltip
                          userInfo={{
                            avatar: deal.saler?.fullName?.charAt(0) || '?',
                            name: deal.saler?.fullName || 'Unknown',
                            role: 'Seller',
                            email: deal.saler?.email || 'N/A',
                            phone: deal.saler?.phoneNumber || 'N/A',
                          }}
                        >
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
                            {deal.saler?.fullName?.charAt(0) || '?'}
                          </div>
                        </UserInfoTooltip>
                      </div>
                    </div>

                    {/* Lead Info */}
                    <div className="flex items-center gap-2">
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <ContactRound className="w-4 h-4" />
                        Lead
                      </label>
                      <div className="flex items-center gap-2">
                        <UserInfoTooltip
                          userInfo={{
                            avatar: deal.customer?.name?.charAt(0) || '?',
                            name: deal.customer?.name || 'Unknown',
                            role: 'Lead',
                            email: deal.customer?.email || 'N/A',
                            phone: deal.customer?.phone || 'N/A',
                          }}
                        >
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-medium">
                            {deal.customer?.name?.charAt(0) || '?'}
                          </div>
                        </UserInfoTooltip>
                      </div>
                    </div>
                    {/* Created Date - CHANGED: From input to display */}
                    <div className="flex items-center gap-2 ">
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <Calendar className="w-4 h-4" />
                        Created Date
                      </label>
                      <div className="">
                        <span className="text-sm font-medium text-gray-600">
                          {deal.createdAt
                            ? format(new Date(deal.createdAt), 'MMM dd, yyyy')
                            : 'Not available'}
                        </span>
                      </div>
                    </div>

                    {/* Latest update - CHANGED: From input to display */}
                    <div className="flex items-center gap-2 ">
                      <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <Clock className="w-4 h-4" />
                        Time stamp
                      </label>
                      <div className="">
                        <span className="text-sm font-medium text-gray-600">
                          {formatTimeAgo(deal.updatedAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Description */}
                <Card className="p-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Description</h3>

                    {/* Toolbar */}
                    {/* <div className="flex items-center gap-2 p-2 border rounded-md bg-gray-50">
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <strong>B</strong>
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <em>I</em>
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <u>U</u>
                    </Button>
                    <div className="w-px h-6 bg-gray-300 mx-2"></div>
                    <Button variant="ghost" size="sm" className="text-xs">
                      Longer
                    </Button>
                    <Button variant="ghost" size="sm" className="text-xs">
                      Shorter
                    </Button>
                    <Button variant="ghost" size="sm" className="text-xs">
                      Regenerate
                    </Button>
                    <Button variant="ghost" size="sm" className="text-xs bg-blue-100 text-blue-700">
                      Generate by AI
                    </Button>
                  </div> */}

                    <Textarea
                      value={description}
                      onChange={e => {
                        setDescription(e.target.value);
                        setIsEditing(true);
                      }}
                      className="min-h-[200px] resize-none"
                      placeholder="Enter deal description..."
                      disabled={isUpdating}
                    />

                    {isEditing && (
                      <div className="flex gap-2 justify-end">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setDescription(deal.description || '');
                            setIsEditing(false);
                          }}
                          disabled={isUpdating}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleDescriptionSave}
                          className="bg-blue-600 hover:bg-blue-700"
                          disabled={isUpdating}
                        >
                          {isUpdating ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            'Save Description'
                          )}
                        </Button>
                      </div>
                    )}
                  </div>
                </Card>

                {/* Keep your existing Tabs Section */}
                <Card className="p-6">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="customer">Customer Info</TabsTrigger>
                      <TabsTrigger value="saler">Saler Info</TabsTrigger>
                    </TabsList>

                    <TabsContent value="customer" className="mt-6">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Customer Details</h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditing(!isEditing)}
                            className="flex items-center gap-2"
                          >
                            <Edit2 className="w-4 h-4" />
                            {isEditing ? 'Save' : 'Edit'}
                          </Button>
                        </div>

                        <div className="space-y-4">
                          {/* First Name */}
                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <User className="w-5 h-5 text-gray-500" />
                              <div>
                                <p className="text-xs text-gray-500 mb-1"> Name</p>
                                {isEditing ? (
                                  <Input
                                    value={deal.customer?.name || ''}
                                    className="h-8"
                                    disabled={isUpdating}
                                  />
                                ) : (
                                  <p className="font-medium">
                                    {deal.customer?.name || 'Not provided'}
                                  </p>
                                )}
                              </div>
                            </div>
                            {!isEditing && (
                              <Button variant="ghost" size="sm" onClick={() => setIsEditing(true)}>
                                <Edit2 className="w-4 h-4" />
                              </Button>
                            )}
                          </div>

                          {/* Email */}
                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <Mail className="w-5 h-5 text-gray-500" />
                              <div className="flex-1">
                                <p className="text-xs text-gray-500 mb-1">Email</p>
                                {isEditing ? (
                                  <Input
                                    type="email"
                                    value={deal.customer?.email || ''}
                                    onChange={e => handleFieldUpdate('email', e.target.value)}
                                    className="h-8"
                                    disabled={isUpdating}
                                  />
                                ) : (
                                  <p className="font-medium text-blue-600">
                                    {deal.customer?.email || 'Not provided'}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {!isEditing && deal.customer?.email && (
                                <Button variant="ghost" size="sm">
                                  <Plus className="w-4 h-4" />
                                </Button>
                              )}
                              {!isEditing && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setIsEditing(true)}
                                >
                                  <Edit2 className="w-4 h-4" />
                                </Button>
                              )}
                            </div>
                          </div>

                          {/* Phone Number */}
                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <Phone className="w-5 h-5 text-gray-500" />
                              <div className="flex-1">
                                <p className="text-xs text-gray-500 mb-1">Phone Number</p>
                                {isEditing ? (
                                  <Input
                                    type="tel"
                                    value={deal.customer?.phone || ''}
                                    onChange={e =>
                                      handleFieldUpdate('customerPhone', e.target.value)
                                    }
                                    className="h-8"
                                    disabled={isUpdating}
                                    placeholder="+****************"
                                  />
                                ) : (
                                  <p className="font-medium">
                                    {deal.customer?.phone || 'Not provided'}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {!isEditing && deal.customer?.phone && (
                                <Button variant="ghost" size="sm">
                                  <Plus className="w-4 h-4" />
                                </Button>
                              )}
                              {!isEditing && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setIsEditing(true)}
                                >
                                  <Edit2 className="w-4 h-4" />
                                </Button>
                              )}
                            </div>
                          </div>

                          {/* Sources */}
                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <Hash className="w-5 h-5 text-gray-500" />
                              <div className="flex-1">
                                <p className="text-xs text-gray-500 mb-1">Sources</p>
                                {isEditing ? (
                                  <Select
                                    value={deal.customer?.source || ''}
                                    onValueChange={value =>
                                      handleFieldUpdate('customerSource', value)
                                    }
                                    disabled={isUpdating}
                                  >
                                    <SelectTrigger className="h-8">
                                      <SelectValue placeholder="Select source" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="Form">Form</SelectItem>
                                      <SelectItem value="Message">Message</SelectItem>
                                      <SelectItem value="Appointment">Appointment</SelectItem>
                                      <SelectItem value="ExternalSeller">
                                        External Seller
                                      </SelectItem>
                                      <SelectItem value="Admin">Admin</SelectItem>
                                      <SelectItem value="Zalo">Zalo</SelectItem>
                                      <SelectItem value="Facebook">Facebook</SelectItem>
                                      <SelectItem value="Web">Web</SelectItem>
                                    </SelectContent>
                                  </Select>
                                ) : (
                                  <div className="flex items-center gap-2">
                                    {deal.customer?.source && getSourceIcons(deal.customer.source)}
                                  </div>
                                )}
                              </div>
                            </div>
                            {!isEditing && (
                              <Button variant="ghost" size="sm" onClick={() => setIsEditing(true)}>
                                <ExternalLink className="w-4 h-4" />
                              </Button>
                            )}
                          </div>

                          {/* Address */}
                          <div className="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-start gap-3">
                              <MapPin className="w-5 h-5 text-gray-500 mt-1" />
                              <div className="flex-1">
                                <p className="text-xs text-gray-500 mb-1">Address</p>
                                {isEditing ? (
                                  <Textarea
                                    value={deal.customer?.address || ''}
                                    onChange={e =>
                                      handleFieldUpdate('customerAddress', e.target.value)
                                    }
                                    className="min-h-[60px]"
                                    disabled={isUpdating}
                                    placeholder="Enter customer address"
                                  />
                                ) : (
                                  <p className="font-medium">
                                    {deal.customer?.address || 'Not provided'}
                                  </p>
                                )}
                              </div>
                            </div>
                            {!isEditing && (
                              <Button variant="ghost" size="sm" onClick={() => setIsEditing(true)}>
                                <Edit2 className="w-4 h-4" />
                              </Button>
                            )}
                          </div>

                          {/* Lead Score */}
                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <Target className="w-5 h-5 text-gray-500" />
                              <div className="flex-1">
                                <p className="text-xs text-gray-500 mb-1">Lead Score</p>
                                {isEditing ? (
                                  <Select
                                    value={deal.customer?.score || ''}
                                    onValueChange={value =>
                                      handleFieldUpdate('customerScore', value)
                                    }
                                    disabled={isUpdating}
                                  >
                                    <SelectTrigger className="h-8">
                                      <SelectValue placeholder="Select lead score" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="Hot">
                                        <div className="flex items-center gap-2">
                                          <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                          <span>Hot</span>
                                        </div>
                                      </SelectItem>
                                      <SelectItem value="Warm">
                                        <div className="flex items-center gap-2">
                                          <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                                          <span>Warm</span>
                                        </div>
                                      </SelectItem>
                                      <SelectItem value="Cold">
                                        <div className="flex items-center gap-2">
                                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                                          <span>Cold</span>
                                        </div>
                                      </SelectItem>
                                    </SelectContent>
                                  </Select>
                                ) : (
                                  <div className="flex items-center gap-2">
                                    <div
                                      className={`w-3 h-3 rounded-full ${
                                        deal.customer?.score === 'Hot'
                                          ? 'bg-red-500'
                                          : deal.customer?.score === 'Warm'
                                            ? 'bg-orange-500'
                                            : deal.customer?.score === 'Cold'
                                              ? 'bg-blue-500'
                                              : 'bg-gray-400'
                                      }`}
                                    ></div>
                                    <span className="font-medium capitalize">
                                      {deal.customer?.score || 'Not set'}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                            {!isEditing && (
                              <Button variant="ghost" size="sm" onClick={() => setIsEditing(true)}>
                                <Edit2 className="w-4 h-4" />
                              </Button>
                            )}
                          </div>

                          {/* Last Contacted */}
                          {/* <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <Clock className="w-5 h-5 text-gray-500" />
                              <div>
                                <p className="text-xs text-gray-500 mb-1">Last Contacted</p>
                                <p className="font-medium">
                                  {deal.customer?.lastContacted || 'Never'}
                                </p>
                              </div>
                            </div>
                            <Button variant="ghost" size="sm">
                              <ChevronRight className="w-4 h-4" />
                            </Button>
                          </div> */}
                        </div>
                      </div>
                    </TabsContent>
                    <TabsContent value="saler" className="mt-6">
                      <div className="space-y-4">
                        {/* Seller Information */}
                        <div className="mt-8">
                          <h3 className="text-lg font-semibold mb-4">Seller Information</h3>
                          <div className="space-y-4">
                            {/* Full Name */}
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center gap-3">
                                <User className="w-5 h-5 text-gray-500" />
                                <div>
                                  <p className="text-xs text-gray-500 mb-1">Name</p>
                                  <p className="font-medium">
                                    {deal.saler?.fullName || 'Not assigned'}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Email */}
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center gap-3">
                                <Mail className="w-5 h-5 text-gray-500" />
                                <div className="flex-1">
                                  <p className="text-xs text-gray-500 mb-1">Email</p>
                                  <p className="font-medium text-blue-600">
                                    {deal.saler?.email || 'Not provided'}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Phone Number */}
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center gap-3">
                                <Phone className="w-5 h-5 text-gray-500" />
                                <div className="flex-1">
                                  <p className="text-xs text-gray-500 mb-1">Phone Number</p>
                                  <p className="font-medium">
                                    {deal.saler?.phoneNumber || 'Not provided'}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Role */}
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center gap-3">
                                <Shield className="w-5 h-5 text-gray-500" />
                                <div className="flex-1">
                                  <p className="text-xs text-gray-500 mb-1">Role</p>
                                  <p className="font-medium capitalize">
                                    {deal.saler?.role || 'Not assigned'}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </Card>

                {/* Attachments */}
                <Card className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">Attachments Document</h3>
                      <Button className="bg-blue-600 hover:bg-blue-700">
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Document
                      </Button>
                    </div>

                    {/* You can replace this with your actual attachments logic */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="p-4 border rounded-lg text-center">
                        <div className="w-12 h-12 bg-blue-100 rounded mx-auto mb-2 flex items-center justify-center">
                          <FileText className="h-6 w-6 text-blue-600" />
                        </div>
                        <p className="text-sm font-medium">Proposal.PDF</p>
                      </div>
                      <div className="p-4 border rounded-lg text-center">
                        <div className="w-12 h-12 bg-red-100 rounded mx-auto mb-2 flex items-center justify-center">
                          <FileText className="h-6 w-6 text-red-600" />
                        </div>
                        <p className="text-sm font-medium">Contract.PDF</p>
                      </div>
                    </div>
                  </div>
                </Card>
                {/* Notes Section */}
                <NotesSection deal={deal} />
              </div>

              {/* Activity Sidebar - Same as before */}
              <div className="flex-1 space-y-4">
                <Card className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">Activity</h3>
                    </div>

                    {/* Activity Filter Tabs */}
                    <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                      <button
                        onClick={() => setActivityFilter('all')}
                        className={`flex-1 text-xs py-2 px-3 rounded-md transition-colors ${
                          activityFilter === 'all'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        All Activity
                      </button>
                      <button
                        onClick={() => setActivityFilter('comment')}
                        className={`flex-1 text-xs py-2 px-3 rounded-md transition-colors ${
                          activityFilter === 'comment'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        Comment
                      </button>
                      <button
                        onClick={() => setActivityFilter('history')}
                        className={`flex-1 text-xs py-2 px-3 rounded-md transition-colors ${
                          activityFilter === 'history'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        History
                      </button>
                    </div>

                    {/* Conditional Content Based on Active Filter */}
                    {activityFilter === 'history' ? (
                      // Show Deal Logs when History tab is active
                      <div className="max-h-96 overflow-y-auto">
                        <DealLogs dealId={dealId} />
                      </div>
                    ) : (
                      // Show existing activity feed for 'all' and 'comment' filters
                      <>
                        {/* Activity Feed */}
                        <div className="space-y-4 max-h-96 overflow-y-auto">
                          {filteredActivities.map(activity => (
                            <div key={activity.id} className="flex gap-3">
                              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs font-medium flex-shrink-0">
                                {activity.user.avatar}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium text-sm text-gray-900">
                                    {activity.user.name}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    ({activity.user.role})
                                  </span>
                                </div>
                                <div className="text-xs text-gray-500 mb-1">
                                  {activity.timestamp}
                                </div>
                                <p className="text-sm text-gray-700 leading-relaxed">
                                  {activity.action}
                                </p>
                                {activity.type === 'comment' && activity.id === '3' && (
                                  <div className="mt-2 grid grid-cols-2 gap-2">
                                    <div className="p-2 border rounded text-center">
                                      <div className="w-8 h-8 bg-blue-100 rounded mx-auto mb-1 flex items-center justify-center">
                                        <FileText className="h-4 w-4 text-blue-600" />
                                      </div>
                                      <p className="text-xs">Inspiration Web App 0123.PNG</p>
                                    </div>
                                    <div className="p-2 border rounded text-center">
                                      <div className="w-8 h-8 bg-red-100 rounded mx-auto mb-1 flex items-center justify-center">
                                        <FileText className="h-4 w-4 text-red-600" />
                                      </div>
                                      <p className="text-xs">Inspiration Web App 0123.PDF</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Comment Input - Only show for 'all' and 'comment' filters */}
                        {activityFilter !== 'history' && (
                          <div className="space-y-2 border-t pt-4">
                            <Textarea
                              placeholder="Add a comment..."
                              value={newComment}
                              onChange={e => setNewComment(e.target.value)}
                              className="min-h-[80px] resize-none"
                            />
                            <div className="flex justify-between items-center">
                              <Button variant="ghost" size="sm">
                                <Paperclip className="h-4 w-4" />
                              </Button>
                              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                                Post Comment
                              </Button>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </div>
  );
}
