'use client';
import React, { useState } from 'react';
import { CSS } from '@dnd-kit/utilities';
import { useSortable } from '@dnd-kit/sortable';
import { formatDate, formatTimeAgo } from '@/utils/dates/formatDate';
import { DealDetailsDialog } from './DealDetailsDialog';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  CalendarPlus,
  FileEdit,
  MessageCircle,
  MoreHorizontal,
  Clock,
  User,
  Calendar,
  Trash2,
  Phone,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Deal } from '@/lib/api/services/fetchDeal';
import { DeleteDealDialog } from '@/app/(admin)/admin/sales/components/DeleteDealDialog';
import { priorityConfig, statusConfig } from '../config/configuration';
import { useDeleteDealWithConfirm } from '../custom-hooks/useDeleteDealWithConfirm';

export interface DealCardProps {
  deal: Deal;
  onEdit?: (deal: Deal) => void;
  onDelete?: (dealId: string) => void;
  index?: number;
  className?: string;
}

const DealCard: React.FC<DealCardProps> = ({ deal, onEdit, className, index }) => {
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const router = useRouter();
  const { dealToDelete, handleDeleteClick, confirmDelete, closeDeleteDialog, isDeleting } =
    useDeleteDealWithConfirm();

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: deal.id,
    data: {
      type: 'Deal',
      deal,
      index,
      status: deal.status,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? '0.5' : '1',
  };

  // Handle click on the card (not on drag handle)
  const handleCardClick = (e: React.MouseEvent) => {
    // Only navigate if not dragging
    if (!e.defaultPrevented) {
      router.push(`sales/${deal.id}`);
    }
  };

  const priority =
    priorityConfig.card[deal.priority?.toUpperCase() as keyof typeof priorityConfig.card] ||
    priorityConfig.card.LOW;

  const statusColor =
    statusConfig[deal.status as keyof typeof statusConfig]?.className.split(' ')[0] ||
    'bg-gray-500';

  return (
    <>
      <div
        style={style}
        className={`bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all duration-200 cursor-pointer group ${className} ${
          isDeleting ? 'opacity-50 pointer-events-none' : ''
        }`}
        ref={setNodeRef}
        {...attributes}
        {...listeners}
        onClick={handleCardClick}
      >
        {/* Status indicator bar */}
        <div className={`h-1 w-full rounded-t-lg ${statusColor}`} />

        <div className="p-3">
          {/* Header with title and actions */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 text-sm leading-5 truncate">
                {deal.title || 'Untitled Deal'}
              </h3>
            </div>

            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0 ml-2"
                  onClick={e => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  disabled={isDeleting}
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    onEdit?.(deal);
                  }}
                  disabled={isDeleting}
                >
                  <FileEdit className="mr-2 h-3 w-3" />
                  Edit Deal
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    setIsDetailsOpen(true);
                  }}
                  disabled={isDeleting}
                >
                  <MessageCircle className="mr-2 h-3 w-3" />
                  Add Note
                </DropdownMenuItem>
                <DropdownMenuItem onClick={e => e.stopPropagation()} disabled={isDeleting}>
                  <CalendarPlus className="mr-2 h-3 w-3" />
                  Schedule Meeting
                </DropdownMenuItem>

                <DropdownMenuSeparator />

                <DropdownMenuItem
                  onClick={e => {
                    e.stopPropagation();
                    e.preventDefault();
                    // Then open delete dialog
                    handleDeleteClick(deal, e);
                  }}
                  className="text-red-600 focus:text-red-600 focus:bg-red-50"
                  disabled={isDeleting}
                >
                  <Trash2 className="mr-2 h-3 w-3" />
                  Delete Deal
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Priority badge */}
          <div className="mb-3">
            <Badge
              variant="outline"
              className={`text-xs px-2 py-0.5 font-medium ${priority.className}`}
            >
              <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${priority.dot}`} />
              {priority.label}
            </Badge>
          </div>

          {/* Customer info */}
          {deal.customer?.name && (
            <div className="flex items-center text-xs text-gray-600 mb-2">
              <User className="h-3 w-3 mr-1.5" />
              <span className="truncate">{deal.customer?.name}</span>
            </div>
          )}

          {/* Customer Phone Number */}
          <div className="flex items-center text-xs text-gray-500 mb-3">
            <div className="flex items-center">
              <Phone className="h-3 w-3 mr-1.5" />
              <span className="truncate">{deal.customer?.phone || 'No phone number'}</span>
            </div>
          </div>

          {/* Footer with dates and assignee */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-xs text-gray-500">
              <Calendar className="h-3 w-3 mr-1" />
              <span>{formatDate(deal.createdAt, 'MMM dd')}</span>
            </div>

            <div className="flex items-center gap-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Updated {formatTimeAgo(deal.updatedAt)}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Avatar className="h-6 w-6 cursor-pointer border border-gray-200">
                      <AvatarFallback className="text-xs bg-gray-100 text-gray-600">
                        {deal.customer?.name?.charAt(0) || 'C'}
                      </AvatarFallback>
                    </Avatar>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{deal.customer?.name || 'Unknown Customer'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>

      <DealDetailsDialog
        deal={deal}
        open={isDetailsOpen}
        onOpenChange={setIsDetailsOpen}
        onEdit={onEdit}
      />

      <DeleteDealDialog
        deal={dealToDelete}
        open={!!dealToDelete}
        onOpenChange={closeDeleteDialog}
        onConfirm={confirmDelete}
        isDeleting={isDeleting}
      />
    </>
  );
};

export default DealCard;
