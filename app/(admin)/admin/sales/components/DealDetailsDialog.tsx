'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { formatDate } from '@/utils/dates/formatDate';
import { Deal } from '@/lib/api/services/fetchDeal';

interface DealDetailsDialogProps {
  deal: Deal | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (deal: Deal) => void;
  onDelete?: (dealId: string) => void;
}

export function DealDetailsDialog({
  deal,
  open,
  onOpenChange,
  onEdit,
  onDelete,
}: DealDetailsDialogProps) {
  if (!deal) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Deal Details</DialogTitle>
          <DialogDescription>View and manage deal information</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-500">Deal ID</h3>
              <p className="text-sm">{deal.id}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-500">Status</h3>
              <p className="text-sm font-medium">{deal.status}</p>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-500">Title</h3>
            <p className="text-sm">{deal.title || 'No title'}</p>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-500">Description</h3>
            <p className="text-sm">{deal.description || 'No description'}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-500">Lead ID</h3>
              <p className="text-sm">{deal.leadId}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-500">Sales Rep ID</h3>
              <p className="text-sm">{deal.salesRepId}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-500">Created At</h3>
              <p className="text-sm">{formatDate(deal.createdAt, 'dd/MM/yyyy HH:mm:ss')}</p>
            </div>
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-500">Updated At</h3>
              <p className="text-sm">{formatDate(deal.updatedAt, 'dd/MM/yyyy HH:mm:ss')}</p>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex space-x-2">
            {onEdit && (
              <Button variant="outline" onClick={() => onEdit(deal)}>
                Edit
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                onClick={() => {
                  if (confirm('Are you sure you want to delete this deal?')) {
                    onDelete(deal.id);
                  }
                }}
              >
                Delete
              </Button>
            )}
          </div>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
