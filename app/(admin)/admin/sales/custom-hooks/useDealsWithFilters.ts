import { useState } from 'react';
import { DealSearchParams } from '@/lib/api/services/fetchDeal';
import { useDeals } from '@/hooks/useDeals';

export function useDealsWithFilters() {
  const [filters, setFilters] = useState<DealSearchParams>({
    searchTerm: '',
    status: 'all',
    priority: 'all',
    sortBy: 'createdAt',
    isAscending: true,
  });

  const [view, setView] = useState<'board' | 'list' | 'table'>('board');
  const [pagination, setPagination] = useState({
    pageNumber: 1,
    pageSize: view === 'board' ? 10 : 10,
  });

  const { data, isLoading, error } = useDeals({
    ...pagination,
    searchTerm: filters.searchTerm || undefined,
    status: filters.status !== 'all' ? filters.status : undefined,
    priority: filters.priority !== 'all' ? filters.priority : undefined,
    sortBy: filters.sortBy,
    isAscending: !filters.isAscending,
  });

  const handleFilterChange = (key: keyof DealSearchParams, value: string | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, pageNumber: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, pageNumber: newPage }));
  };

  const handlePageSizeChange = (newSize: number) => {
    setPagination(prev => ({ ...prev, pageSize: newSize, pageNumber: 1 }));
  };

  const handleViewChange = (newView: 'board' | 'list' | 'table') => {
    setView(newView);
    setPagination(prev => ({
      ...prev,
      pageSize: newView === 'board' ? 10 : 10,
      pageNumber: 1,
    }));
  };

  return {
    data,
    isLoading,
    error,
    filters,
    pagination,
    view,
    handleFilterChange,
    handlePageChange,
    handlePageSizeChange,
    handleViewChange,
  };
}
