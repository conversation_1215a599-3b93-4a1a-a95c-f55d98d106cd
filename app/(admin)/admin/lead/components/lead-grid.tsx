'use client';

import { LeadCard } from './lead-card';
import { useState } from 'react';
import { Alert } from '@/components/ui/alert';
import { AlertCircle, TrendingUp, Star, Clock, ArrowUpDown, Grid3X3, Loader2 } from 'lucide-react';
import { AlertTitle } from '@/components/ui/alert';
import { AlertDescription } from '@/components/ui/alert';
import { LeadScore, LeadSearchParams } from '@/lib/api/services/fetchLead';
import { useLeads, useUpdateLeadScore } from '@/hooks/useLead';
import { LeadCardSkeleton } from '@/components/leadCardSeketon';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

const LEAD_COLUMNS = [
  {
    id: 'Hot',
    title: 'Tiềm năng cao',
    description: '<PERSON>h<PERSON>ch hàng có khả năng chuyển đổi cao',
    icon: TrendingUp,
    priority: 'Ưu tiên',
  },
  {
    id: 'Warm',
    title: 'Quan tâm',
    description: 'Khách hàng đang tìm hiểu và cân nhắc',
    icon: Star,
    priority: 'Trung bình',
  },
  {
    id: 'Cold',
    title: 'Tiềm năng thấp',
    description: 'Khách hàng cần được nuôi dưỡng thêm',
    icon: Clock,
    priority: 'Thấp',
  },
];

export function LeadGrid() {
  const [searchParams] = useState<LeadSearchParams>({
    pageNumber: 1,
    pageSize: 100,
  });

  const { data: leads, isLoading, isError, error, isFetching } = useLeads(searchParams);
  const updateLeadStatus = useUpdateLeadScore();

  // Group leads by score/status
  const groupedLeads = {
    Hot: leads?.leads.filter(lead => lead.score === 'Hot') || [],
    Warm: leads?.leads.filter(lead => lead.score === 'Warm') || [],
    Cold: leads?.leads.filter(lead => lead.score === 'Cold') || [],
  };

  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index)
      return;

    updateLeadStatus.mutate({
      leadId: draggableId,
      score: destination.droppableId as LeadScore,
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {LEAD_COLUMNS.map(column => (
            <Card key={column.id} className="border border-border/40 shadow-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-muted/50 flex items-center justify-center">
                      <column.icon className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div>
                      <CardTitle className="text-base font-semibold">{column.title}</CardTitle>
                      <p className="text-xs text-muted-foreground mt-1">{column.description}</p>
                    </div>
                  </div>
                  <div className="h-6 w-12 bg-muted/30 rounded animate-pulse" />
                </div>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                {Array.from({ length: 2 }).map((_, index) => (
                  <LeadCardSkeleton key={index} />
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive" className="border-destructive/20 bg-destructive/5">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Lỗi tải dữ liệu</AlertTitle>
        <AlertDescription>
          {error?.message || 'Không thể tải danh sách khách hàng tiềm năng. Vui lòng thử lại.'}
        </AlertDescription>
      </Alert>
    );
  }

  const totalLeads = leads?.leads.length || 0;

  return (
    <div className="space-y-6">
      {/* Drag Instructions */}
      {totalLeads > 0 && (
        <Card className="border border-border/40 bg-muted/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-center gap-3 text-sm text-muted-foreground">
              <div className="w-8 h-8 rounded-lg bg-muted/50 flex items-center justify-center">
                <ArrowUpDown className="h-4 w-4" />
              </div>
              <span className="font-medium">
                Kéo thả các thẻ khách hàng để thay đổi trạng thái phân loại
              </span>
              <Separator orientation="vertical" className="h-4" />
              <div className="flex items-center gap-1">
                <Grid3X3 className="h-4 w-4" />
                <span>{totalLeads} khách hàng</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Kanban Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {LEAD_COLUMNS.map(column => {
            const columnLeads = groupedLeads[column.id as LeadScore];

            return (
              <Card key={column.id} className="border border-border/40 shadow-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-muted/50 flex items-center justify-center border border-border/20">
                        <column.icon className="h-5 w-5 text-foreground/80" />
                      </div>
                      <div>
                        <CardTitle className="text-base font-semibold">{column.title}</CardTitle>
                        <p className="text-xs text-muted-foreground mt-1">{column.description}</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-sm bg-muted/30 border-border/40">
                      {columnLeads.length}
                    </Badge>
                  </div>
                </CardHeader>
                <Droppable droppableId={column.id}>
                  {provided => (
                    <CardContent
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="p-4 space-y-3 min-h-[400px]"
                    >
                      {columnLeads.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-12 text-center">
                          <div className="w-16 h-16 rounded-lg bg-muted/30 flex items-center justify-center mb-4 border border-border/20">
                            <column.icon className="h-8 w-8 text-muted-foreground/60" />
                          </div>
                          <p className="text-sm font-medium text-muted-foreground">
                            Chưa có khách hàng
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Kéo thả khách hàng vào đây để phân loại
                          </p>
                          <Badge variant="outline" className="mt-3 text-xs bg-muted/20">
                            {column.priority}
                          </Badge>
                        </div>
                      ) : (
                        columnLeads.map((lead, index) => (
                          <Draggable key={lead.id} draggableId={lead.id} index={index}>
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                style={provided.draggableProps.style}
                              >
                                <LeadCard
                                  lead={lead}
                                  status={column.id as LeadScore}
                                  isDragging={snapshot.isDragging}
                                  isLoading={isFetching}
                                />
                              </div>
                            )}
                          </Draggable>
                        ))
                      )}
                      {provided.placeholder}
                    </CardContent>
                  )}
                </Droppable>
              </Card>
            );
          })}
        </div>
      </DragDropContext>

      {/* Global Loading Indicator */}
      {isFetching && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className="bg-background border border-border shadow-lg rounded-full p-3 flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Đang cập nhật...</span>
          </div>
        </div>
      )}
    </div>
  );
}
