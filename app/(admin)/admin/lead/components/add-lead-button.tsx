'use client';

import { useState } from 'react';
import { PlusCircle, User, Mail, Phone, MapPin, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useCreateLead } from '@/hooks/useLead';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { LeadScore } from '@/lib/api/services/fetchLead';

// Form schema matching ActionLeadRequest interface
const formSchema = z.object({
  name: z.string().min(2, {
    message: 'Tên phải có ít nhất 2 ký tự.',
  }),
  email: z.string().email({
    message: 'Vui lòng nhập địa chỉ email hợp lệ.',
  }),
  phone: z.string().min(1, {
    message: 'Số điện thoại là bắt buộc.',
  }),
  address: z.string().min(1, {
    message: 'Địa chỉ là bắt buộc.',
  }),
  score: z.nativeEnum(LeadScore, {
    required_error: 'Vui lòng chọn mức độ tiềm năng.',
  }),
});

export function AddLeadButton() {
  const [open, setOpen] = useState(false);

  const createLead = useCreateLead();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
      score: undefined,
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    createLead.mutate(values, {
      onSuccess: data => {
        if (data.status) {
          form.reset();
          setOpen(false);
        }
      },
    });
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="lg" className="px-6">
          <PlusCircle className="mr-2 h-5 w-5" />
          Thêm khách hàng mới
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Thêm khách hàng tiềm năng mới</DialogTitle>
          <DialogDescription>
            Nhập thông tin chi tiết của khách hàng tiềm năng. Tất cả thông tin sẽ được lưu trữ an
            toàn.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Personal Information */}
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                  <User className="h-4 w-4 text-primary" />
                  Thông tin cá nhân
                </CardTitle>
                <CardDescription>Thông tin cơ bản về khách hàng tiềm năng</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Họ và tên <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Nguyễn Văn A" className="h-11" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                  <Phone className="h-4 w-4 text-primary" />
                  Thông tin liên hệ
                </CardTitle>
                <CardDescription>Các phương thức liên hệ với khách hàng</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium flex items-center gap-2">
                        <Mail className="h-3.5 w-3.5" />
                        Email <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          className="h-11"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium flex items-center gap-2">
                        <Phone className="h-3.5 w-3.5" />
                        Số điện thoại <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="0123 456 789" className="h-11" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium flex items-center gap-2">
                        <MapPin className="h-3.5 w-3.5" />
                        Địa chỉ <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="123 Đường ABC, Quận 1, TP.HCM"
                          className="h-11"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Lead Assessment */}
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                  <Star className="h-4 w-4 text-primary" />
                  Đánh giá tiềm năng
                </CardTitle>
                <CardDescription>Mức độ tiềm năng của khách hàng</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="score"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Mức độ tiềm năng <span className="text-destructive">*</span>
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-11">
                            <SelectValue placeholder="Chọn mức độ tiềm năng" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={LeadScore.Hot}>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-red-500" />
                              <span>Hot - Tiềm năng cao</span>
                            </div>
                          </SelectItem>
                          <SelectItem value={LeadScore.Warm}>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-orange-500" />
                              <span>Warm - Tiềm năng trung bình</span>
                            </div>
                          </SelectItem>
                          <SelectItem value={LeadScore.Cold}>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-blue-500" />
                              <span>Cold - Tiềm năng thấp</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Help Section */}
                <div className="mt-4 p-3 bg-muted/50 rounded-lg border">
                  <div className="grid grid-cols-1 gap-2 text-xs">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-500" />
                      <span>
                        <strong>Hot:</strong> Khách hàng sẵn sàng mua, cần liên hệ ngay
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-orange-500" />
                      <span>
                        <strong>Warm:</strong> Khách hàng quan tâm, cần nuôi dưỡng thêm
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-blue-500" />
                      <span>
                        <strong>Cold:</strong> Khách hàng tiềm năng, cần chiến lược dài hạn
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Separator />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={createLead.isPending}
              >
                Hủy
              </Button>
              <Button type="submit" disabled={createLead.isPending} className="px-6">
                {createLead.isPending ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/50 border-t-white animate-spin rounded-full mr-2" />
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Tạo khách hàng
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
