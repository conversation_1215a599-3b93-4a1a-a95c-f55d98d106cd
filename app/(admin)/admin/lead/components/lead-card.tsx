'use client';

import { useState } from 'react';
import { Building2, MapPin, Phone, Mail } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON><PERSON>ontent, CardHeader, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Lead, LeadScore } from '@/lib/api/services/fetchLead';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface LeadCardProps {
  lead: Lead;
  status?: LeadScore;
  isDragging?: boolean;
  isLoading?: boolean;
}

export function LeadCard({ lead, isDragging, isLoading }: LeadCardProps) {
  const [skipDelayDuration] = useState<number | undefined>(300);

  const initials = lead.name
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase();

  return (
    <TooltipProvider skipDelayDuration={skipDelayDuration}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'group relative transition-all duration-300 cursor-grab active:cursor-grabbing',
              'border border-border/40 bg-card/50 backdrop-blur-sm rounded-lg overflow-hidden',
              'hover:border-border hover:bg-card hover:shadow-lg hover:shadow-black/5',
              'hover:-translate-y-1 hover:scale-[1.02]',
              isDragging && [
                'shadow-2xl shadow-black/20 border-primary/30 scale-105 rotate-3 z-50',
                'bg-background/95 backdrop-blur-sm cursor-grabbing',
              ],
              isLoading && 'opacity-50 pointer-events-none'
            )}
          >
            <TooltipContent className="p-0 max-w-sm w-80 bg-background border border-border shadow-lg rounded-lg overflow-hidden">
              <div className="p-4 bg-muted/20 border-b border-border/20">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12 ring-2 ring-border/20 shadow-sm">
                    <AvatarImage src="/placeholder.svg" alt={lead.name} />
                    <AvatarFallback className="bg-muted/80 text-foreground font-semibold text-sm">
                      {initials}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-base text-foreground">{lead.name}</h3>
                    <p className="text-xs text-muted-foreground">{lead.source}</p>
                  </div>
                </div>
              </div>
              <div className="p-4 space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-xs text-muted-foreground">Email</p>
                    <p className="text-sm text-foreground">{lead.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-xs text-muted-foreground">Điện thoại</p>
                    <p className="text-sm text-foreground">{lead.phone}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-xs text-muted-foreground">Địa chỉ</p>
                    <p className="text-sm text-foreground">{lead.address || 'Chưa cập nhật'}</p>
                  </div>
                </div>
              </div>
            </TooltipContent>

            {/* Drag Indicator */}
            {isDragging && (
              <div className="absolute top-2 right-2 w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center">
                <div className="w-3 h-3 bg-primary rounded-full animate-pulse" />
              </div>
            )}

            <CardHeader className="pb-4 pt-5">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <Avatar className="h-12 w-12 ring-2 ring-border/20 shadow-sm">
                    <AvatarImage src="/placeholder.svg" alt={lead.name} />
                    <AvatarFallback className="bg-muted/80 text-foreground font-semibold text-sm">
                      {initials}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-base truncate text-foreground group-hover:text-foreground/90">
                      {lead.name}
                    </h3>
                    <div className="flex items-center gap-2 mt-2">
                      {lead.source && (
                        <Badge variant="secondary" className="text-xs bg-muted/30">
                          Được tạo từ {lead.source}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                {/* <div className="flex items-center gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <MoreVertical className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {lead.assignedTo && lead.assignedTo.length > 0 ? (
                        <DropdownMenuItem
                          className="text-destructive focus:text-destructive/80"
                          onSelect={handleUnassignLead}
                        >
                          <UserMinus className="h-4 w-4 mr-2" />
                          Bỏ phân công
                        </DropdownMenuItem>
                      ) : (
                        <div className="flex flex-col gap-2 p-2">
                          <input
                            type="text"
                            placeholder="Nhập ID của nhân viên"
                            className="border border-border rounded p-1 text-sm"
                            onChange={e => setSellerId(e.target.value)}
                          />
                          <Button onClick={handleAssignLead} className="flex items-center gap-2">
                            <UserPlus className="h-4 w-4" />
                            Phân công
                          </Button>
                        </div>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div> */}
              </div>
            </CardHeader>

            <CardContent className="pt-0 pb-4">
              {/* Lead Information */}
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-foreground">Name:</span>
                  <span className="text-sm text-muted-foreground">{lead.name || '--'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-foreground">Email:</span>
                  <span className="text-sm text-muted-foreground">{lead.email || '--'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-foreground">Phone:</span>
                  <span className="text-sm text-muted-foreground">{lead.phone || '--'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-foreground">Address:</span>
                  <span className="text-sm text-muted-foreground">{lead.address || '--'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-foreground">Source:</span>
                  <span className="text-sm text-muted-foreground">{lead.source || '--'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-foreground">Score:</span>
                  <span className="text-sm text-muted-foreground">{lead.score || '--'}</span>
                </div>
              </div>
            </CardContent>

            <CardFooter className="pt-0 pb-4 px-6">
              {lead.assignedTo && lead.assignedTo.length > 0 ? (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground font-medium">
                      Nhân viên phụ trách
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-2 bg-muted/30 rounded-lg border border-border/20">
                    <Avatar className="h-8 w-8 ring-1 ring-border/20">
                      <AvatarFallback className="bg-muted text-foreground text-xs font-medium">
                        {lead.assignedTo[0].name?.charAt(0).toUpperCase() || '?'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate text-foreground">
                        {lead.assignedTo[0].name || 'Chưa phân công'}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {lead.assignedTo[0].email || 'Chưa có email'}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex justify-between items-center w-full">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground font-medium">
                      Nhân viên phụ trách
                    </span>
                  </div>
                  <Badge variant="outline" className="text-xs bg-muted/30">
                    Chưa phân công
                  </Badge>
                </div>
              )}
            </CardFooter>
          </div>
        </TooltipTrigger>
      </Tooltip>
    </TooltipProvider>
  );
}
