import React, { useState } from 'react';
import Image from 'next/image';
import { MapPin, Bed, Bath, Home, Building, ChevronLeft, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Property,
  PropertyStatus,
  PropertyType,
  TransactionType,
} from '@/lib/api/services/fetchProperty';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import { cn } from '@/lib/utils';
import { Separator } from '@/components/ui/separator';
import { PropertyPreviewDialog } from '@/components/property-preview-dialog';

interface PropertyCardProps {
  property: Property;
  viewMode?: 'grid' | 'list';
  onVerify: (propertyId: string) => void;
  onUnverify: (propertyId: string) => void;
}

const getPropertyTypeIcon = (type: PropertyType) => {
  switch (type) {
    case PropertyType.APARTMENT:
      return Building;
    case PropertyType.VILLA:
      return Home;
    case PropertyType.SHOP_HOUSE:
      return Building;
    case PropertyType.LAND_PLOT:
      return MapPin;
    default:
      return Building;
  }
};

const getStatusColor = (status: PropertyStatus) => {
  switch (status) {
    case PropertyStatus.AVAILABLE:
      return 'bg-green-100 text-green-800 border-green-200';
    case PropertyStatus.PENDING:
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case PropertyStatus.SOLD:
      return 'bg-red-100 text-red-800 border-red-200';
    case PropertyStatus.RENTED:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export function PropertyCard({
  property,
  viewMode = 'grid',
  onVerify,
  onUnverify,
}: PropertyCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showPreview, setShowPreview] = useState(false);

  const PropertyTypeIcon = getPropertyTypeIcon(property.type);
  const images = property.imageUrls || [];
  const hasMultipleImages = images.length > 1;

  const nextImage = () => {
    setCurrentImageIndex(prev => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex(prev => (prev - 1 + images.length) % images.length);
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  const getPrice = () => {
    if (property.transactionType === TransactionType.FOR_RENT) {
      return formatCurrency(property.priceDetails.rentalPrice || 0, property.priceDetails.currency);
    }
    return formatCurrency(property.priceDetails.salePrice || 0, property.priceDetails.currency);
  };

  const getTransactionTypeLabel = () => {
    return property.transactionType === TransactionType.FOR_RENT ? 'Cho thuê' : 'Bán';
  };

  const getPropertyTypeLabel = () => {
    switch (property.type) {
      case PropertyType.APARTMENT:
        return 'Chung cư';
      case PropertyType.VILLA:
        return 'Biệt thự';
      case PropertyType.SHOP_HOUSE:
        return 'Nhà phố';
      case PropertyType.LAND_PLOT:
        return 'Đất';
      default:
        return property.type;
    }
  };

  const getStatusLabel = () => {
    switch (property.status) {
      case PropertyStatus.AVAILABLE:
        return 'Có sẵn';
      case PropertyStatus.PENDING:
        return 'Đang chờ';
      case PropertyStatus.SOLD:
        return 'Đã bán';
      case PropertyStatus.RENTED:
        return 'Đã cho thuê';
      default:
        return property.status;
    }
  };

  if (viewMode === 'list') {
    return (
      <Card className="group hover:shadow-md transition-all duration-300 shadow-sm bg-background border">
        <div className="grid grid-cols-1 sm:grid-cols-3 h-auto sm:h-40 md:h-48 lg:h-52">
          <div className="relative col-span-1 sm:col-span-1 h-52 sm:h-full flex-shrink-0 md:h-48 lg:h-52">
            <div className="relative w-full h-full overflow-hidden rounded-t-lg sm:rounded-l-lg sm:rounded-tr-none">
              {images.length > 0 ? (
                <Image
                  src={images[currentImageIndex]}
                  alt={property.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                />
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <Building className="h-8 w-8 text-gray-400" />
                </div>
              )}

              <div className="absolute top-2 left-2">
                <Badge
                  variant="secondary"
                  className="bg-white/90 text-gray-900 font-semibold text-xs"
                >
                  {property.code}
                </Badge>
              </div>

              <div className="absolute top-2 right-2">
                <Badge className={cn('border text-xs', getStatusColor(property.status))}>
                  {getStatusLabel()}
                </Badge>
              </div>
            </div>
          </div>

          <CardContent className="col-span-1 sm:col-span-2 p-3 flex flex-col justify-between">
            <div className="space-y-2">
              <div className="text-lg sm:text-xl font-bold text-foreground">
                {getPrice()}
                {property.transactionType === TransactionType.FOR_RENT && (
                  <span className="text-xs font-normal text-foreground">/tháng</span>
                )}
              </div>

              <div className="flex items-center gap-2 flex-wrap">
                <PropertyTypeIcon className="h-3 w-3 text-foreground" />
                <span className="text-xs sm:text-sm font-medium text-foreground">
                  {getPropertyTypeLabel()}
                </span>
                <Badge variant="outline" className="text-xs px-1 py-0">
                  {getTransactionTypeLabel()}
                </Badge>
              </div>

              <div className="flex items-start gap-1">
                <MapPin className="h-3 w-3 text-foreground mt-0.5 flex-shrink-0" />
                <p className="text-xs sm:text-sm text-foreground line-clamp-2 sm:line-clamp-1">
                  {property.location.address}, {property.location.district}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-start gap-10">
              <div className="flex items-center gap-1">
                <div className="text-sm sm:text-base font-semibold text-foreground">
                  {property.propertyDetails.buildingArea || '-'}
                </div>
                <div className="text-xs text-muted-foreground truncate">m²</div>
              </div>
              <div className="flex items-center gap-1">
                <div className="text-sm sm:text-base font-semibold text-foreground">
                  {property.propertyDetails.landArea || '-'}
                </div>
                <div className="text-xs text-muted-foreground truncate">Diện tích đất</div>
              </div>
              <div className="flex items-center gap-1">
                <div className="text-sm sm:text-base font-semibold text-foreground">
                  {property.propertyDetails.numberOfFloors || '-'}
                </div>
                <div className="text-xs text-muted-foreground truncate">Số tầng</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-2">
              <div className="flex items-center gap-3 text-xs">
                <div className="flex items-center gap-1">
                  <Home className="h-3 w-3 text-foreground" />
                  <span>{property.propertyDetails.livingRooms || 0}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Bed className="h-3 w-3 text-foreground" />
                  <span>{property.propertyDetails.bedrooms || 0}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Bath className="h-3 w-3 text-foreground" />
                  <span>{property.propertyDetails.bathrooms || 0}</span>
                </div>
                <span className="text-muted-foreground">
                  {property.propertyDetails.buildingArea || '-'} m²
                </span>
              </div>

              <div className="flex gap-2 sm:gap-1">
                {property.isVerified ? (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full sm:w-auto h-7 px-3 text-xs"
                    onClick={() => onUnverify(property.id)}
                  >
                    Bỏ xác thực
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full sm:w-auto h-7 px-3 text-xs"
                    onClick={() => onVerify(property.id)}
                  >
                    Xác thực
                  </Button>
                )}
                <Button
                  size="sm"
                  className="flex-1 sm:flex-none w-full sm:w-auto h-7 px-3 text-xs"
                  onClick={() => setShowPreview(true)}
                >
                  Xem
                </Button>
              </div>
            </div>
          </CardContent>
        </div>

        <PropertyPreviewDialog
          property={property}
          open={showPreview}
          onOpenChange={setShowPreview}
        />
      </Card>
    );
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 shadow-sm bg-background border">
      <div className="relative">
        <div className="relative aspect-[4/3] sm:aspect-[16/11] lg:aspect-[16/10] overflow-hidden rounded-t-lg">
          {images.length > 0 ? (
            <>
              <Image
                src={images[currentImageIndex]}
                alt={property.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              />

              {hasMultipleImages && (
                <>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute left-1 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 sm:h-7 sm:w-7"
                    onClick={prevImage}
                  >
                    <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 -translate-y-1/2 bg-black/20 hover:bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 sm:h-7 sm:w-7"
                    onClick={nextImage}
                  >
                    <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                  </Button>
                </>
              )}

              {hasMultipleImages && (
                <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
                  {images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => goToImage(index)}
                      className={cn(
                        'w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full transition-all',
                        index === currentImageIndex ? 'bg-white' : 'bg-white/50 hover:bg-white/75'
                      )}
                    />
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <Building className="h-8 w-8 sm:h-10 sm:w-10 text-gray-400" />
            </div>
          )}

          <div className="absolute top-2 left-2">
            <Badge variant="secondary" className="bg-white/90 text-gray-900 font-semibold text-xs">
              {property.code}
            </Badge>
          </div>

          <div className="absolute top-2 right-2">
            <Badge className={cn('border text-xs', getStatusColor(property.status))}>
              {getStatusLabel()}
            </Badge>
          </div>
        </div>

        <CardContent className="p-3 sm:p-4 space-y-2 sm:space-y-3">
          <div className="flex items-start gap-1">
            <MapPin className="h-3 w-3 text-foreground mt-0.5 flex-shrink-0" />
            <p className="text-xs sm:text-sm text-foreground line-clamp-2 truncate">
              {property.location.address}, {property.location.district}
            </p>
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            <PropertyTypeIcon className="h-3 w-3 text-foreground" />
            <span className="text-xs sm:text-sm font-medium text-foreground">
              {getPropertyTypeLabel()}
            </span>
            <Badge variant="outline" className="text-xs px-1 py-0">
              {getTransactionTypeLabel()}
            </Badge>
          </div>

          <div className="text-lg sm:text-xl font-bold text-foreground">
            {getPrice()}
            {property.transactionType === TransactionType.FOR_RENT && (
              <span className="text-xs font-normal text-foreground">/tháng</span>
            )}
          </div>

          <Separator />

          <div className="grid grid-cols-3 gap-1">
            <div className="text-center">
              <div className="text-sm sm:text-base font-semibold text-foreground">
                {property.propertyDetails.buildingArea || '-'}
              </div>
              <div className="text-xs text-muted-foreground truncate">m²</div>
            </div>
            <div className="text-center">
              <div className="text-sm sm:text-base font-semibold text-foreground">
                {property.propertyDetails.landArea || '-'}
              </div>
              <div className="text-xs text-muted-foreground truncate">Diện tích đất</div>
            </div>
            <div className="text-center">
              <div className="text-sm sm:text-base font-semibold text-foreground">
                {property.propertyDetails.numberOfFloors || '-'}
              </div>
              <div className="text-xs text-muted-foreground truncate">Số tầng</div>
            </div>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 sm:gap-3 text-xs">
              <div className="flex items-center gap-1">
                <Home className="h-3 w-3 text-foreground" />
                <span>{property.propertyDetails.livingRooms || 0}</span>
              </div>
              <div className="flex items-center gap-1">
                <Bed className="h-3 w-3 text-foreground" />
                <span>{property.propertyDetails.bedrooms || 0}</span>
              </div>
              <div className="flex items-center gap-1">
                <Bath className="h-3 w-3 text-foreground" />
                <span>{property.propertyDetails.bathrooms || 0}</span>
              </div>
            </div>
            {property.yearBuilt !== 0 && (
              <div className="text-xs text-muted-foreground hidden sm:block">
                Năm xây dựng: {property.yearBuilt}
              </div>
            )}
          </div>

          <div className="flex gap-2">
            {property.isVerified ? (
              <Button
                variant="outline"
                className="w-full h-7 sm:h-8 text-xs sm:text-sm"
                onClick={() => onUnverify(property.id)}
              >
                Bỏ xác thực
              </Button>
            ) : (
              <Button
                variant="outline"
                className="w-full h-7 sm:h-8 text-xs sm:text-sm"
                onClick={() => onVerify(property.id)}
              >
                Xác thực
              </Button>
            )}
            <Button
              className="flex-1 w-full h-7 sm:h-8 text-xs sm:text-sm"
              onClick={() => setShowPreview(true)}
            >
              Xem
            </Button>
          </div>
        </CardContent>
      </div>

      <PropertyPreviewDialog property={property} open={showPreview} onOpenChange={setShowPreview} />
    </Card>
  );
}
