import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  PropertySearchParams,
  PropertyStatus,
  PropertyType,
  TransactionType,
} from '@/lib/api/services/fetchProperty';
import { cn } from '@/lib/utils';

interface PropertyFiltersProps {
  searchParams: Partial<PropertySearchParams>;
  onFilterChange: (params: PropertySearchParams) => void;
  onClearAll: () => void;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export function PropertyFilters({ ...props }: PropertyFiltersProps) {
  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const [activeTab, setActiveTab] = useState(() => {
    switch (props.searchParams.transactionType) {
      case TransactionType.FOR_RENT:
        return 'rentals';
      case TransactionType.FOR_SALE:
        return 'sales';
      default:
        return 'all';
    }
  });

  useEffect(() => {
    if (!props.searchParams.transactionType) {
      setActiveTab('all');
    }
  }, [props.searchParams.transactionType, props.searchParams]);

  const handleTransactionTypeChange = useCallback(
    (value: string) => {
      const transactionType =
        value === 'rentals'
          ? TransactionType.FOR_RENT
          : value === 'sales'
            ? TransactionType.FOR_SALE
            : undefined;
      setActiveTab(value);
      props.onFilterChange({
        ...props.searchParams,
        transactionType,
        pageNumber: 1,
      });
    },
    [props.searchParams, props.onFilterChange]
  );

  const handleFilterUpdate = useCallback(
    (updates: Partial<PropertySearchParams>) => {
      props.onFilterChange({
        ...props.searchParams,
        ...updates,
        pageNumber: 1,
      });
    },
    [props.searchParams, props.onFilterChange]
  );

  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (props.searchParams.type) count++;
    if (props.searchParams.status && props.searchParams.status !== PropertyStatus.AVAILABLE)
      count++;
    return count;
  }, [props.searchParams]);

  if (!isMounted) return null;

  return (
    <div className={cn('h-full flex flex-col', props.className)}>
      <div className="flex items-center justify-between p-3 lg:p-4 border-b bg-background lg:pl-6">
        <div className="flex items-center gap-2">
          <h3 className="text-base lg:text-lg font-semibold">Bộ lọc</h3>
          {activeFiltersCount > 0 && (
            <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
              {activeFiltersCount}
            </span>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-3 lg:p-4 space-y-3 lg:space-y-4 lg:pl-6">
        <Card className="border-border/50">
          <CardContent className="p-3 lg:p-4">
            <Tabs value={activeTab} onValueChange={handleTransactionTypeChange}>
              <TabsList className="grid w-full grid-cols-3 h-8 lg:h-9">
                <TabsTrigger value="all" className="text-xs">
                  Tất cả
                </TabsTrigger>
                <TabsTrigger value="sales" className="text-xs">
                  Bán
                </TabsTrigger>
                <TabsTrigger value="rentals" className="text-xs">
                  Cho thuê
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </CardContent>
        </Card>

        <Card className="border-border/50">
          <CardHeader className="pb-2 lg:pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Home className="h-4 w-4" />
              Danh mục
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <Select
              value={props.searchParams.type || 'all'}
              onValueChange={value =>
                handleFilterUpdate({
                  type: value === 'all' ? undefined : (value as PropertyType),
                })
              }
            >
              <SelectTrigger className="h-8 lg:h-9">
                <SelectValue placeholder="Chọn danh mục" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value={PropertyType.APARTMENT}>Chung cư</SelectItem>
                <SelectItem value={PropertyType.VILLA}>Biệt thự</SelectItem>
                <SelectItem value={PropertyType.SHOP_HOUSE}>Nhà phố</SelectItem>
                <SelectItem value={PropertyType.LAND_PLOT}>Đất</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        <Card className="border-border/50">
          <CardHeader className="pb-2 lg:pb-3">
            <CardTitle className="text-sm font-medium">Trạng thái</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <Select
              value={props.searchParams.status || PropertyStatus.AVAILABLE}
              onValueChange={value => handleFilterUpdate({ status: value as PropertyStatus })}
            >
              <SelectTrigger className="h-8 lg:h-9">
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={PropertyStatus.AVAILABLE}>Có sẵn</SelectItem>
                <SelectItem value={PropertyStatus.PENDING}>Đang chờ</SelectItem>
                <SelectItem value={PropertyStatus.SOLD}>Đã bán</SelectItem>
                <SelectItem value={PropertyStatus.RENTED}>Đã cho thuê</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>
      </div>

      <div className="p-3 lg:p-4 border-t bg-background lg:pl-6">
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={props.onClearAll}
            className="flex-1 h-8 lg:h-9 text-xs lg:text-sm"
          >
            Xóa tất cả
          </Button>
          <Button
            onClick={props.onClose}
            className="flex-1 h-8 lg:h-9 text-xs lg:text-sm lg:hidden"
          >
            Áp dụng
          </Button>
        </div>
      </div>
    </div>
  );
}
