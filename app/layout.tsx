import type { Metadata, Viewport } from 'next';
import './globals.css';
import { Inter } from 'next/font/google';
// import { Toaster } from 'sonner';
import { QueryProvider } from '@/lib/providers/queryProvider';
import { Toaster } from '@/components/ui/sonner';
import { NavigationProgressProvider } from '@/components/providers/navigationProgressProvider';
// import { I18nextProvider } from '@/components/i18next-provider';
// import { I18nextProvider } from '@/components/i18next-provider';
// import { ChatWidget } from '@/components/chatWidget';
// import { ChatWidget } from '@/components/chatWidget';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  metadataBase: new URL('https://www.revoland.vn'),
  title: 'RevoLand - Nền Tảng Công Nghệ Bất Động Sản Toàn <PERSON>',
  description:
    'RevoLand - G<PERSON><PERSON>i pháp bất động sản toàn di<PERSON>n, đáng tin cậy. <PERSON><PERSON><PERSON><PERSON> cung cấp dịch vụ mua bán, cho thuê nh<PERSON> đất, biệt thự, căn hộ và đất nền với đội ngũ chuyên gia uy tín, tận tâm.',
  applicationName: 'RevoLand',
  authors: [{ name: 'RevoLand', url: 'https://www.revoland.vn' }],
  keywords: [
    'RevoLand',
    'bất động sản',
    'cho thuê nhà đất',
    'tư vấn bất động sản',
    'đầu tư bất động sản',
    'mua bán bất động sản',
    'giải pháp bất động sản',
    'RevoGroup',
    'RevoLand Solutions',
  ],
  creator: 'RevoLand Team',
  publisher: 'RevoLand',
  openGraph: {
    title: 'RevoLand - Giải Pháp Bất Động Sản Toàn Diện',
    description:
      'RevoLand mang đến các giải pháp bất động sản tối ưu, đáp ứng mọi nhu cầu từ mua bán, cho thuê đến tư vấn đầu tư. Trải nghiệm dịch vụ chuyên nghiệp và đáng tin cậy tại RevoLand.',
    url: 'https://www.revoland.vn',
    siteName: 'RevoLand Official Website',
    images: [
      {
        url: '/banner_revoland.png',
        width: 1200,
        height: 630,
        alt: 'RevoLand Real Estate Banner',
      },
    ],
    locale: 'vi_VN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@RevoLand_official',
    title: 'RevoLand - Giải Pháp Bất Động Sản Toàn Diện',
    description:
      'Khám phá giải pháp bất động sản toàn diện tại RevoLand. Chúng tôi cung cấp dịch vụ mua bán, cho thuê, và tư vấn đầu tư bất động sản với sự chuyên nghiệp và tận tâm.',
    images: [
      {
        url: '/banner_revoland.png',
        alt: 'RevoLand Real Estate Banner',
      },
    ],
  },
  robots: {
    index: true,
    follow: true,
  },
  icons: {
    icon: '/LOGO_RV_red-01-01.png',
    apple: '/LOGO_RV_red-01-01.png',
  },
};

export const viewport: Viewport = {
  themeColor: '#1E293B',
  colorScheme: 'light',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <body className={inter.className}>
        <QueryProvider>
          <NavigationProgressProvider>
            <main>{children}</main>
            <Toaster richColors theme="light" closeButton={true} />
          </NavigationProgressProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
