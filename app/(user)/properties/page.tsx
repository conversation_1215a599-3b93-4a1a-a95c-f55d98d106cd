import { Suspense } from 'react';
import PropertiesClient from './components/PropertiesClient';
import { Skeleton } from '@/components/ui/skeleton';

function LoadingUI() {
  return (
    <div className="h-screen flex flex-col">
      <div className="h-16 border-b">
        <Skeleton className="h-full w-full" />
      </div>
      <div className="flex-1 grid grid-cols-2 gap-4">
        <Skeleton className="w-full h-full" />
        <Skeleton className="w-full h-full" />
      </div>
    </div>
  );
}

export default function PropertiesPage() {
  return (
    <Suspense fallback={<LoadingUI />}>
      <PropertiesClient />
    </Suspense>
  );
}
