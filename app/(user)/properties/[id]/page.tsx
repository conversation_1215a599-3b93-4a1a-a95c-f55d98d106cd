'use client';

import PropertyDetail from './component/PropertyDetail';
import Footer from '@/components/Footer';
import { useProperty } from '@/hooks/useProperty';
import PropertyShowcaseSkeleton from '@/components/propertyDetailSkeleton';
import { Property } from '@/lib/api/services/fetchProperty';
import { APIProvider } from '@vis.gl/react-google-maps';

export default function Page({ params }: { params: { id: string } }) {
  const { data, isLoading, error } = useProperty(params.id);

  if (isLoading) return <PropertyShowcaseSkeleton />;

  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className="font-mann">
      <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
        <PropertyDetail property={data?.data as Property} />
        <Footer />
      </APIProvider>
    </div>
  );
}
