'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';

import { Calendar as CalendarIcon } from 'lucide-react';
import { AppointmentResponse } from '@/lib/api/services/fetchAppointment';
import { useIsMobile } from '@/hooks/useMobile';
import Calendar20 from '@/components/calendar-20';

interface AppointmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  propertyId: string;
  propertyName: string;
}

export default function AppointmentDialog({
  isOpen,
  onClose,
  propertyId,
  propertyName,
}: AppointmentDialogProps) {
  const [appointmentData, setAppointmentData] = useState<AppointmentResponse['data'] | null>(null);
  const isMobile = useIsMobile();
  console.log(appointmentData);
  const handleClose = () => {
    setAppointmentData(null);
    onClose();
  };

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={handleClose}>
        <DrawerContent className="h-[90vh] font-mann">
          <DrawerHeader className="text-left">
            <DrawerTitle className="flex items-center gap-2 text-base">
              <CalendarIcon className="h-5 w-5" />
              Đặt lịch hẹn xem {propertyName}
            </DrawerTitle>
            <DrawerDescription className="text-xs">
              Chọn ngày và giờ phù hợp để xem bất động sản
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex-1 overflow-y-auto px-4">
            <Calendar20
              propertyId={propertyId}
              propertyName={propertyName}
              onSuccess={data => setAppointmentData(data)}
              onClose={handleClose}
              isDrawer={true}
            />
          </div>
          {/* {!appointmentData && (
            <DrawerFooter className="pt-2">
              <DrawerClose asChild>
                <Button variant="outline">Hủy</Button>
              </DrawerClose>
            </DrawerFooter>
          )} */}
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[650px] font-mann">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Đặt lịch hẹn xem {propertyName}
          </DialogTitle>
          <DialogDescription>Chọn ngày và giờ phù hợp để xem bất động sản</DialogDescription>
        </DialogHeader>
        <Calendar20
          propertyId={propertyId}
          propertyName={propertyName}
          onSuccess={data => setAppointmentData(data)}
          onClose={handleClose}
        />
      </DialogContent>
    </Dialog>
  );
}
