import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Clock, Phone } from 'lucide-react';
import { Property, TransactionType } from '@/lib/api/services/fetchProperty';

export default function ContactSideBar({
  property,
  onShowAppointment,
  topPosition = 'top-20',
}: {
  property: Property;
  onShowAppointment: () => void;
  topPosition?: string;
}) {
  function formatPriceShort(value: number): string {
    if (value >= 1_000_000_000) {
      return (value / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + ' tỷ';
    }
    if (value >= 1_000_000) {
      return (value / 1_000_000).toFixed(1).replace(/\.0$/, '') + ' triệu';
    }
    if (value >= 1_000) {
      return (value / 1_000).toFixed(1).replace(/\.0$/, '') + ' nghìn';
    }
    return value.toString();
  }
  return (
    <div className="lg:w-1/3">
      <div
        className={`sticky ${topPosition} border-2 border-zinc-200 rounded-2xl bg-background/50 p-6`}
      >
        {/* Price Section */}
        <div className="mb-6">
          {property.transactionType === TransactionType.FOR_SALE ? (
            <>
              <span className="text-sm text-muted-foreground ">Giá bán</span>
              <div className="text-2xl md:text-3xl font-medium mb-2">
                {formatPriceShort(property.priceDetails.salePrice || 0)}
              </div>
              <div className="text-lg text-foreground">
                {formatPriceShort(property.priceDetails.pricePerSquareMeter || 0)}{' '}
                <span className="text-sm text-muted-foreground">/m²</span>
              </div>
            </>
          ) : (
            <>
              <span className="text-sm text-muted-foreground">Giá thuê</span>
              <div className="text-2xl md:text-3xl font-medium mb-2">
                {formatPriceShort(property.priceDetails.rentalPrice || 0)}{' '}
                <span className="text-sm text-muted-foreground">/tháng</span>
              </div>
            </>
          )}
        </div>

        <Separator className="my-6" />

        {/* Action Buttons */}
        <div className="space-y-4 mb-6">
          <div className="relative">
            <Button className="w-full bg-red-600 text-white hover:bg-red-700" size="lg">
              <MessageCircle className="mr-2 h-4 w-4" />
              Liên hệ môi giới
            </Button>
            <Badge className="absolute -top-2 -right-2 border-transparent bg-gradient-to-r from-indigo-500 to-pink-500 [background-size:105%] bg-center text-white">
              Coming Soon
            </Badge>
          </div>
          <div className="relative">
            <Button className="w-full" size="lg" variant="outline" onClick={onShowAppointment}>
              <Clock className="mr-2 h-4 w-4" />
              Đặt lịch hẹn
            </Button>
          </div>
          <div className="relative">
            <Button className="w-full" size="lg" variant="outline">
              Tính toán khoản vay
            </Button>
            <Badge className="absolute -top-2 -right-2 border-transparent bg-gradient-to-r from-indigo-500 to-pink-500 [background-size:105%] bg-center text-white">
              Coming Soon
            </Badge>
          </div>
        </div>

        <Separator className="my-6" />

        {/* Agent Details */}
        <div className="space-y-4">
          <h3 className="font-semibold text-lg"> Liên hệ {property.title}</h3>
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
              <span className="text-red-600 font-semibold">
                {property.saler.fullName.charAt(0)}
              </span>
            </div>
            <div>
              <p className="font-medium">{property.saler.fullName}</p>
              <p className="text-sm text-muted-foreground"> Liên hệ môi giới</p>
            </div>
          </div>
          <Button className="w-full" variant="outline" size="sm">
            <Phone className="mr-2 h-4 w-4" />
            {property.saler.phoneNumber}
          </Button>
        </div>
      </div>
    </div>
  );
}
