import { Property, TransactionType, ApartmentOrientation } from '@/lib/api/services/fetchProperty';
import {
  BedDouble,
  Bath,
  Home,
  Calendar,
  MapPin,
  CheckCircle,
  Car,
  ArrowUpDown,
  Waves,
  Dumbbell,
  Shield,
  Wind,
  Building,
  Trees,
  PlayCircle,
  Power,
  Building2,
  HomeIcon,
  Store,
  LandPlot,
  Compass,
  DollarSign,
  Heart,
  Share2,
  Scan,
  ChefHat,
  AlertTriangle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import PropertyMap from './PropertyMap';

import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';
import AppointmentDialog from './AppointmentDialog';
import Image from 'next/image';
import { useFavoriteProperty } from '@/hooks/useFavoriteProperty';
import ContactSideBar from './ContactSideBar';

interface PropertyDetailsSectionProps {
  property: Property;
}

export default function PropertyDetailsSection({ property }: PropertyDetailsSectionProps) {
  const { addFavorite, removeFavorite, isAddingFavorite, isRemovingFavorite } =
    useFavoriteProperty();

  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFocusedImage, setShowFocusedImage] = useState(false);

  // Initialize favorite state from property
  const [isFavorite, setIsFavorite] = useState(property.isFavorite || false);

  // Update local state when property.isFavorite changes
  useEffect(() => {
    setIsFavorite(property.isFavorite || false);
  }, [property.isFavorite]);

  console.log(currentImageIndex);
  console.log(showFocusedImage);

  const handleFavoriteClick = () => {
    if (isFavorite) {
      removeFavorite(property.id);
      setIsFavorite(false);
    } else {
      addFavorite({
        propertyId: property.id,
        savedAt: new Date().toISOString(),
      });
      setIsFavorite(true);
    }
  };
  // function formatPriceShort(value: number): string {
  //   if (value >= 1_000_000_000) {
  //     return (value / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + ' tỷ';
  //   }
  //   if (value >= 1_000_000) {
  //     return (value / 1_000_000).toFixed(1).replace(/\.0$/, '') + ' triệu';
  //   }
  //   if (value >= 1_000) {
  //     return (value / 1_000).toFixed(1).replace(/\.0$/, '') + ' nghìn';
  //   }
  //   return value.toString();
  // }

  return (
    <div className="flex flex-col lg:flex-row gap-8">
      {/* Main Content - 2/3 width */}
      <div className="lg:w-2/3">
        {/* Property Header */}
        <div className="mb-4 md:mb-8">
          <div className="flex md:flex-row md:items-center justify-between gap-4 md:gap-0">
            <h1 className="text-2xl md:text-4xl font-medium mb-2">{property.title}</h1>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4"
                onClick={() => {
                  // Add share functionality here
                  if (navigator.share) {
                    navigator.share({
                      title: property.title,
                      url: window.location.href,
                    });
                  }
                }}
              >
                <Share2 className="h-3 w-3 md:h-4 md:w-4" />
                {/* <span className="hidden sm:inline">Chia sẻ</span> */}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  'flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4 transition-colors',
                  isFavorite &&
                    'text-red-600 border-red-600 hover:text-red-700 hover:border-red-700',
                  (isAddingFavorite || isRemovingFavorite) && 'opacity-50 cursor-not-allowed'
                )}
                onClick={handleFavoriteClick}
                disabled={isAddingFavorite || isRemovingFavorite}
              >
                <Heart className={cn('h-3 w-3 md:h-4 md:w-4', isFavorite && 'fill-current')} />
                {/* <span className="hidden sm:inline">Lưu</span> */}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4"
                onClick={() => {
                  // Add warning functionality here
                }}
              >
                <AlertTriangle className="h-3 w-3 md:h-4 md:w-4" />
                {/* <span className="hidden sm:inline">Lưu</span> */}
              </Button>
            </div>
          </div>
          <div className="flex items-center text-xs md:text-base text-muted-foreground">
            <MapPin className="size-3 md:size-4 mr-1" />
            <span>
              {property.location.address}, {property.location.district}, {property.location.city}
            </span>
          </div>
          {property.code && (
            <div className="flex items-center gap-2 mt-2">
              <span className="text-xs text-muted-foreground">
                Mã bất động sản: {property.code}
              </span>
            </div>
          )}
        </div>

        <Separator className="my-4 md:my-6" />

        {/* Property Specs */}
        <div className="flex flex-wrap gap-2 md:gap-4 mb-4 md:mb-8">
          <PropertySpec
            icon={<BedDouble className="size-4" />}
            value={`${property.propertyDetails.bedrooms} phòng ngủ`}
            label="Bedrooms"
          />
          <PropertySpec
            icon={<Bath className="size-4" />}
            value={`${property.propertyDetails.bathrooms} phòng tắm`}
            label="Bathrooms"
          />
          <PropertySpec
            icon={<Home className="size-4" />}
            value={`${property.propertyDetails.livingRooms} phòng khách`}
            label="Living Rooms"
          />
          <PropertySpec
            icon={<ChefHat className="size-4" />}
            value={`${property.propertyDetails.kitchens} phòng bếp`}
            label="Kitchens"
          />
          <PropertySpec
            icon={<Building2 className="size-4" />}
            value={`${property.propertyDetails.numberOfFloors} tầng`}
            label="Floors"
          />
          <PropertySpec
            icon={<Scan className="size-4" />}
            value={`${property.propertyDetails.buildingArea} m²`}
            label="Land Area"
          />
          <PropertySpec
            icon={<Calendar className="size-4" />}
            value={`${property.yearBuilt}`}
            label="Year Built"
          />
        </div>

        {/* Description Section */}
        <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
          <div className="p-4 md:p-6">
            <h2 className="text-base md:text-xl font-medium mb-2 md:mb-4 flex items-center gap-2">
              <Home className="size-4 md:size-5" />
              Mô tả
            </h2>
            <Separator className="mb-4 md:mb-6" />
            <p className="text-foreground font-sans font-light leading-relaxed text-sm md:text-base">
              {property.description}
            </p>
          </div>
        </div>

        {/* Features & Amenities */}
        <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl  bg-background/50 mb-4 md:mb-8">
          <div className="p-4 md:p-6">
            <h2 className="text-base md:text-xl font-medium mb-2 md:mb-4 flex items-center gap-2">
              <CheckCircle className="size-4 md:size-5" />
              Tiện ích & Tiện nghi
            </h2>
            <Separator className="mb-4 md:mb-6" />
            <div className="grid grid-cols-2 sm:grid-cols-2 gap-0">
              {Object.entries(property.amenities).map(
                ([amenity, isAvailable], index) =>
                  isAvailable && (
                    <div key={index} className="relative">
                      <div className="flex items-center gap-3 py-3">
                        {amenity === 'parking' && (
                          <Car className="size-4 md:size-5  flex-shrink-0" />
                        )}
                        {amenity === 'elevator' && (
                          <ArrowUpDown className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'swimmingPool' && (
                          <Waves className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'gym' && (
                          <Dumbbell className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'securitySystem' && (
                          <Shield className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'airConditioning' && (
                          <Wind className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'balcony' && (
                          <Building className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'garden' && (
                          <Trees className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'playground' && (
                          <PlayCircle className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        {amenity === 'backupGenerator' && (
                          <Power className="size-4 md:size-5 flex-shrink-0" />
                        )}
                        <span className="font-medium capitalize text-sm md:text-base">
                          {amenity === 'parking' && 'Bãi đỗ xe'}
                          {amenity === 'elevator' && 'Thang máy'}
                          {amenity === 'swimmingPool' && 'Hồ bơi'}
                          {amenity === 'gym' && 'Phòng gym'}
                          {amenity === 'securitySystem' && 'Hệ thống an ninh'}
                          {amenity === 'airConditioning' && 'Điều hòa'}
                          {amenity === 'balcony' && 'Ban công'}
                          {amenity === 'garden' && 'Vườn'}
                          {amenity === 'playground' && 'Sân chơi'}
                          {amenity === 'backupGenerator' && 'Máy phát điện'}
                        </span>
                      </div>
                      {/* Add separator if not the last item */}
                      {/* {index < array.length - 1 && (
                        <Separator className="absolute bottom-0 left-0 right-0" />
                      )} */}
                    </div>
                  )
              )}
            </div>
          </div>
        </div>
        {/* Location Details */}
        <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
          <div className="p-4 md:p-6">
            <h3 className="text-base md:text-xl font-medium mb-2 md:mb-4 flex items-center gap-2">
              <MapPin className="size-4 md:size-5" />
              Địa chỉ
            </h3>
            <Separator className="mb-4 md:mb-6" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-xs md:text-sm text-muted-foreground">Địa chỉ</p>
                <p className="font-medium">{property.location.address}</p>
              </div>
              <div>
                <p className="text-xs md:text-sm text-muted-foreground">Thành phố</p>
                <p className="font-medium">{property.location.city}</p>
              </div>
              <div>
                <p className="text-xs md:text-sm text-muted-foreground">Quận</p>
                <p className="font-medium">{property.location.district}</p>
              </div>
              <div>
                <p className="text-xs md:text-sm text-muted-foreground">Phường</p>
                <p className="font-medium">{property.location.ward}</p>
              </div>
            </div>

            {/* Property Map */}
            <div className="mt-4 md:mt-6">
              <PropertyMap
                latitude={property.location.latitude}
                longitude={property.location.longitude}
                address={property.location.address}
                title={property.title}
                imageUrl={property.imageUrls[0] || '/placeholder-property.jpg'}
                size={500}
              />
            </div>
          </div>
        </div>
        {/* Floor Plans Section */}
        <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
          <div className="p-4 md:p-6">
            <h2 className="text-base md:text-xl font-medium mb-2 md:mb-4 flex items-center gap-2">
              <Building2 className="size-4 md:size-5" />
              Bản vẽ tòa nhà
            </h2>
            <Separator className="mb-4 md:mb-6" />
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {property?.floorPlanUrls.map((floorPlan, index) => (
                <div
                  key={index}
                  className="relative aspect-[4/3] rounded-lg overflow-hidden border-2 border-zinc-200"
                >
                  <Image
                    src={floorPlan}
                    alt={`${property.title} - Floor Plan ${index + 1}`}
                    fill
                    className="object-cover hover:opacity-95 transition-opacity cursor-pointer"
                    onClick={() => {
                      setCurrentImageIndex(index);
                      setShowFocusedImage(true);
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Videos Section */}
        <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
          <div className="p-4 md:p-6">
            <h2 className="text-base md:text-xl font-medium mb-2 md:mb-4 flex items-center gap-2">
              <PlayCircle className="size-4 md:size-5" />
              Video giới thiệu
            </h2>
            <Separator className="mb-4 md:mb-6" />
            <div className="grid grid-cols-1 gap-4">
              <div className="relative aspect-video rounded-lg overflow-hidden bg-black">
                {property?.video?.videoUrl && (
                  <video
                    className="w-full h-full object-contain"
                    controls
                    playsInline
                    preload="metadata"
                  >
                    <source src={property.video.videoUrl} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                )}
              </div>
              <div className="mt-4">
                <h3 className="text-base md:text-lg font-medium mb-2">{property?.video?.title}</h3>
                <p className="text-muted-foreground text-xs md:text-sm">
                  {property?.video?.description}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Property Details */}
        <div className="md:border-2 border border-zinc-200 rounded-md md:rounded-xl bg-background/50 mb-4 md:mb-8">
          <div className="p-4 md:p-6">
            <h3 className="text-base md:text-xl font-medium mb-2 md:mb-4 flex items-center gap-2">
              <Home className="size-4 md:size-5" />
              Chi tiết
            </h3>
            <Separator className="mb-4 md:mb-6" />
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <DetailItem
                label="Loại bất động sản"
                value={
                  <div className="flex items-center gap-2">
                    {property.type === 'Apartment' && (
                      <Building2 className="size-4 md:size-5 text-red-600" />
                    )}
                    {property.type === 'Villa' && (
                      <HomeIcon className="size-4 md:size-5 text-red-600" />
                    )}
                    {property.type === 'ShopHouse' && (
                      <Store className="size-4 md:size-5 text-red-600" />
                    )}
                    {property.type === 'LandPlot' && (
                      <LandPlot className="size-4 md:size-5 text-red-600" />
                    )}
                    <span>
                      {property.type === 'Apartment' && 'Căn hộ'}
                      {property.type === 'Villa' && 'Biệt thự'}
                      {property.type === 'ShopHouse' && 'Nhà phố'}
                      {property.type === 'LandPlot' && 'Đất nền'}
                    </span>
                  </div>
                }
              />
              <DetailItem
                label="Trạng thái"
                value={
                  <div className="flex items-center gap-2">
                    {property.status === 'Available' && (
                      <CheckCircle className="size-4 md:size-5 text-green-600" />
                    )}
                    {property.status === 'Pending' && (
                      <Calendar className="size-4 md:size-5 text-yellow-600" />
                    )}
                    {property.status === 'Sold' && (
                      <CheckCircle className="size-4 md:size-5 text-red-600" />
                    )}
                    {property.status === 'Rented' && (
                      <CheckCircle className="size-4 md:size-5 text-blue-600" />
                    )}
                    <span>
                      {property.status === 'Available' && 'Có sẵn'}
                      {property.status === 'Pending' && 'Đang xử lý'}
                      {property.status === 'Sold' && 'Đã bán'}
                      {property.status === 'Rented' && 'Đã cho thuê'}
                    </span>
                  </div>
                }
              />
              <DetailItem
                label="Loại giao dịch"
                value={
                  <div className="flex items-center gap-2">
                    {property.transactionType === TransactionType.FOR_SALE && (
                      <DollarSign className="size-4 md:size-5 text-red-600" />
                    )}
                    {property.transactionType === TransactionType.FOR_RENT && (
                      <Calendar className="size-4 md:size-5 text-red-600" />
                    )}
                    <span>
                      {property.transactionType === TransactionType.FOR_SALE && 'Bán'}
                      {property.transactionType === TransactionType.FOR_RENT && 'Cho thuê'}
                    </span>
                  </div>
                }
              />
              <DetailItem label="Mã bất động sản" value={property.code} />
              <DetailItem label="Phòng khách" value={property.propertyDetails.livingRooms} />
              <DetailItem label="Phòng bếp" value={property.propertyDetails.kitchens} />
              <DetailItem label="Diện tích đất" value={`${property.propertyDetails.landArea} m²`} />
              <DetailItem
                label="Chiều rộng đất"
                value={`${property.propertyDetails.landWidth} m`}
              />
              <DetailItem
                label="Chiều dài đất"
                value={`${property.propertyDetails.landLength} m`}
              />
              <DetailItem label="Số tầng" value={property.propertyDetails.numberOfFloors} />
              <DetailItem
                label="Tầng hầm"
                value={property.propertyDetails.hasBasement ? 'Yes' : 'No'}
              />
              <DetailItem
                label="Trang bị nội thất"
                value={property.propertyDetails.furnished ? 'Yes' : 'No'}
              />
              <DetailItem
                label="Hướng của bất động sản"
                value={
                  <div className="flex items-center gap-2">
                    <Compass className="size-4 md:size-5 text-red-600" />
                    <span>
                      {property.propertyDetails.apartmentOrientation ===
                        ApartmentOrientation.NORTH && 'Hướng Bắc'}
                      {property.propertyDetails.apartmentOrientation ===
                        ApartmentOrientation.SOUTH && 'Hướng Nam'}
                      {property.propertyDetails.apartmentOrientation ===
                        ApartmentOrientation.EAST && 'Hướng Đông'}
                      {property.propertyDetails.apartmentOrientation ===
                        ApartmentOrientation.WEST && 'Hướng Tây'}
                      {property.propertyDetails.apartmentOrientation ===
                        ApartmentOrientation.NORTHEAST && 'Hướng Đông Bắc'}
                      {property.propertyDetails.apartmentOrientation ===
                        ApartmentOrientation.NORTHWEST && 'Hướng Tây Bắc'}
                      {property.propertyDetails.apartmentOrientation ===
                        ApartmentOrientation.SOUTHEAST && 'Hướng Đông Nam'}
                      {property.propertyDetails.apartmentOrientation ===
                        ApartmentOrientation.SOUTHWEST && 'Hướng Tây Nam'}
                    </span>
                  </div>
                }
              />
            </div>
          </div>
        </div>
      </div>

      {/* Sticky Sidebar - 1/3 width */}
      <ContactSideBar
        property={property}
        onShowAppointment={() => setShowAppointmentDialog(true)}
      />
      <AppointmentDialog
        isOpen={showAppointmentDialog}
        onClose={() => setShowAppointmentDialog(false)}
        propertyId={property.id}
        propertyName={property.title}
      />
    </div>
  );
}

function PropertySpec({
  icon,
  value,
  label,
}: {
  icon: React.ReactNode;
  value: React.ReactNode;
  label: string;
}) {
  console.log(label);
  return (
    <div className="flex flex-col items-center bg-zinc-50/50 p-2 rounded-md md:rounded-xl border md:border-2 border-zinc-200">
      <div className="flex items-center gap-2">
        {icon}
        <p className="text-foreground text-xs md:text-sm">{value}</p>
      </div>
      {/* <p className="text-muted-foreground text-sm">{label}</p> */}
    </div>
  );
}

function DetailItem({ label, value }: { label: string; value: React.ReactNode }) {
  return (
    <div className="flex justify-between p-3 bg-zinc-50/50 rounded-md border-2 border-zinc-200">
      <span className="text-muted-foreground">{label}:</span>
      <span className="font-medium">{value}</span>
    </div>
  );
}
