import { Property } from '@/lib/api/services/fetchProperty';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import {
  DollarSign,
  BadgeDollarSign,
  Percent,
  ShieldCheck,
  BarChart3,
  ArrowRight,
} from 'lucide-react';

interface PropertyPriceDetailsProps {
  property: Property;
}

export default function PropertyPriceDetails({ property }: PropertyPriceDetailsProps) {
  const hasRentalPrice = !!property.priceDetails.rentalPrice;
  const hasDepositAmount = !!property.priceDetails.depositAmount;
  const isForSale = property.transactionType === 'ForSale';
  const isForRent = property.transactionType === 'ForRent';

  return (
    <div className="mb-20">
      {/* Section Header */}
      <div className="mb-8">
        <div className="inline-flex items-center px-4 py-1.5 bg-red-50 rounded-full mb-3">
          <DollarSign className="h-4 w-4 text-red-600 mr-1.5" />
          <span className="text-sm font-medium text-red-600">Thông tin giá</span>
        </div>
        <h2 className="text-2xl md:text-3xl font-bold mb-3">Giá cả chi tiết</h2>
        <p className="text-muted-foreground text-base font-light max-w-3xl">Giá cả chi tiết</p>
      </div>

      {/* Price Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        {isForSale && (
          <div className="bg-background rounded-2xl p-6 border border-muted-foreground/30 hover:shadow-lg transition-shadow duration-300">
            <BadgeDollarSign className="h-8 w-8 text-red-600 mb-4" />
            <p className="text-sm text-muted-foreground mb-1">Giá bán</p>
            <p className="text-3xl font-bold">
              {formatCurrency(property.priceDetails.salePrice || 0)}
            </p>
            <div className="mt-4 pt-4 border-t border-muted-foreground/20">
              <div className="flex justify-between">
                <span className="text-xs text-muted-foreground">Giá mỗi mét vuông</span>
                <span className="text-sm font-medium">
                  {formatCurrency(property.priceDetails.pricePerSquareMeter || 0)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Giá Thuê chỉ hiện khi ForRent */}
        {isForRent &&
          (hasRentalPrice ? (
            <div className="bg-background rounded-2xl p-6 border border-muted-foreground/30 hover:shadow-lg transition-shadow duration-300">
              <BarChart3 className="h-8 w-8 text-red-600 mb-4" />
              <p className="text-sm text-muted-foreground mb-1">Giá thuê</p>
              <p className="text-3xl font-bold">
                {formatCurrency(property.priceDetails.rentalPrice || 0)}
              </p>
              <div className="mt-4 pt-4 border-t border-muted-foreground/20">
                <div className="flex justify-between">
                  <span className="text-xs text-muted-foreground">Tháng</span>
                  <span className="text-sm font-medium">
                    {formatCurrency((property.priceDetails.rentalPrice || 0) / 12)}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-background/50 rounded-2xl p-6 border border-dashed border-muted-foreground/30">
              <BarChart3 className="h-8 w-8 text-muted-foreground mb-4" />
              <p className="text-sm text-muted-foreground mb-1">Giá thuê</p>
              <p className="text-lg font-medium">Không cho thuê</p>
              <p className="text-xs text-muted-foreground mt-2">Chỉ cho thuê</p>
            </div>
          ))}

        {/* Tiền cọc hiển thị nếu có, không phụ thuộc loại giao dịch */}
        {hasDepositAmount ? (
          <div className="bg-background rounded-2xl p-6 border border-muted-foreground/30 hover:shadow-lg transition-shadow duration-300">
            <ShieldCheck className="h-8 w-8 text-red-600 mb-4" />
            <p className="text-sm text-muted-foreground mb-1">Tiền cọc</p>
            <p className="text-3xl font-bold">
              {formatCurrency(property.priceDetails.depositAmount || 0)}
            </p>
            <div className="mt-4 pt-4 border-t border-muted-foreground/20">
              <div className="flex justify-between">
                <span className="text-xs text-muted-foreground">Hoàn trả</span>
                <span className="inline-flex items-center text-sm font-medium text-green-500">
                  <span className="h-2 w-2 rounded-full bg-green-500 mr-1.5"></span> Có
                </span>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-background/50 rounded-2xl p-6 border border-dashed border-muted-foreground/30">
            <ShieldCheck className="h-8 w-8 text-muted-foreground mb-4" />
            <p className="text-sm text-muted-foreground mb-1">Tiền cọc</p>
            <p className="text-lg font-medium">Không áp dụng</p>
            <p className="text-xs text-muted-foreground mt-2">Không áp dụng</p>
          </div>
        )}
      </div>

      {/* Additional Costs */}
      <div className="bg-background rounded-2xl border border-muted-foreground/30 overflow-hidden hover:shadow-lg transition-shadow duration-300">
        <div className="p-6 border-b border-muted-foreground/20">
          <h3 className="text-xl font-semibold">Chi phí bổ sung</h3>
          <p className="text-sm text-muted-foreground mt-1">Chi phí bổ sung</p>
        </div>

        <div className="divide-y divide-muted-foreground/20">
          <CostItem
            label="Phí bảo trì"
            value={formatCurrency(property.priceDetails.maintenanceFee)}
            description="Phí bảo trì"
            icon={<Percent className="h-5 w-5 text-red-600" />}
          />

          <CostItem
            label="Thuế bất động sản"
            value={formatCurrency((property.priceDetails.salePrice || 0) * 0.01)}
            description="Thuế bất động sản"
            icon={<BadgeDollarSign className="h-5 w-5 text-red-600" />}
          />

          <CostItem
            label="Bảo hiểm"
            value={formatCurrency((property.priceDetails.salePrice || 0) * 0.005)}
            description="Bảo hiểm"
            icon={<ShieldCheck className="h-5 w-5 text-red-600" />}
          />
        </div>
      </div>

      {/* Note Card */}
      <div className="mt-8 bg-red-50 text-foreground p-5 rounded-xl border border-red-200 text-sm">
        <div className="flex gap-3">
          <div className="flex-shrink-0 mt-1">
            <div className="h-6 w-6 rounded-full bg-red-100 flex items-center justify-center">
              <BadgeDollarSign className="h-4 w-4 text-red-600" />
            </div>
          </div>
          <div>
            <p className="font-medium mb-1">Thông tin thanh toán</p>
            <button className="mt-3 inline-flex items-center text-red-600 text-sm font-medium">
              Yêu cầu tư vấn <ArrowRight className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function CostItem({
  label,
  value,
  description,
  icon,
}: {
  label: string;
  value: string;
  description: string;
  icon: React.ReactNode;
}) {
  return (
    <div className="p-6 hover:bg-muted/5 transition-colors">
      <div className="flex justify-between items-start">
        <div className="flex gap-3">
          <div className="flex-shrink-0 mt-1">
            <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
              {icon}
            </div>
          </div>
          <div>
            <p className="font-medium">{label}</p>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-xl font-semibold">{value}</p>
          <p className="text-xs text-muted-foreground">Per Year</p>
        </div>
      </div>
    </div>
  );
}
