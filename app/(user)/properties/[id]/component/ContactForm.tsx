import React from 'react';
import { Property } from '@/lib/api/services/fetchProperty';
import ContactFormComponent from '@/components/ContactFormComponent';

interface ContactFormProps {
  property: Property;
}

export default function ContactForm({ property }: ContactFormProps) {
  const propertyInfo = {
    id: property.id,
    name: property.title,
    code: property.code,
    address: property.location.address,
    contactName: property.contactName,
    contactPhone: property.contactPhone,
    contactEmail: property.contactEmail,
    propertyDetails: property.propertyDetails,
    type: property.type,
    bedrooms: property.propertyDetails?.bedrooms,
    bathrooms: property.propertyDetails?.bathrooms,
    landArea: property.propertyDetails?.landArea,
    imageUrls: property.imageUrls,
    yearBuilt: property.yearBuilt,
    status: property.status,
  };

  return <ContactFormComponent propertyInfo={propertyInfo} isPropertyDetail={true} />;
}
