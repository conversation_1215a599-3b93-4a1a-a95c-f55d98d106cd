import { Property } from '@/lib/api/services/fetchProperty';
import { useProperties } from '@/hooks/useProperty';
import { useEffect, useState } from 'react';
import { PropertyCard } from '@/components/PropertyCard';

interface TopPropertiesSectionProps {
  currentPropertyId: string;
}

export default function TopPropertiesSection({ currentPropertyId }: TopPropertiesSectionProps) {
  const [topProperties, setTopProperties] = useState<Property[]>([]);
  const { properties, isLoading } = useProperties({ pageSize: 8 });

  useEffect(() => {
    if (properties.length > 0) {
      const filteredProperties = properties
        .filter(property => property.id !== currentPropertyId)
        .slice(0, 8);
      setTopProperties(filteredProperties);
    }
  }, [properties, currentPropertyId]);

  if (isLoading || topProperties.length === 0) return null;

  return (
    <div className="mt-16 mb-8">
      <h2 className="text-2xl font-semibold mb-6"><PERSON><PERSON>t động sản tương tự</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {topProperties.map(property => (
          <PropertyCard key={property.id} property={property} />
        ))}
      </div>
    </div>
  );
}
