'use client';

import { Property } from '@/lib/api/services/fetchProperty';
import PropertyShowcase from './PropertiesShowcase';
import PropertyDetailsSection from './PropertyDetailsSection';
import TopPropertiesSection from './TopPropertiesSection';

type PropertyDetailProps = {
  property: Property;
};

export default function PropertyDetail({ property }: PropertyDetailProps) {
  return (
    <div className="min-h-screen bg-background text-foreground scrollbar-hide">
      <div className="w-full max-w-screen px-4 md:px-8 xl:px-8 py-4 mx-auto bg-background text-foreground ">
        <PropertyShowcase property={property} />
        <div className="xl:px-32">
          <PropertyDetailsSection property={property} />
          <TopPropertiesSection currentPropertyId={property.id} />
          {/* <PropertyPriceDetails property={property} />
          <ContactForm property={property} /> */}
        </div>
      </div>
    </div>
  );
}
