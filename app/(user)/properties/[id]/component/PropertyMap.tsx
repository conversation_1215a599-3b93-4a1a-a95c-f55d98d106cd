'use client';

import { useState, useCallback } from 'react';
import { Map, AdvancedMarker, InfoWindow, useAdvancedMarkerRef } from '@vis.gl/react-google-maps';
import Image from 'next/image';

import { HomeIcon } from 'lucide-react';

interface PropertyMapProps {
  latitude: number;
  longitude: number;
  address: string;
  title: string;
  imageUrl: string;
  size: number;
}

// Custom marker component that handles the pin and hover effects
function PropertyMarker({
  position,
  onClick,
  isHovered,
}: {
  position: google.maps.LatLngLiteral;
  onClick: () => void;
  isHovered: boolean;
}) {
  const [markerRef, marker] = useAdvancedMarkerRef();
  console.log(marker);
  console.log(isHovered);
  return (
    <AdvancedMarker ref={markerRef} position={position} onClick={onClick}>
      {/* TODO: Add a rounded circle with a red background */}
      <div className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
        <HomeIcon className="w-5 h-5 text-white" />
      </div>
    </AdvancedMarker>
  );
}

// Custom info window component that displays property details
function PropertyInfoWindow({
  position,
  title,
  address,
  imageUrl,
  onClose,
}: {
  position: google.maps.LatLngLiteral;
  title: string;
  address: string;
  imageUrl: string;
  onClose: () => void;
}) {
  return (
    <InfoWindow
      position={position}
      onCloseClick={onClose}
      headerDisabled
      shouldFocus={false}
      disableAutoPan={true}
      pixelOffset={[0, -50]}
    >
      <div className="bg-white flex items-center gap-3 rounded-lg w-72 shadow-lg">
        <div className="w-24 h-16 flex-shrink-0">
          <Image
            src={imageUrl}
            alt={title}
            width={96}
            height={64}
            className="w-full h-full object-cover rounded-md"
          />
        </div>
        <div className="flex flex-col">
          <h3 className="font-medium text-base mb-1">{title}</h3>
          <p className="text-xs text-gray-600">{address}</p>
        </div>
      </div>
    </InfoWindow>
  );
}

// Main component
export default function PropertyMap({
  latitude,
  longitude,
  address,
  title,
  imageUrl,
}: PropertyMapProps) {
  const [isHovered] = useState(false);
  const [showInfoWindow, setShowInfoWindow] = useState(true);

  const position = {
    lat: latitude,
    lng: longitude,
  };

  const handleMarkerClick = useCallback(() => {
    setShowInfoWindow(prev => !prev);
  }, []);

  const handleInfoWindowClose = useCallback(() => {
    setShowInfoWindow(false);
  }, []);

  return (
    <div className={`w-full h-[500px] rounded-xl overflow-hidden border-2 border-zinc-200`}>
      <Map
        defaultCenter={position}
        defaultZoom={15}
        mapId={process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID}
        mapTypeControl={false}
        fullscreenControl={false}
        streetViewControl={false}
        className="w-full h-full"
      >
        <PropertyMarker position={position} onClick={handleMarkerClick} isHovered={isHovered} />
        {showInfoWindow && (
          <PropertyInfoWindow
            position={position}
            title={title}
            address={address}
            imageUrl={imageUrl}
            onClose={handleInfoWindowClose}
          />
        )}
      </Map>
    </div>
  );
}
