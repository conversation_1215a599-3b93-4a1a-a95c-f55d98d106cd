'use client';

import Image from 'next/image';
import {
  ChevronLeft,
  ChevronRight,
  //MapPin,
  X,
  ZoomIn,
  ZoomOut,
  Camera,
  ChevronRight as ChevronRightIcon,
  Search,
  Home,
  MapPin,
  Building2,
  PlayCircle,
} from 'lucide-react';
import { useState, useEffect, useRef, useLayoutEffect } from 'react';
//import { formatCurrency } from '@/utils/numbers/formatCurrency';
import { Property } from '@/lib/api/services/fetchProperty';
import { Button } from '@/components/ui/button';
import { AnimatePresence, motion } from 'framer-motion';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PropertyMap from './PropertyMap';
import ContactSideBar from './ContactSideBar';
import AppointmentDialog from './AppointmentDialog';

interface PropertyShowcaseProps {
  property: Property;
}

export default function PropertyShowcase({ property }: PropertyShowcaseProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showAllImages, setShowAllImages] = useState(false);
  const [showFocusedImage, setShowFocusedImage] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [preloadedImages, setPreloadedImages] = useState<number[]>([0]);
  const [isImageLoading, setIsImageLoading] = useState(true);
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('images');
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const [underlineStyle, setUnderlineStyle] = useState({ left: 0, width: 0 });

  // Only display first 5 images in the grid
  const displayImages = property.imageUrls.slice(0, 7);

  // Preload next and previous images
  useEffect(() => {
    const preloadImage = (index: number) => {
      if (index >= 0 && index < property.imageUrls.length && !preloadedImages.includes(index)) {
        const img = new window.Image();
        img.src = property.imageUrls[index];
        setPreloadedImages(prev => [...prev, index]);
      }
    };

    // Preload next image
    const nextIndex = (currentImageIndex + 1) % property.imageUrls.length;
    preloadImage(nextIndex);

    // Preload previous image
    const prevIndex =
      currentImageIndex === 0 ? property.imageUrls.length - 1 : currentImageIndex - 1;
    preloadImage(prevIndex);
  }, [currentImageIndex, property.imageUrls, preloadedImages]);

  // Animated underline effect for tabs
  useLayoutEffect(() => {
    const tabs = ['images', 'map', 'floor-plans', 'videos'];
    const activeIndex = tabs.findIndex(tab => tab === activeTab);
    const activeTabElement = tabRefs.current[activeIndex];

    if (activeTabElement) {
      const { offsetLeft, offsetWidth } = activeTabElement;

      setUnderlineStyle({
        left: offsetLeft,
        width: offsetWidth,
      });
    }
  }, [activeTab]);

  const handleNextImage = () => {
    setIsImageLoading(true);
    setCurrentImageIndex(prev => (prev === property.imageUrls.length - 1 ? 0 : prev + 1));
  };

  const handlePrevImage = () => {
    setIsImageLoading(true);
    setCurrentImageIndex(prev => (prev === 0 ? property.imageUrls.length - 1 : prev - 1));
  };

  const toggleZoom = () => {
    setIsZoomed(!isZoomed);
  };

  const openFocusedImage = (index: number) => {
    setIsImageLoading(true);
    setCurrentImageIndex(index);
    setShowFocusedImage(true);
  };

  return (
    <div className="mb-6">
      {/* Breadcrumb Navigation */}
      <nav className="flex flex-col md:flex-row md:items-center justify-between gap-4 md:gap-0 mb-6">
        <div className="flex flex-wrap items-center gap-1 md:gap-2 text-sm text-muted-foreground">
          <Link
            href="/properties"
            className="hover:text-foreground transition-colors flex items-center gap-1 md:gap-2"
          >
            <Search className="size-3 md:size-4" />
            <span className="hidden sm:inline">Tìm kiếm</span>
          </Link>
          <ChevronRightIcon className="size-3 md:size-4" />
          <Link
            href={`/properties?city=${property.location.city}`}
            className="hover:text-foreground transition-colors"
          >
            {property.location.city}
          </Link>
          <ChevronRightIcon className="size-3 md:size-4" />
          <div className="text-foreground transition-colors flex items-center gap-1 md:gap-2">
            <Home className="size-3 md:size-4" />
            <span className="font-medium line-clamp-1">{property.title}</span>
          </div>
        </div>

        {/* <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4"
            onClick={() => {
              // Add share functionality here
              if (navigator.share) {
                navigator.share({
                  title: property.title,
                  url: window.location.href,
                });
              }
            }}
          >
            <Share2 className="h-3 w-3 md:h-4 md:w-4" />
            <span className="hidden sm:inline">Chia sẻ</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9 px-2 md:px-4"
            onClick={() => {
              // Add save functionality here
            }}
          >
            <Heart className="h-3 w-3 md:h-4 md:w-4" />
            <span className="hidden sm:inline">Lưu</span>
          </Button>
        </div> */}
      </nav>

      {/* Main Gallery Layout - 5 Images */}
      <div className="relative w-full mb-4 md:mb-8 rounded-xl overflow-hidden group">
        {/* Mobile View - Single Column */}
        <div className="md:hidden space-y-1 md:space-y-2">
          <div className="relative aspect-[4/3] cursor-zoom-in overflow-hidden rounded-md md:rounded-xl">
            {/* Skeleton Loading */}
            {isImageLoading && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-md md:rounded-xl" />
            )}
            <Image
              src={displayImages[0]}
              alt={`${property.title} - Main View`}
              fill
              className={cn(
                'object-cover rounded-md md:rounded-xl transition-transform duration-300 hover:scale-105',
                isImageLoading ? 'opacity-0' : 'opacity-100'
              )}
              sizes="100vw"
              priority
              loading="eager"
              quality={75}
              onLoad={() => setIsImageLoading(false)}
            />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-1 md:gap-2">
            {displayImages.slice(1, 7).map((image, index) => (
              <motion.div
                key={index}
                className="relative aspect-[4/3] cursor-zoom-in overflow-hidden rounded-md md:rounded-xl"
                onClick={() => {
                  setCurrentImageIndex(index + 1);
                  setShowAllImages(true);
                }}
              >
                <Image
                  src={image}
                  alt={`${property.title} - View ${index + 2}`}
                  fill
                  className="object-cover transition-transform duration-300 hover:scale-105"
                  sizes="50vw"
                  loading="lazy"
                  quality={75}
                />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Desktop View - Original Layout */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-5 gap-2 h-[40rem]">
          {/* Main Large Image - 2/5 width */}
          <div
            className="col-span-1 md:col-span-2 relative cursor-zoom-in overflow-hidden"
            onClick={() => {
              setCurrentImageIndex(0);
              setShowAllImages(true);
            }}
          >
            {/* Skeleton Loading */}
            {isImageLoading && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-xl" />
            )}
            <Image
              src={displayImages[0]}
              alt={`${property.title} - Main View`}
              fill
              className={cn(
                'object-cover rounded-xl transition-transform duration-300 hover:scale-105',
                isImageLoading ? 'opacity-0' : 'opacity-100'
              )}
              sizes="(max-width: 768px) 100vw, 40vw"
              priority
              loading="eager"
              quality={75}
              onLoad={() => setIsImageLoading(false)}
            />
          </div>

          {/* Right Side Grid - 6 Images - 3/5 width */}
          <div className="hidden md:grid md:col-span-3 grid-cols-3 grid-rows-2 gap-2">
            {displayImages.slice(1, 7).map((image, index) => (
              <motion.div
                key={index}
                className="relative cursor-zoom-in overflow-hidden rounded-xl"
                onClick={() => {
                  setCurrentImageIndex(index + 1);
                  setShowAllImages(true);
                }}
              >
                <Image
                  src={image}
                  alt={`${property.title} - View ${index + 2}`}
                  fill
                  className="object-cover transition-transform duration-300 hover:scale-105"
                  sizes="(max-width: 768px) 100vw, 30vw"
                  loading="lazy"
                  quality={75}
                />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Show All Photos Button - Mobile */}
        <Button
          variant="outline"
          className="md:hidden text-xs absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm text-black font-medium rounded-full shadow-lg hover:bg-white hover:scale-105 transition-all duration-300 opacity-90 group-hover:opacity-100"
          onClick={() => setShowAllImages(true)}
        >
          <Camera className="size-3 mr-1" />
          Xem tất cả {property.imageUrls.length} ảnh
        </Button>

        {/* Show All Photos Button - Desktop */}
        <Button
          variant="outline"
          className="hidden md:flex absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm text-black font-medium rounded-full shadow-lg hover:bg-white hover:scale-105 transition-all duration-300 opacity-90 group-hover:opacity-100"
          onClick={() => setShowAllImages(true)}
        >
          <Camera className="w-4 h-4 mr-2" />
          Xem tất cả {property.imageUrls.length} ảnh
        </Button>

        {/* Image Counter Badge */}
        <div className="absolute top-4 right-4 bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          {currentImageIndex + 1} / {property.imageUrls.length}
        </div>
      </div>

      {/* Full Gallery Modal - Mobile Optimized */}
      {showAllImages && (
        <div className="fixed inset-0 bg-white z-50 overflow-y-auto">
          {/* Gallery Header */}
          <div className="sticky top-0 z-10 flex justify-between items-center p-4 bg-white border-b">
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full hover:bg-gray-100"
              onClick={() => setShowAllImages(false)}
            >
              <X className="h-5 w-5" />
            </Button>
            <h2 className="text-lg font-medium">{property.title} Photos</h2>
            <div className="w-10" /> {/* Empty div for balance */}
          </div>

          {/* Main Content */}
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Gallery Grid - Responsive */}
              <div className="lg:w-2/3">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="w-full p-0 bg-background justify-start border-b rounded-none mb-6 relative">
                    <TabsTrigger
                      value="images"
                      ref={el => {
                        tabRefs.current[0] = el;
                      }}
                      className="bg-background dark:data-[state=active]:bg-background relative z-10 rounded-none border-0 data-[state=active]:shadow-none"
                    >
                      <div className="flex items-center gap-2">
                        <Camera className="h-4 w-4" />
                        <span>Hình ảnh</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger
                      value="map"
                      ref={el => {
                        tabRefs.current[1] = el;
                      }}
                      className="bg-background dark:data-[state=active]:bg-background relative z-10 rounded-none border-0 data-[state=active]:shadow-none"
                    >
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span>Bản đồ</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger
                      value="floor-plans"
                      ref={el => {
                        tabRefs.current[2] = el;
                      }}
                      className="bg-background dark:data-[state=active]:bg-background relative z-10 rounded-none border-0 data-[state=active]:shadow-none"
                    >
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        <span>Bản vẽ</span>
                      </div>
                    </TabsTrigger>
                    <TabsTrigger
                      value="videos"
                      ref={el => {
                        tabRefs.current[3] = el;
                      }}
                      className="bg-background dark:data-[state=active]:bg-background relative z-10 rounded-none border-0 data-[state=active]:shadow-none"
                    >
                      <div className="flex items-center gap-2">
                        <PlayCircle className="h-4 w-4" />
                        <span>Video</span>
                      </div>
                    </TabsTrigger>

                    <motion.div
                      className="bg-primary absolute bottom-0 z-20 h-0.5"
                      layoutId="underline"
                      style={{
                        left: underlineStyle.left,
                        width: underlineStyle.width,
                      }}
                      transition={{
                        type: 'spring',
                        stiffness: 400,
                        damping: 40,
                      }}
                    />
                  </TabsList>

                  <TabsContent value="images">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      {property.imageUrls.map((image, index) => (
                        <div
                          key={index}
                          className="relative cursor-pointer aspect-[4/3] rounded-lg overflow-hidden"
                          onClick={() => openFocusedImage(index)}
                        >
                          <Image
                            src={image}
                            alt={`${property.title} - View ${index + 1}`}
                            fill
                            className="object-cover hover:opacity-95 transition-opacity"
                            loading="lazy"
                            quality={75}
                          />
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="map">
                    <div className="aspect-[4/3] rounded-lg overflow-hidden">
                      <PropertyMap
                        latitude={property.location.latitude}
                        longitude={property.location.longitude}
                        address={property.location.address}
                        title={property.title}
                        imageUrl={property.imageUrls[0] || '/placeholder-property.jpg'}
                        size={500}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="floor-plans">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {property.floorPlanUrls.map((floorPlan, index) => (
                        <div
                          key={index}
                          className="relative cursor-pointer aspect-[4/3] rounded-lg overflow-hidden"
                          onClick={() => openFocusedImage(index)}
                        >
                          <Image
                            src={floorPlan}
                            alt={`${property.title} - Floor Plan ${index + 1}`}
                            fill
                            className="object-cover hover:opacity-95 transition-opacity"
                            loading="lazy"
                            quality={75}
                          />
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="videos">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="relative aspect-video rounded-lg overflow-hidden bg-black">
                        {property?.video?.videoUrl && (
                          <video
                            className="w-full h-full object-contain"
                            controls
                            playsInline
                            preload="metadata"
                          >
                            <source src={property.video.videoUrl} type="video/mp4" />
                            Your browser does not support the video tag.
                          </video>
                        )}
                      </div>
                      <div className="mt-4">
                        <h3 className="text-lg font-medium mb-2">{property?.video?.title}</h3>
                        <p className="text-muted-foreground">{property?.video?.description}</p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Sticky Sidebar */}
              <ContactSideBar
                property={property}
                onShowAppointment={() => setShowAppointmentDialog(true)}
                topPosition="top-24"
              />
            </div>
          </div>
        </div>
      )}

      {/* Focused Image Modal */}
      {showFocusedImage && (
        <div className="fixed inset-0 bg-black z-50 flex flex-col">
          {/* Header */}
          <div className="flex justify-between items-center p-4 text-white">
            <span className="text-sm">
              {currentImageIndex + 1} / {property.imageUrls.length}
            </span>
            <Button
              variant="ghost"
              size="icon"
              className="rounded-full text-white hover:bg-gray-800"
              onClick={() => setShowFocusedImage(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Focused Image */}
          <div className="flex-1 flex items-center justify-center relative">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentImageIndex}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className={cn(
                  'relative w-full h-full flex items-center justify-center',
                  isZoomed ? 'cursor-zoom-out' : 'cursor-zoom-in'
                )}
                onClick={toggleZoom}
              >
                {/* Skeleton Loading */}
                {isImageLoading && (
                  <div className="absolute inset-0 bg-gray-800 animate-pulse rounded-xl" />
                )}
                <div className={cn('relative', isZoomed ? 'w-full h-full' : 'w-4/5 h-4/5')}>
                  <Image
                    src={property.imageUrls[currentImageIndex]}
                    alt={`${property.title} - View ${currentImageIndex + 1}`}
                    fill
                    className={cn(
                      'object-contain transition-transform duration-300',
                      isZoomed ? 'scale-125' : 'scale-100',
                      isImageLoading ? 'opacity-0' : 'opacity-100'
                    )}
                    quality={90}
                    priority
                    onLoad={() => setIsImageLoading(false)}
                  />
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Controls */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-4 rounded-full bg-black/50 text-white hover:bg-black/70"
              onClick={e => {
                e.stopPropagation();
                handlePrevImage();
              }}
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className="absolute right-4 rounded-full bg-black/50 text-white hover:bg-black/70"
              onClick={e => {
                e.stopPropagation();
                handleNextImage();
              }}
            >
              <ChevronRight className="h-6 w-6" />
            </Button>

            {/* Zoom Control */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute bottom-4 right-4 rounded-full bg-black/50 text-white hover:bg-black/70"
              onClick={e => {
                e.stopPropagation();
                toggleZoom();
              }}
            >
              {isZoomed ? <ZoomOut className="h-5 w-5" /> : <ZoomIn className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      )}

      {/* Appointment Dialog */}
      <AppointmentDialog
        isOpen={showAppointmentDialog}
        onClose={() => setShowAppointmentDialog(false)}
        propertyId={property.id}
        propertyName={property.title}
      />
    </div>
  );
}
