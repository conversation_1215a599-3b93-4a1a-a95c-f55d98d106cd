import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { PropertyCard } from '@/components/PropertyCard';
import { AlertTriangle, Search } from 'lucide-react';
import { Property } from '@/lib/api/services/fetchProperty';

interface PropertyListingsProps {
  properties: Property[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isFetching: boolean;
  count: number;
  totalPages: number;
  page: number;
  onPageChange: (page: number) => void;
  onPropertyHover?: (propertyId: string | null) => void;
  containerWidth?: number;
}

// Custom hook to measure container width
// function useContainerWidth() {
//   const containerRef = useRef<HTMLDivElement>(null);
//   const [width, setWidth] = useState<number>(0);
//   const [isInitialized, setIsInitialized] = useState(false);

//   useEffect(() => {
//     if (!containerRef.current) return;

//     const updateWidth = () => {
//       if (containerRef.current) {
//         const newWidth = containerRef.current.getBoundingClientRect().width;
//         if (newWidth > 0) {
//           setWidth(newWidth);
//           setIsInitialized(true);
//         }
//       }
//     };

//     // Initial measurement with a small delay to allow panel to settle
//     const initialTimer = setTimeout(updateWidth, 100);

//     // Use ResizeObserver for more reliable measurements
//     const resizeObserver = new ResizeObserver(() => {
//       updateWidth();
//     });

//     resizeObserver.observe(containerRef.current);

//     // Force an update after a short delay to ensure we get the correct width
//     const forceUpdateTimer = setTimeout(updateWidth, 500);

//     return () => {
//       clearTimeout(initialTimer);
//       clearTimeout(forceUpdateTimer);
//       resizeObserver.disconnect();
//     };
//   }, []);

//   // Force a re-render when the component is mounted
//   useEffect(() => {
//     if (!isInitialized && containerRef.current) {
//       const timer = setTimeout(() => {
//         const newWidth = containerRef.current?.getBoundingClientRect().width ?? 0;
//         if (newWidth > 0) {
//           setWidth(newWidth);
//           setIsInitialized(true);
//         }
//       }, 1000);
//       return () => clearTimeout(timer);
//     }
//   }, [isInitialized]);

//   return { containerRef, width, isInitialized };
// }

// Helper function to determine grid columns based on width
function getGridColumns(width: number): number {
  if (width >= 1200) return 4;
  if (width >= 900) return 3;
  if (width >= 600) return 2;
  return 1;
}

export default function PropertyListings({
  properties,
  isLoading,
  isError,
  error,
  isFetching,
  //count,
  totalPages,
  page,
  onPageChange,
  onPropertyHover,
  containerWidth = 0,
}: PropertyListingsProps) {
  const gridColumns = getGridColumns(containerWidth);

  // Add debug logging
  useEffect(() => {
    console.log('Container width:', containerWidth, 'Grid columns:', gridColumns);
  }, [containerWidth, gridColumns]);

  // Loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 12 }).map((_, i) => (
          <Card key={i} className="bg-background border-muted-foreground/20">
            <Skeleton className="w-full h-48 rounded-t-lg" />
            <div className="p-4 space-y-3">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <div className="flex gap-3 mt-3">
                <Skeleton className="h-4 w-14" />
                <Skeleton className="h-4 w-14" />
                <Skeleton className="h-4 w-14" />
              </div>
              <div className="flex justify-between items-center mt-4">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-8 w-24 rounded-md" />
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="flex justify-center items-center min-h-[500px] bg-background text-foreground text-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Lỗi</h3>
          <p className="text-muted-foreground mb-2">Đã xảy ra lỗi khi tải bất động sản</p>
          <p className="text-sm text-destructive mb-4">
            {error instanceof Error ? error.message : 'Đã xảy ra lỗi khi tải bất động sản'}
          </p>
          <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
            Tải lại
          </Button>
        </div>
      </div>
    );
  }

  // Empty results state
  if (!properties || properties.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-[500px] bg-background text-foreground text-center">
        <div className="text-center max-w-md mx-auto">
          <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Không tìm thấy bất động sản</h3>
          <p className="text-muted-foreground mb-6">Không tìm thấy bất động sản</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => (window.location.href = '/properties')}
          >
            Xóa bộ lọc
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full">
      {/* Properties grid with loading overlay when fetching */}
      <div className="relative w-full">
        {isFetching && !isLoading && (
          <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-10 backdrop-blur-sm">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-primary font-medium">Đang cập nhật</p>
            </div>
          </div>
        )}

        <div
          className="grid gap-3 w-full"
          style={{
            gridTemplateColumns: `repeat(${gridColumns}, minmax(0, 1fr))`,
            minWidth: '100%',
            width: '100%',
          }}
        >
          {properties.map((property, index) => (
            <PropertyCard
              key={property.id}
              property={property}
              priority={index < 6}
              onHover={onPropertyHover}
            />
          ))}
        </div>
      </div>

      {totalPages > 1 && (
        <Pagination className="mt-12">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => onPageChange(Math.max(page - 1, 1))}
                className={page === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
              />
            </PaginationItem>

            {Array.from({ length: totalPages }).map((_, i) => (
              <PaginationItem key={i}>
                <PaginationLink isActive={page === i + 1} onClick={() => onPageChange(i + 1)}>
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                onClick={() => onPageChange(Math.min(page + 1, totalPages))}
                className={
                  page === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
