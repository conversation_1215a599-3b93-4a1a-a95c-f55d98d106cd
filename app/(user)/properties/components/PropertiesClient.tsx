'use client';

import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { APIProvider } from '@vis.gl/react-google-maps';
import { Button } from '@/components/ui/button';
import { ArrowUpDown } from 'lucide-react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import PropertyListings from './PropertyListings';
import SearchFilter from './SearchFilter';
import PropertiesMap from './PropertiesMap';
import { useProperties } from '@/hooks/useProperty';
import {
  PropertySearchParams,
  PropertyType,
  TransactionType,
  Property,
} from '@/lib/api/services/fetchProperty';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

// Add SearchFilterSkeleton component
function SearchFilterSkeleton() {
  return (
    <section className="w-full max-w-screen mx-auto bg-background text-foreground font-mann">
      <div className="ml-4">
        <div className="w-full border-0 bg-card overflow-hidden">
          <div className="p-2 space-y-2">
            <div className="flex flex-row gap-2">
              {/* Search Input Skeleton */}
              <div className="max-xl:hidden relative w-full md:w-[300px] flex-shrink-0">
                <Skeleton className="h-10 w-full" />
              </div>

              {/* Filter Buttons Skeletons */}
              <div className="flex-1 flex flex-wrap gap-2 max-md:hidden">
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
                <Skeleton className="h-10 w-[120px]" />
              </div>

              {/* Search and More Filters Buttons Skeletons */}
              <div className="col-span-2 sm:col-span-3 md:col-span-4 flex flex-row gap-2">
                <Skeleton className="h-10 w-[100px]" />
                <Skeleton className="max-md:hidden h-10 w-[40px]" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Add MobileHeaderButtonSkeleton component
function MobileHeaderButtonSkeleton() {
  return (
    <div className="flex items-center gap-2">
      <Skeleton className="h-10 w-[200px] rounded-full" />
      <Skeleton className="h-6 w-6 rounded-full" />
    </div>
  );
}

export default function PropertiesClient() {
  const searchParams = useSearchParams();
  const [page, setPage] = useState(1);
  const [searchFilters, setSearchFilters] = useState<PropertySearchParams>({});
  const [hoveredPropertyId, setHoveredPropertyId] = useState<string | null>(null);
  const [panelWidth, setPanelWidth] = useState<number>(0);
  const panelRef = useRef<HTMLDivElement>(null);
  const itemsPerPage = 12;
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Add effect to measure panel width
  useEffect(() => {
    const updatePanelWidth = () => {
      if (panelRef.current) {
        const width = panelRef.current.getBoundingClientRect().width;
        if (width > 0) {
          setPanelWidth(width);
        }
      }
    };

    // Initial measurement with a small delay
    const timer = setTimeout(updatePanelWidth, 100);

    // Set up resize observer
    const resizeObserver = new ResizeObserver(updatePanelWidth);
    if (panelRef.current) {
      resizeObserver.observe(panelRef.current);
    }

    return () => {
      clearTimeout(timer);
      resizeObserver.disconnect();
    };
  }, []);

  // Convert URL search params to server-side API filters
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    const filters: PropertySearchParams = {
      pageNumber: page,
      pageSize: itemsPerPage,
      isDescending: sortOrder === 'desc',
    };

    // Extract search term
    if (params.has('searchTerm')) {
      filters.searchTerm = params.get('searchTerm') || undefined;
    }

    // Map property type
    if (params.has('propertyType')) {
      const propertyType = params.get('propertyType');
      if (propertyType === 'apartment') filters.type = PropertyType.APARTMENT;
      else if (propertyType === 'land_plot') filters.type = PropertyType.LAND_PLOT;
      else if (propertyType === 'villa') filters.type = PropertyType.VILLA;
      else if (propertyType === 'shophouse') filters.type = PropertyType.SHOP_HOUSE;
    }

    // Map transaction type
    if (params.has('transactionType')) {
      const transactionType = params.get('transactionType');
      if (
        transactionType === TransactionType.FOR_SALE ||
        transactionType === TransactionType.FOR_RENT
      ) {
        filters.transactionType = transactionType;
      }
    }

    // Location filters
    if (params.has('city')) {
      // Note: City filtering will be handled differently in future
      // For now, we'll pass it as searchTerm if no other search term exists
      if (!filters.searchTerm && params.get('city')) {
        filters.searchTerm = params.get('city')!;
      }
    }

    // Price range filters
    if (params.has('minPrice')) {
      filters.minPrice = Number(params.get('minPrice')) || undefined;
    }
    if (params.has('maxPrice')) {
      filters.maxPrice = Number(params.get('maxPrice')) || undefined;
    }

    // Area filters
    if (params.has('minArea')) {
      filters.minLandArea = Number(params.get('minArea')) || undefined;
    }
    if (params.has('maxArea')) {
      filters.maxLandArea = Number(params.get('maxArea')) || undefined;
    }

    // Bedroom filters
    if (params.has('bedCount')) {
      const bedCount = params.get('bedCount');
      const isExactMatch = params.get('exactBedMatch') === 'true';

      if (bedCount && bedCount !== 'any') {
        if (bedCount === 'studio') {
          filters.bedrooms = 0;
        } else {
          const beds = parseInt(bedCount);
          if (isExactMatch) {
            filters.bedrooms = beds;
          } else {
            filters.minBedrooms = beds;
          }
        }
      }
    }

    // Bathroom filters
    if (params.has('bathCount')) {
      const bathCount = params.get('bathCount');
      if (bathCount && bathCount !== 'any') {
        const baths = parseInt(bathCount.replace('+', ''));
        filters.minBathrooms = baths;
      }
    }

    setSearchFilters(filters);
  }, [searchParams, page, sortOrder]);

  // Fetch properties with server-side filtering
  const { properties, isLoading, isError, error, isFetching, count, totalPages } =
    useProperties(searchFilters);

  const handleMapBoundsChange = (_properties: Property[], _bounds: google.maps.LatLngBounds) => {
    // Server-side filtering handles geographic bounds
    // This function is kept for future map-based filtering implementation
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const getTitleText = () => {
    const city = searchParams.get('city') || 'Hồ Chí Minh';
    const transactionType = searchParams.get('transactionType');

    if (transactionType === TransactionType.FOR_SALE) {
      return `Nhà bán ở ${city}`;
    } else if (transactionType === TransactionType.FOR_RENT) {
      return `Nhà cho thuê tại ${city}`;
    }
    return `Bất động sản ở ${city}`;
  };

  const handleSort = () => {
    setSortOrder(prev => (prev === 'asc' ? 'desc' : 'asc'));
  };

  // Calculate pagination - Server already handles this
  const paginatedProperties = properties;

  return (
    <div className="font-mann h-[calc(100vh-10rem)] md:h-[calc(100vh-4rem)] flex flex-col">
      <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
        {/* Mobile Header */}
        <div className="bg-background border-b flex-shrink-0 md:hidden">
          <div className="flex items-center px-2">
            {isLoading ? (
              <MobileHeaderButtonSkeleton />
            ) : (
              <Drawer>
                <DrawerTrigger asChild>
                  <Button
                    variant="outline"
                    className="rounded-full border-primary border-dashed shadow-none"
                  >
                    {getTitleText()}
                    <Badge variant="destructive" className="rounded-full px-1 py-px">
                      {count}+
                    </Badge>
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="h-[90vh] font-mann">
                  <DrawerHeader>
                    <DrawerTitle>{getTitleText()}</DrawerTitle>
                    <DrawerDescription>{count} bất động sản</DrawerDescription>
                  </DrawerHeader>

                  <div className="flex-1 overflow-y-auto p-4">
                    <PropertyListings
                      properties={paginatedProperties}
                      isLoading={isLoading}
                      isError={isError}
                      error={error}
                      isFetching={isFetching}
                      count={count}
                      totalPages={totalPages}
                      page={page}
                      onPageChange={handlePageChange}
                      onPropertyHover={setHoveredPropertyId}
                      containerWidth={panelWidth}
                    />
                  </div>
                </DrawerContent>
              </Drawer>
            )}

            <div className="flex-1">{isLoading ? <SearchFilterSkeleton /> : <SearchFilter />}</div>
          </div>
        </div>

        {/* Desktop Header */}
        <div className="bg-background border-b flex-shrink-0 hidden md:block">
          {isLoading ? <SearchFilterSkeleton /> : <SearchFilter />}
        </div>

        {/* Mobile Layout */}
        <div className="flex-1 min-h-0 md:hidden flex flex-col">
          <div className="h-full flex-shrink-0 relative touch-none">
            <div className="absolute inset-0">
              <PropertiesMap
                properties={properties}
                onBoundsChange={handleMapBoundsChange}
                hoveredPropertyId={hoveredPropertyId}
              />
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="flex-1 min-h-0 hidden md:flex">
          <ResizablePanelGroup direction="horizontal" className="h-full">
            <ResizablePanel defaultSize={50} minSize={30}>
              <div className="h-full">
                <PropertiesMap
                  properties={properties}
                  onBoundsChange={handleMapBoundsChange}
                  hoveredPropertyId={hoveredPropertyId}
                />
              </div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={50} minSize={30}>
              <div className="h-full flex flex-col" ref={panelRef}>
                <div className="flex-shrink-0 p-4 border-b">
                  <div className="flex justify-between items-center">
                    <h1 className="text-lg font-semibold">
                      {getTitleText()}{' '}
                      <span className="text-sm text-muted-foreground">- {count} bất động sản</span>
                    </h1>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSort}
                      className="flex items-center gap-2"
                    >
                      <ArrowUpDown className="h-4 w-4" />
                      {sortOrder === 'asc' ? 'Giá tăng dần' : 'Giá giảm dần'}
                    </Button>
                  </div>
                </div>
                <div className="flex-1 overflow-y-auto p-4">
                  <PropertyListings
                    properties={paginatedProperties}
                    isLoading={isLoading}
                    isError={isError}
                    error={error}
                    isFetching={isFetching}
                    count={count}
                    totalPages={totalPages}
                    page={page}
                    onPageChange={handlePageChange}
                    onPropertyHover={setHoveredPropertyId}
                    containerWidth={panelWidth}
                  />
                </div>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </APIProvider>
    </div>
  );
}
