import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { TransactionType } from "@/lib/api/services/fetchProperty";

/**
 * ----------------------------------------------------------------------------------
 * TODO – MIGRATION PLAN
 * ----------------------------------------------------------------------------------
 * 1. Copy ALL shared type definitions (Province, District, Ward, FilterValues, ...)
 *    from `SearchFilter.tsx` to this file so they can be reused across hooks & UI.
 * 2. Move every piece of state logic currently living in `SearchFilter.tsx` into
 *    this hook. The component should ONLY render UI and call this hook.
 * 3. Replace the old event‐handlers with versions inside this hook that:
 *      a. Update local state.
 *      b. Update URL search params via `useRouter`.
 *      c. Trigger search immediately for "Apply" (no extra Search button).
 * 4. Extract API calls for provinces / districts / wards into a separate helper
 *    (or hook) and call it here. Keep this hook focused on orchestrating state.
 * 5. Expose ONLY the data & callbacks the UI actually needs. Avoid leaking
 *    internal helpers to the component to keep the public API clean.
 * 6. After port is complete, delete redundant logic from `SearchFilter.tsx` and
 *    ensure it imports and consumes `useSearchFilter` instead.
 * 7. Write unit tests for the hook (future task).
 * ----------------------------------------------------------------------------------
 */

// -----------------------------------------------------------------------------
// Shared type definitions (copied from SearchFilter.tsx). We will gradually move
// any additional interfaces as we migrate more logic.
// -----------------------------------------------------------------------------
export interface Province {
  code: number
  name: string
  districts?: District[]
}

export interface District {
  code: number
  name: string
  wards?: Ward[]
}

export interface Ward {
  code: number
  name: string
}

export interface FilterValues {
  city?: string
  district?: string
  ward?: string
  propertyType?: string
  transactionType?: TransactionType
  priceRange?: string
  propertySize?: string
  buildYear?: string
  bedCount?: string
  bathCount?: string
  exactBedMatch?: string
  bedCountDisplay?: string
  bathCountDisplay?: string
  [key: string]: string | undefined
}

interface UseSearchFilterResult {
  // state
  searchQuery: string
  activeFilters: FilterValues
  isSearching: boolean

  // handlers
  setSearchQuery: (q: string) => void
  handleFilterChange: (key: keyof FilterValues, value: string) => void
  handleSearch: () => void
}

export function useSearchFilter(): UseSearchFilterResult {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<FilterValues>({})
  const [isSearching, setIsSearching] = useState(false)

  // ---------------------------------------------------------------------------
  // Sync state with URL when component mounts OR URL query changes externally.
  // For now we only sync `searchTerm`. Other params will be added during port.
  // ---------------------------------------------------------------------------
  useEffect(() => {
    const params = new URLSearchParams(searchParams)
    if (params.has("searchTerm")) {
      setSearchQuery(params.get("searchTerm") ?? "")
    }
  }, [searchParams])

  // ---------------------------------------------------------------------------
  // Helper: build URLSearchParams from current state.
  // TODO: extend to include ALL filter values.
  // ---------------------------------------------------------------------------
  const buildQueryParams = useCallback(
    (overrides: Partial<FilterValues> = {}): URLSearchParams => {
      const params = new URLSearchParams()

      const merged: FilterValues = { ...activeFilters, ...overrides }

      if (searchQuery) params.set("searchTerm", searchQuery)

      Object.entries(merged).forEach(([key, val]) => {
        if (val) params.set(key, val)
      })

      return params
    },
    [activeFilters, searchQuery]
  )

  // ---------------------------------------------------------------------------
  // Core handler: update a single filter key then immediately trigger search.
  // This satisfies the new requirement: pressing "Apply" == instant search.
  // ---------------------------------------------------------------------------
  const handleFilterChange = useCallback(
    (key: keyof FilterValues, value: string) => {
      setActiveFilters(prev => {
        const next = { ...prev, [key]: value }
        // Trigger search with new filters right away.
        const params = buildQueryParams({ [key]: value })
        router.push(`/properties?${params.toString()}`)
        return next
      })
    },
    [buildQueryParams, router]
  )

  const handleSearch = useCallback(() => {
    const params = buildQueryParams()
    setIsSearching(true)
    router.push(`/properties?${params.toString()}`)
    // there is no async op yet, but we keep this flag for future fetch calls
    setIsSearching(false)
  }, [buildQueryParams, router])

  return {
    searchQuery,
    activeFilters,
    isSearching,

    setSearchQuery,
    handleFilterChange,
    handleSearch,
  }
}
