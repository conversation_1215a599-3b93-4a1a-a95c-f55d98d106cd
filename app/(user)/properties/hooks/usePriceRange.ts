import { useState, useMemo, useCallback, useEffect } from 'react';
import { TransactionType } from '@/lib/api/services/fetchProperty';
import { formatCurrency } from '@/utils/numbers/formatCurrency';

interface PriceRange {
  min: number;
  max: number;
  label: string;
}

interface PriceRangeState {
  sliderValue: [number, number];
  minPrice: string;
  maxPrice: string;
  isPriceRangeOpen: boolean;
  setSliderValue: (value: [number, number]) => void;
  setMinPrice: (price: string) => void;
  setMaxPrice: (price: string) => void;
  setIsPriceRangeOpen: (open: boolean) => void;
  resetPriceRange: () => void;
  formatPriceLabel: (value: number, isMax?: boolean) => string;
  formatPriceDisplay: (priceRange: string) => string;
  getCurrentPriceRanges: () => {
    distribution: any[];
    quickRanges: PriceRange[];
    maxValue: number;
    step: number;
  };
  getPriceRangeString: () => string;
  handleApplyPriceRange: (onFilterChange: (key: string, value: string) => void) => void;
  handleMinPriceChange: (value: string) => void;
  handleMaxPriceChange: (value: string) => void;
  handleMinPriceBlur: () => void;
  handleMaxPriceBlur: () => void;
  handleMinPriceFocus: () => void;
  handleMaxPriceFocus: () => void;
  handleQuickRangeSelect: (range: [number, number]) => void;
}

/**
 * Hook quản lý logic price range slider và formatting
 * Hỗ trợ cả bán và cho thuê với format khác nhau, URL sync và input handling
 */
export function usePriceRange(transactionType?: TransactionType): PriceRangeState {
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [isPriceRangeOpen, setIsPriceRangeOpen] = useState(false);

  // Price distributions and ranges - synced with SearchFilter.tsx
  const priceDistribution = useMemo(
    () => ({
      sale: [
        { price: '0-1', count: 120 },
        { price: '1-2', count: 250 },
        { price: '2-3', count: 180 },
        { price: '3-4', count: 90 },
        { price: '4-5', count: 60 },
        { price: '5-6', count: 40 },
        { price: '6-7', count: 30 },
        { price: '7-8', count: 20 },
        { price: '8-9', count: 15 },
        { price: '9-10', count: 10 },
      ],
      rent: [
        { price: '0-5', count: 150 },
        { price: '5-10', count: 280 },
        { price: '10-15', count: 320 },
        { price: '15-20', count: 250 },
        { price: '20-25', count: 180 },
        { price: '25-30', count: 120 },
        { price: '30-40', count: 80 },
        { price: '40-50', count: 50 },
        { price: '50-70', count: 30 },
        { price: '70+', count: 20 },
      ],
    }),
    []
  );

  const quickPriceRanges = useMemo(
    () => ({
      sale: [
        { min: 0, max: 1000000000, label: 'Dưới 1 tỷ' },
        { min: 1000000000, max: 2000000000, label: '1-2 tỷ' },
        { min: 2000000000, max: 3000000000, label: '2-3 tỷ' },
        { min: 3000000000, max: 5000000000, label: '3-5 tỷ' },
        { min: 5000000000, max: 7000000000, label: '5-7 tỷ' },
        { min: 7000000000, max: 10000000000, label: 'Trên 7 tỷ' },
      ],
      rent: [
        { min: 0, max: 5000000, label: 'Dưới 5tr' },
        { min: 5000000, max: 10000000, label: '5-10tr' },
        { min: 10000000, max: 15000000, label: '10-15tr' },
        { min: 15000000, max: 20000000, label: '15-20tr' },
        { min: 20000000, max: 30000000, label: '20-30tr' },
        { min: 30000000, max: 100000000, label: 'Trên 30tr' },
      ],
    }),
    []
  );

  // Get current price ranges based on transaction type
  const getCurrentPriceRanges = useCallback(() => {
    const isRent = transactionType === TransactionType.FOR_RENT;
    return {
      distribution: priceDistribution[isRent ? 'rent' : 'sale'],
      quickRanges: quickPriceRanges[isRent ? 'rent' : 'sale'],
      maxValue: isRent ? 100000000 : 10000000000,
      step: isRent ? 100000 : 1000000, // 100K for rent, 1M for sale
    };
  }, [transactionType, priceDistribution, quickPriceRanges]);

  // Default slider value based on transaction type
  const defaultSliderValue = useMemo((): [number, number] => {
    const { maxValue } = getCurrentPriceRanges();
    return [0, maxValue];
  }, [getCurrentPriceRanges]);

  const [sliderValue, setSliderValue] = useState<[number, number]>(defaultSliderValue);

  // Format price label for slider
  const formatPriceLabel = useCallback(
    (value: number, isMax: boolean = false) => {
      const isRent = transactionType === TransactionType.FOR_RENT;
      if (isRent) {
        if (value >= 1000000) {
          return `${(value / 1000000).toFixed(1)} triệu${isMax ? '+' : ''}/tháng`;
        }
        return `${(value / 1000).toFixed(0)} nghìn${isMax ? '+' : ''}/tháng`;
      } else {
        if (value >= 1000000000) {
          return `${(value / 1000000000).toFixed(1)} tỷ${isMax ? '+' : ''}`;
        }
        return `${(value / 1000000).toFixed(0)} triệu${isMax ? '+' : ''}`;
      }
    },
    [transactionType]
  );

  // Format price display for button
  const formatPriceDisplay = useCallback(
    (priceRange: string) => {
      if (!priceRange) return 'Mức giá';

      const isRent = transactionType === TransactionType.FOR_RENT;
      const [min, max] = priceRange.split(' đến ').map(price => {
        const value = price.replace(/[^0-9]/g, '');
        const numValue = parseInt(value);

        if (isRent) {
          if (numValue >= 1000000) {
            return `${(numValue / 1000000).toFixed(1)} triệu`;
          }
          return `${(numValue / 1000).toFixed(0)} nghìn`;
        } else {
          if (numValue >= 1000000000) {
            return `${(numValue / 1000000000).toFixed(1)} tỷ`;
          }
          return `${(numValue / 1000000).toFixed(0)} triệu`;
        }
      });

      return `${min} - ${max}${isRent ? '/tháng' : ''}`;
    },
    [transactionType]
  );

  // Get price range string for filters
  const getPriceRangeString = useCallback(() => {
    const { maxValue } = getCurrentPriceRanges();
    if (sliderValue[0] === 0 && sliderValue[1] === maxValue) {
      return '';
    }
    return `${formatCurrency(sliderValue[0], 'VND')} đến ${formatCurrency(sliderValue[1], 'VND')}`;
  }, [sliderValue, getCurrentPriceRanges]);

  // Reset price range
  const resetPriceRange = useCallback(() => {
    const { maxValue } = getCurrentPriceRanges();
    setSliderValue([0, maxValue]);
    setMinPrice('');
    setMaxPrice('');
  }, [getCurrentPriceRanges]);

  // Handle price input changes with validation
  const handleMinPriceChange = useCallback((value: string) => {
    const rawValue = value.replace(/[^0-9]/g, '');
    setMinPrice(rawValue);
    setSliderValue(prev => [Number(rawValue) || 0, prev[1]]);
  }, []);

  const handleMaxPriceChange = useCallback(
    (value: string) => {
      const rawValue = value.replace(/[^0-9]/g, '');
      setMaxPrice(rawValue);
      setSliderValue(prev => [prev[0], Number(rawValue) || getCurrentPriceRanges().maxValue]);
    },
    [getCurrentPriceRanges]
  );

  // Handle input blur events (format display)
  const handleMinPriceBlur = useCallback(() => {
    if (minPrice) {
      setMinPrice(formatCurrency(Number(minPrice), 'VND'));
    }
  }, [minPrice]);

  const handleMaxPriceBlur = useCallback(() => {
    if (maxPrice) {
      setMaxPrice(formatCurrency(Number(maxPrice), 'VND'));
    }
  }, [maxPrice]);

  // Handle input focus events (show raw numbers)
  const handleMinPriceFocus = useCallback(() => {
    setMinPrice(sliderValue[0]?.toString() || '');
  }, [sliderValue]);

  const handleMaxPriceFocus = useCallback(() => {
    setMaxPrice(sliderValue[1]?.toString() || '');
  }, [sliderValue]);

  // Handle quick range selection
  const handleQuickRangeSelect = useCallback((range: [number, number]) => {
    setSliderValue(range);
    setMinPrice(range[0].toString());
    setMaxPrice(range[1].toString());
  }, []);

  // Apply price range to filters
  const handleApplyPriceRange = useCallback(
    (onFilterChange: (key: string, value: string) => void) => {
      const priceRangeString = getPriceRangeString();
      onFilterChange('priceRange', priceRangeString);
      setIsPriceRangeOpen(false);
    },
    [getPriceRangeString]
  );

  // Sync from URL parameters
  const syncFromURLParams = useCallback(
    (minPriceParam: string, maxPriceParam: string) => {
      const { maxValue } = getCurrentPriceRanges();
      const minPrice = Number(minPriceParam) || 0;
      const maxPrice = Number(maxPriceParam) || maxValue;

      setSliderValue([minPrice, maxPrice]);
      setMinPrice(minPrice.toString());
      setMaxPrice(maxPrice.toString());
    },
    [getCurrentPriceRanges]
  );

  // Update slider when transaction type changes
  useEffect(() => {
    const { maxValue } = getCurrentPriceRanges();
    if (sliderValue[1] > maxValue) {
      setSliderValue([sliderValue[0], maxValue]);
      setMaxPrice(maxValue.toString());
    }
  }, [transactionType, getCurrentPriceRanges, sliderValue]);

  return {
    sliderValue,
    minPrice,
    maxPrice,
    isPriceRangeOpen,
    setSliderValue,
    setMinPrice,
    setMaxPrice,
    setIsPriceRangeOpen,
    resetPriceRange,
    formatPriceLabel,
    formatPriceDisplay,
    getCurrentPriceRanges,
    getPriceRangeString,
    handleApplyPriceRange,
    handleMinPriceChange,
    handleMaxPriceChange,
    handleMinPriceBlur,
    handleMaxPriceBlur,
    handleMinPriceFocus,
    handleMaxPriceFocus,
    handleQuickRangeSelect,
    // Additional utility method
    syncFromURLParams,
  } as PriceRangeState & {
    syncFromURLParams: (minPriceParam: string, maxPriceParam: string) => void;
  };
}
