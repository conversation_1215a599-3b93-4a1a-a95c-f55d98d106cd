import type React from 'react';
import '@/app/globals.css';
import type { Metadata } from 'next';
//import { Inter } from 'next/font/google';

//const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'RevoLand - G<PERSON><PERSON>i Pháp Toàn Diện Cho <PERSON> Bất Động Sản',
  description:
    'RevoLand - Giải pháp bất động sản toàn diện, đáng tin cậy. Chuyên cung cấp dịch vụ mua bán, cho thuê nhà đất, bi<PERSON>t thự, căn hộ và đất nền với đội ngũ chuyên gia uy tín, tận tâm.',
};

export default function PropertiesLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="font-mann">
      {children}
      {/* <ChatWidget /> */}
    </div>
  );
}
