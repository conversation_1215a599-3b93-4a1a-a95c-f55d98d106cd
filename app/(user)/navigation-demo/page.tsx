'use client';

import React from 'react';
import {
  NavigationLink,
  EnhancedNavigationLink,
  NavigationButton,
} from '@/components/ui/navigation-link';
import { useNavigationProgressContext } from '@/components/providers/navigationProgressProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

export default function NavigationDemoPage() {
  const { isNavigating, progress, startNavigation, completeNavigation } =
    useNavigationProgressContext();

  const handleManualStart = () => {
    startNavigation();
  };

  const handleManualComplete = () => {
    completeNavigation();
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Navigation Progress Demo</h1>
          <p className="text-muted-foreground text-lg">
            Experience the YouTube-style navigation progress bar in action
          </p>

          {/* Current Status */}
          <div className="flex items-center justify-center gap-4">
            <Badge variant={isNavigating ? 'default' : 'secondary'}>
              Status: {isNavigating ? 'Navigating' : 'Idle'}
            </Badge>
            {isNavigating && <Badge variant="outline">Progress: {Math.round(progress)}%</Badge>}
          </div>
        </div>

        <Separator />

        {/* Manual Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Manual Controls</CardTitle>
            <CardDescription>Test the navigation progress manually</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4 flex-wrap">
              <Button onClick={handleManualStart} disabled={isNavigating}>
                Start Navigation
              </Button>
              <Button onClick={handleManualComplete} disabled={!isNavigating}>
                Complete Navigation
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Examples */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Basic Navigation Links */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Navigation Links</CardTitle>
              <CardDescription>Standard links with automatic progress detection</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <NavigationLink href="/properties" className="text-blue-600 hover:underline">
                  → Go to Properties
                </NavigationLink>
                <NavigationLink href="/login" className="text-blue-600 hover:underline">
                  → Go to Login
                </NavigationLink>
                <NavigationLink href="/register" className="text-blue-600 hover:underline">
                  → Go to Register
                </NavigationLink>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Navigation Links */}
          <Card>
            <CardHeader>
              <CardTitle>Enhanced Navigation Links</CardTitle>
              <CardDescription>Links with custom progress behavior</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <EnhancedNavigationLink
                  href="/properties"
                  className="text-green-600 hover:underline"
                  progressDelay={500}
                  onNavigationStart={() => console.log('Navigation starting...')}
                >
                  → Delayed Navigation (500ms)
                </EnhancedNavigationLink>
                <EnhancedNavigationLink
                  href="/login"
                  className="text-green-600 hover:underline"
                  progressType="on-hover"
                >
                  → Hover to Start Progress
                </EnhancedNavigationLink>
                <EnhancedNavigationLink
                  href="/register"
                  className="text-green-600 hover:underline"
                  showProgress={false}
                >
                  → No Progress Bar
                </EnhancedNavigationLink>
              </div>
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <Card>
            <CardHeader>
              <CardTitle>Navigation Buttons</CardTitle>
              <CardDescription>Buttons that trigger navigation progress</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <NavigationButton
                  className="w-full text-left p-2 rounded border hover:bg-gray-50"
                  onClick={() => (window.location.href = '/properties')}
                >
                  → Button Navigation to Properties
                </NavigationButton>
                <NavigationButton
                  className="w-full text-left p-2 rounded border hover:bg-gray-50"
                  progressDelay={1000}
                  onClick={() => setTimeout(() => (window.location.href = '/login'), 1000)}
                >
                  → Delayed Button Navigation (1s)
                </NavigationButton>
              </div>
            </CardContent>
          </Card>

          {/* External Links */}
          <Card>
            <CardHeader>
              <CardTitle>External Links</CardTitle>
              <CardDescription>
                These won&apos;t trigger progress (external links are ignored)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <a
                  href="https://github.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-red-600 hover:underline block"
                >
                  → External Link (GitHub)
                </a>
                <a href="mailto:<EMAIL>" className="text-red-600 hover:underline block">
                  → Mailto Link
                </a>
                <a href="tel:+1234567890" className="text-red-600 hover:underline block">
                  → Tel Link
                </a>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Features List */}
        <Card>
          <CardHeader>
            <CardTitle>Features</CardTitle>
            <CardDescription>What makes this navigation progress system special</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-semibold">Automatic Detection</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Detects all internal link clicks</li>
                  <li>• Ignores external links and anchors</li>
                  <li>• Handles programmatic navigation</li>
                  <li>• Works with Next.js Link components</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Smart Progress</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Realistic progress simulation</li>
                  <li>• Smooth animations and transitions</li>
                  <li>• Shimmer effects for visual appeal</li>
                  <li>• Automatic completion on route change</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Customizable</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Manual control options</li>
                  <li>• Configurable delays</li>
                  <li>• Different progress types</li>
                  <li>• Easy to disable per link</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Performance</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Lightweight implementation</li>
                  <li>• No impact on page load</li>
                  <li>• Efficient event handling</li>
                  <li>• Memory leak prevention</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Code Example */}
        <Card>
          <CardHeader>
            <CardTitle>Usage Example</CardTitle>
            <CardDescription>
              How to use the navigation progress system in your components
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
              {`// Basic usage - automatically works with all internal links
<NavigationLink href="/properties">
  Go to Properties
</NavigationLink>

// Enhanced usage with custom behavior
<EnhancedNavigationLink 
  href="/login"
  progressDelay={500}
  progressType="on-hover"
  onNavigationStart={() => console.log('Starting...')}
>
  Login with Hover Progress
</EnhancedNavigationLink>

// Manual control
const { startNavigation, completeNavigation } = useNavigationProgressContext();

<Button onClick={startNavigation}>
  Start Progress Manually
</Button>`}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
