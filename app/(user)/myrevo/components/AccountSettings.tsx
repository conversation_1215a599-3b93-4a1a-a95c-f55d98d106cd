'use client';

import { useEffect, useState, useRef } from 'react';
import { useUserProfile, useUpdateProfile } from '@/hooks/useUsers';
import {
  Mail,
  Phone,
  Calendar,
  Clock,
  Shield,
  Check,
  User as LucideUser,
  FileEdit,
  Settings,
  LogOut,
  Loader2,
  Pencil,
  Upload,
  FileImage,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { formatDate } from '@/utils/dates/formatDate';
import { ThemeScript } from '@/components/themeScript';
import { useAuthStore } from '@/lib/store/authStore';
import { EmptyState } from '@/app/(admin)/admin/profile/components/EmptyState';
import { ErrorState } from '@/app/(admin)/admin/profile/components/ErrorState';
import { LoadingState } from '@/app/(admin)/admin/profile/components/LoadingState';
import { EditProfileForm } from '@/app/(admin)/admin/profile/components/EditProfileForm';
import { toast } from 'sonner';

export default function AccountSettings() {
  const [isMounted, setIsMounted] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [backgroundImage, setBackgroundImage] = useState('');
  const backgroundInputRef = useRef<HTMLInputElement>(null);
  const [isChangingBackground, setIsChangingBackground] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    if (typeof window !== 'undefined') {
      const savedBackground = localStorage.getItem('userBackgroundImage');
      if (savedBackground) {
        setBackgroundImage(savedBackground);
      }
    }
  }, []);

  const { data: profileResponse, isLoading, error, refetch } = useUserProfile();
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const avatarInputRef = useRef<HTMLInputElement>(null);
  const { mutate: updateProfile, isPending } = useUpdateProfile();
  const logout = useAuthStore(state => state.logout);

  const handleAvatarClick = () => {
    avatarInputRef.current?.click();
  };

  const handleAvatarChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploadingAvatar(true);

    const formData = new FormData();
    formData.append('avatarFile', file);
    if (profileResponse?.profile) {
      formData.append('userName', profileResponse.profile.userName);
      formData.append('fullName', profileResponse.profile.fullName);
      formData.append('email', profileResponse.profile.email);
      if (profileResponse.profile.phoneNumber) {
        formData.append('phoneNumber', profileResponse.profile.phoneNumber);
      }
      if (profileResponse.profile.about) {
        formData.append('about', profileResponse.profile.about);
      }
      if (profileResponse.profile.birthDate) {
        formData.append('birthDate', profileResponse.profile.birthDate);
      }
      if (profileResponse.profile.status) {
        formData.append('status', profileResponse.profile.status);
      }
      formData.append('role', profileResponse.profile.role);
    }
    updateProfile(formData);
    setIsUploadingAvatar(false);
  };

  const handleBackgroundClick = () => {
    backgroundInputRef.current?.click();
  };

  const handleBackgroundChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsChangingBackground(true);
    try {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setBackgroundImage(base64String);
        localStorage.setItem('userBackgroundImage', base64String);
        setIsChangingBackground(false);
        toast.success('Background updated');
      };
      reader.onerror = () => {
        setIsChangingBackground(false);
        toast.error('Failed to update background image. Please try again.');
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Failed to update background:', error);
      setIsChangingBackground(false);
      toast.error('Failed to update background image. Please try again.');
    }
  };

  const handleLogout = () => {
    logout();
    window.location.href = '/login';
  };

  if (!isMounted) {
    return null;
  }

  if (isLoading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState onRetry={refetch} error={error} />;
  }

  if (!profileResponse || !profileResponse.profile) {
    return (
      <EmptyState
        apiResponse={{
          status: false,
          code: 404,
          message: 'No profile data',
        }}
      />
    );
  }

  return (
    <>
      <ThemeScript />
      <div className="w-full min-h-screen">
        {/* Profile Header with Background Image */}
        <div
          className="relative bg-cover bg-center h-[350px] md:h-[350px] rounded-xl"
          style={{
            backgroundImage: `url('${
              backgroundImage ||
              'https://images.unsplash.com/photo-1554147090-e1221a04a025?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80'
            }')`,
            backgroundPosition: 'center',
            backgroundSize: 'cover',
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background/95"></div>

          <input
            type="file"
            ref={backgroundInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleBackgroundChange}
            disabled={isChangingBackground}
          />
          <Button
            variant="secondary"
            size="sm"
            className="absolute top-4 right-4 shadow-lg z-10"
            onClick={handleBackgroundClick}
            disabled={isChangingBackground}
          >
            {isChangingBackground ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <FileImage className="h-4 w-4 mr-2" />
            )}
            Thay đổi ảnh bìa
          </Button>

          {/* Profile Info Overlay */}
          <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
            <div className="max-w-screen-2xl mx-auto flex flex-col md:flex-row md:items-end gap-4">
              <div className="relative mb-[-1rem] md:mb-[-2rem]">
                <input
                  type="file"
                  ref={avatarInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  disabled={isUploadingAvatar}
                />
                <div className="relative">
                  <Avatar className="h-24 w-24 sm:h-32 sm:w-32 border-4 border-background shadow-xl">
                    <AvatarImage src={profileResponse.profile.avatar} className="object-cover" />
                    <AvatarFallback className="text-4xl bg-primary/10">
                      {profileResponse.profile.fullName ? (
                        profileResponse.profile.fullName.substring(0, 2).toUpperCase()
                      ) : (
                        <LucideUser className="h-12 w-12" />
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute -bottom-2 -right-2 rounded-full shadow-lg z-10 h-10 w-10"
                    onClick={handleAvatarClick}
                    disabled={isUploadingAvatar}
                  >
                    {isUploadingAvatar ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="flex-1 text-left">
                <h1 className="text-2xl md:text-3xl font-bold text-muted-foreground">
                  {profileResponse.profile.fullName}
                </h1>
                <p className="text-muted-foreground text-sm md:text-base">
                  @{profileResponse.profile.userName}
                </p>

                <div className="flex flex-wrap gap-2 mt-3">
                  <Badge variant="secondary" className="px-2 py-1">
                    <Check className="h-3 w-3 mr-1" /> {profileResponse.profile.status}
                  </Badge>
                  <Badge variant="outline" className="px-2 py-1 bg-background/30 backdrop-blur-sm">
                    <Shield className="h-3 w-3 mr-1" />
                    {profileResponse.profile.role}
                  </Badge>
                  <Badge variant="outline" className="px-2 py-1 bg-background/30 backdrop-blur-sm">
                    <Clock className="h-3 w-3 mr-1" />
                    Tham gia{' '}
                    {profileResponse.profile.joinedAt
                      ? new Date(profileResponse.profile.joinedAt).getFullYear()
                      : 'N/A'}
                  </Badge>
                </div>
              </div>

              <div className="mt-2 md:mt-0">
                <Sheet open={isEditingProfile} onOpenChange={setIsEditingProfile}>
                  <SheetTrigger asChild>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="h-9"
                      onClick={() => setIsEditingProfile(true)}
                    >
                      <FileEdit className="h-4 w-4 mr-2" />
                      Chỉnh sửa thông tin
                    </Button>
                  </SheetTrigger>
                  <SheetContent className="w-full sm:max-w-xl">
                    <SheetHeader>
                      <SheetTitle>Chỉnh sửa thông tin</SheetTitle>
                      <SheetDescription>Chỉnh sửa thông tin</SheetDescription>
                    </SheetHeader>
                    <div className="mt-4 px-1">
                      {profileResponse && (
                        <EditProfileForm
                          isSubmitting={isPending}
                          profile={{
                            fullName: profileResponse.profile.fullName || '',
                            email: profileResponse.profile.email || '',
                            phoneNumber: profileResponse.profile.phoneNumber || '',
                            about: profileResponse.profile.about || '',
                            userName: profileResponse.profile.userName || '',
                            birthdate: profileResponse.profile.birthDate,
                            status: profileResponse.profile.status,
                            avatarFile: profileResponse.profile.avatar as unknown as File,
                          }}
                          onSuccess={() => {
                            setIsEditingProfile(false);
                            refetch();
                          }}
                          onCancel={() => setIsEditingProfile(false)}
                        />
                      )}
                    </div>
                  </SheetContent>
                </Sheet>
              </div>
            </div>
          </div>
        </div>

        {/* Profile Content */}
        <div className="bg-background w-full">
          <div className="max-w-screen-2xl mx-auto px-4 py-6">
            <Tabs
              defaultValue="overview"
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <div className="border-b mb-6">
                <TabsList className="bg-transparent justify-start">
                  <TabsTrigger
                    value="overview"
                    className={`px-6 py-3 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none transition-all ${
                      activeTab === 'overview'
                        ? 'text-primary font-medium'
                        : 'text-muted-foreground'
                    }`}
                  >
                    Thông tin cá nhân
                  </TabsTrigger>
                  <TabsTrigger
                    value="settings"
                    className={`px-6 py-3 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none transition-all ${
                      activeTab === 'settings'
                        ? 'text-primary font-medium'
                        : 'text-muted-foreground'
                    }`}
                  >
                    Cài đặt
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="overview" className="mt-0">
                {/* Profile Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <Card className="border border-border/30 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="bg-primary/5 rounded-t-lg pb-3">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Mail className="h-4 w-4 mr-2 text-primary" />
                        Thông tin liên hệ
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{profileResponse.profile.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {profileResponse.profile.phoneNumber || 'Not provided'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border border-border/30 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="bg-primary/5 rounded-t-lg pb-3">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-primary" />
                        Thông tin cá nhân
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {formatDate(profileResponse.profile.birthDate || '')}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {formatDate(profileResponse.profile.joinedAt || '')}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border border-border/30 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="bg-primary/5 rounded-t-lg pb-3">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Shield className="h-4 w-4 mr-2 text-primary" />
                        Trạng thái tài khoản
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Check className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {profileResponse.profile.status || 'Active'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{profileResponse.profile.role || 'User'}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* About Card */}
                <Card className="border border-border/30 shadow-sm">
                  <CardHeader className="pb-3 flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Giới thiệu về bản thân</CardTitle>
                    </div>
                    {!profileResponse.profile.about && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsEditingProfile(true)}
                        className="h-8"
                      >
                        <Pencil className="h-3.5 w-3.5 mr-1.5" />
                        Add Bio
                      </Button>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-lg bg-muted/50 p-4">
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap leading-relaxed">
                        {profileResponse.profile.about || 'Giới thiệu về bản thân'}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="settings" className="mt-0 p-0">
                <div className="flex flex-col gap-10 w-full">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-6 w-full">
                    {/* Account Settings Section */}
                    <div className="w-full">
                      <h2 className="text-2xl font-semibold mb-2">Cài đặt tài khoản</h2>
                      <p className="text-muted-foreground mb-6">Cài đặt tài khoản</p>
                      <div className="space-y-6">
                        {/* <div className="flex items-center justify-between border-b pb-4">
                          <div>
                            <h3 className="font-medium">Giao diện</h3>
                            <p className="text-sm text-muted-foreground mt-1">
                              Giao diện
                            </p>
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant={currentTheme === 'light' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => handleThemeChange('light')}
                              className="h-9 w-9 p-0"
                            >
                              <Sun className="h-4 w-4" />
                              <span className="sr-only">Light</span>
                            </Button>
                            <Button
                              variant={currentTheme === 'dark' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => handleThemeChange('dark')}
                              className="h-9 w-9 p-0"
                            >
                              <Moon className="h-4 w-4" />
                              <span className="sr-only">Dark</span>
                            </Button>
                            <Button
                              variant={currentTheme === 'system' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => handleThemeChange('system')}
                              className="h-9 w-9 p-0"
                            >
                              <Monitor className="h-4 w-4" />
                              <span className="sr-only">System</span>
                            </Button>
                          </div>
                        </div> */}
                        <div className="flex items-center justify-between border-b pb-4">
                          <div>
                            <h3 className="font-medium">Bảo mật tài khoản</h3>
                            <p className="text-sm text-muted-foreground mt-1">Bảo mật tài khoản</p>
                          </div>
                          <Button variant="outline" size="sm" className="h-9">
                            <Settings className="h-4 w-4 mr-2" />
                            Thay đổi mật khẩu
                          </Button>
                        </div>
                        <div className="flex justify-between items-center">
                          <p className="text-sm text-muted-foreground">
                            Tham gia: {formatDate(profileResponse.profile.joinedAt || '')}
                          </p>
                          <Button variant="destructive" size="sm" onClick={handleLogout}>
                            <LogOut className="h-4 w-4 mr-2" />
                            Đăng xuất
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Email Preferences */}
                    <div className="w-full">
                      <h2 className="text-2xl font-semibold mb-2">Tuỳ chọn email</h2>
                      <p className="text-muted-foreground mb-6">Tuỳ chọn email</p>
                      <div className="space-y-6">
                        <div className="flex items-center justify-between border-b pb-4">
                          <div>
                            <h3 className="font-medium">Email nhận thông báo</h3>
                            <p className="text-sm text-muted-foreground mt-1">
                              Email nhận thông báo
                            </p>
                          </div>
                          <Badge variant="outline" className="px-3 py-1">
                            Tắt
                          </Badge>
                        </div>
                        {/* <div className="flex items-center justify-between border-b pb-4">
                          <div>
                            <h3 className="font-medium">Email nhận thông báo</h3>
                            <p className="text-sm text-muted-foreground mt-1">
                              Tuỳ chọn email
                            </p>
                          </div>
                          <Badge variant="secondary" className="px-3 py-1">
                            Bật
                          </Badge>
                        </div> */}
                      </div>
                    </div>
                  </div>

                  {/* Account Data */}
                  <div className="w-full">
                    <h2 className="text-2xl font-semibold mb-2">Dữ liệu tài khoản</h2>
                    <p className="text-muted-foreground mb-6">Dữ liệu tài khoản</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-6">
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <h3 className="font-medium">Xuất dữ liệu tài khoản</h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            Xuất dữ liệu tài khoản
                          </p>
                        </div>
                        <Button size="sm" variant="outline">
                          Xuất dữ liệu
                        </Button>
                      </div>
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <h3 className="font-medium text-destructive">Xóa tài khoản</h3>
                          <p className="text-sm text-muted-foreground mt-1">Xóa tài khoản</p>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-destructive border-destructive/20 hover:bg-destructive/10"
                        >
                          Xóa tài khoản
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </>
  );
}
