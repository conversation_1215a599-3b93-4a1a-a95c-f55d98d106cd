'use client';

import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { appointmentService, AppointmentResponse } from '@/lib/api/services/fetchAppointment';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, Clock, User, Phone, Mail, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function ManageTours() {
  const [appointments, setAppointments] = useState<NonNullable<AppointmentResponse['data']>[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadAppointments = async () => {
      try {
        const response = await appointmentService.getUserAppointments();
        if (response.status) {
          setAppointments(
            response.data.filter(
              (appointment): appointment is NonNullable<AppointmentResponse['data']> =>
                appointment !== null
            )
          );
        } else {
          throw new Error(response.message);
        }
      } catch (error) {
        console.error('Không thể tải danh sách lịch hẹn');
      } finally {
        setIsLoading(false);
      }
    };

    loadAppointments();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="space-y-3">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-4 w-[150px]" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (appointments.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground mb-4">Bạn chưa có lịch hẹn nào</p>
        <Button asChild>
          <Link href="/properties">Tìm bất động sản</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {appointments.map(appointment => (
        <Card key={appointment.id}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-medium">
              Lịch hẹn #{appointment.id.slice(-6)}
            </CardTitle>
            <Badge className={getStatusColor(appointment.status)}>{appointment.status}</Badge>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Ngày hẹn:</span>
                <span>{format(new Date(appointment.date), 'PPP', { locale: vi })}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Giờ hẹn:</span>
                <span>{format(new Date(appointment.date), 'p', { locale: vi })}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Môi giới:</span>
                <span>{appointment.saler.fullName}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Số điện thoại:</span>
                <span>{appointment.saler.phoneNumber}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Email:</span>
                <span>{appointment.saler.email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Mã bất động sản:</span>
                <Link
                  href={`/properties/${appointment.propertyId}`}
                  className="text-primary hover:underline"
                >
                  {appointment.propertyId}
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
