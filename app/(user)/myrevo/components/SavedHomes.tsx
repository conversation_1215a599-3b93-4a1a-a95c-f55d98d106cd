import { useQuery } from '@tanstack/react-query';
import { PropertyCard } from '@/components/PropertyCard';
import { Skeleton } from '@/components/ui/skeleton';
import favoritePropertyService from '@/lib/api/services/fetchFavoriteProperty';
import { Property } from '@/lib/api/services/fetchProperty';

export default function SavedHomes() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['favorite-properties'],
    queryFn: favoritePropertyService.getFavoriteProperties,
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="space-y-3 md:space-y-4">
            <Skeleton className="aspect-square w-full rounded-xl" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Lỗi khi tải dữ liệu</p>
      </div>
    );
  }

  if (!data?.data[0] || data.data[0].properties.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Bạn chưa lưu bất động sản nào</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 gap-4 2xl:grid-cols-3 md:gap-6">
      {data.data[0].properties.map((property: Property) => (
        <PropertyCard key={property.id} property={property} />
      ))}
    </div>
  );
}
