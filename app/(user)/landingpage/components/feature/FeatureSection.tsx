import React from 'react';
import FeatureCard from './FeatureCard';

const features = [
  {
    image: '/saling.webp',
    title: '<PERSON><PERSON> nh<PERSON>',
    alt: '<PERSON><PERSON> nh<PERSON>',
    description:
      'Tìm ngôi nhà phù hợp với trải nghiệm hình ảnh sống động và danh sách bất động sản đầy đủ nhất, bao gồm những gì bạn không thể tìm thấy ở nơi khác.',
    buttonText: 'Xem nhà',
  },
  {
    image: '/selling.webp',
    title: '<PERSON><PERSON> nhà',
    description:
      'Dù bạn chọn con đường nào để bán nhà, chúng tôi có thể giúp bạn điều hướng một giao dịch thành công.',
    buttonText: 'Xem các lựa chọn',
  },
  {
    image: '/renting.webp',
    title: '<PERSON><PERSON><PERSON> nh<PERSON>',
    description:
      '<PERSON><PERSON>g tôi đang tạo ra trải nghiệm trực tuyến mượt mà – từ việc tìm kiếm trên mạng lưới cho thuê lớn nhất, đến đăng ký và thanh toán tiền thuê.',
    buttonText: 'Tìm nhà thuê',
  },
];

export default function FeatureSection() {
  return (
    <section className="w-full max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 py-12 px-2">
      {features.map(f => (
        <FeatureCard key={f.title} {...f} />
      ))}
    </section>
  );
}
