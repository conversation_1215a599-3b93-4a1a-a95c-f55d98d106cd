'use client';

import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import React from 'react';
import BuyAbilitySection from './buyability/BuyAbilitySection';
import FeatureSection from './feature/FeatureSection';
import Footer from '@/components/Footer';

export default function LandingPage() {
  return (
    <div className="relative min-h-screen flex flex-col bg-white font-mann">
      <div className="relative w-full h-[30vh] sm:h-[40vh] md:h-[50vh] lg:h-[55vh]">
        <Image
          src="/bg_hero.jpg"
          alt="Landing Background"
          fill
          className="object-cover object-center brightness-90"
          priority
        />
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 flex flex-col items-start justify-end h-full px-4 sm:px-6 lg:px-8 pb-10 md:pb-16">
          <div className="w-full max-w-2xl mx-auto lg:mx-0 lg:ml-8 xl:ml-16 2xl:ml-40 flex flex-col gap-4 sm:gap-6">
            <h1 className="text-white text-3xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-extrabold drop-shadow-lg leading-tight text-center lg:text-left">
              Mua bán. Cho thuê.
              <br />
              Tư vấn. Dịch vụ.
            </h1>
            <form
              className="w-full max-w-md lg:max-w-lg xl:max-w-xl flex flex-row items-center bg-white/90 rounded-2xl shadow-lg overflow-hidden border border-gray-200 mt-2 sm:mt-4"
              onSubmit={e => {
                e.preventDefault();
                window.location.href = '/properties';
              }}
            >
              <Input
                type="text"
                placeholder="Nhập địa chỉ của bạn"
                className="flex-1 h-12 sm:h-14 md:h-16 text-sm sm:text-base md:text-lg bg-transparent border-0 focus:ring-0 px-3 sm:px-4 md:px-6 placeholder:text-gray-500 text-left rounded-none"
                aria-label="Search homes"
              />
              <Button
                type="submit"
                className="w-auto h-12 sm:h-14 md:h-16 rounded-none sm:rounded-r-2xl px-4 sm:px-6 md:px-8 text-sm sm:text-base md:text-lg font-semibold bg-red-600 text-white hover:bg-red-700"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={2}
                  stroke="currentColor"
                  className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z"
                  />
                </svg>
              </Button>
            </form>
          </div>
        </div>
      </div>
      <section className="w-full flex flex-col lg:flex-row justify-center gap-2 sm:gap-4 lg:gap-6 mt-8 sm:mt-12 md:mt-16 lg:mt-20 py-6 sm:py-8 md:py-10 lg:py-12 bg-white px-2 sm:px-4 lg:px-6 xl:px-8">
        <div className="w-full max-w-[500px] flex flex-col items-center lg:items-start text-center lg:text-left px-2 sm:px-4 lg:px-0 mb-6 lg:mb-0 mx-auto">
          <h2 className="font-bold text-xl sm:text-2xl md:text-3xl lg:text-4xl mb-2 sm:mb-3 text-gray-900">
            Gợi ý nhà phù hợp
          </h2>
          <p className="text-muted-foreground text-sm sm:text-base md:text-lg mb-4 sm:mb-6 leading-relaxed">
            Đăng nhập để nhận trải nghiệm cá nhân hóa và các đề xuất tốt nhất cho bạn.
          </p>
          <button
            className="px-4 sm:px-6 py-2 sm:py-3 rounded-xl border border-red-500 text-red-500 font-semibold hover:bg-red-50 transition-colors text-sm sm:text-base"
            onClick={() => {
              window.location.href = '/login';
            }}
          >
            Đăng nhập
          </button>
        </div>
        <div className="w-full max-w-[500px] flex items-center justify-center mx-auto">
          <div className="relative w-[280px] sm:w-[320px] md:w-[360px] lg:w-[400px] xl:w-[440px]">
            <div className="relative rounded-2xl shadow-2xl bg-white overflow-visible pt-2 pb-4 px-3 sm:px-4">
              <div className="absolute -top-6 sm:-top-8 left-2 sm:left-4 flex items-center gap-2 bg-white rounded-full shadow-lg px-3 sm:px-4 py-2 z-10">
                <span className="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-green-500"></span>
                <div className="text-left">
                  <div className="font-semibold text-xs sm:text-sm text-gray-900 leading-tight">
                    Nhà đề xuất
                  </div>
                  <div className="text-xs text-gray-500">Dựa trên ngân sách của bạn</div>
                </div>
              </div>
              <div className="absolute -bottom-8 sm:-bottom-10 -right-10 sm:-right-12 lg:-right-16 flex items-center gap-2 bg-white rounded-full shadow-lg px-3 sm:px-4 py-2 z-10">
                <span className="inline-block w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-orange-500"></span>
                <div className="text-left">
                  <div className="font-semibold text-xs sm:text-sm text-gray-900 leading-tight">
                    Nhà đề xuất
                  </div>
                  <div className="text-xs text-gray-500">Dựa trên vị trí ưu tiên</div>
                </div>
              </div>
              <Image
                src="/proper.jpg"
                width={600}
                height={128}
                alt="Recommended Home"
                className="w-full h-44 sm:h-56 md:h-64 object-cover rounded-xl mb-3 sm:mb-4"
              />
              <div className="font-bold text-lg sm:text-xl md:text-2xl text-gray-900 mb-2">
                Vinhome Grandpark
              </div>
              <div className="flex items-center text-gray-700 text-xs sm:text-sm gap-1 sm:gap-2 mb-2">
                <span>Tìm nhà hợp lí chỉ từ 3,xx tỷ - 5,xx tỷ</span>
              </div>
            </div>
          </div>
        </div>
      </section>
      <BuyAbilitySection />
      <FeatureSection />
      <Footer />
    </div>
  );
}
