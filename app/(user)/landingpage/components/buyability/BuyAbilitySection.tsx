import React, { useEffect, useState } from 'react';
import { PropertyCard } from '@/components/PropertyCard';
import { propertyService, PropertyResponse, Property } from '@/lib/api/services/fetchProperty';

export default function BuyAbilitySection() {
  const [properties, setProperties] = useState<Property[]>([]);

  useEffect(() => {
    propertyService
      .getProperties({ pageSize: 8, pageNumber: 1 })
      .then((res: PropertyResponse) => {
        setProperties(res.data.properties || []);
      })
      .catch(() => setProperties([]));
  }, []);

  const suggestedProperties = properties.slice(0, 4);
  const nearbyProperties = properties.slice(4, 8);

  return (
    <section className="w-full flex flex-col items-center justify-center pt-12 pb-16 bg-white mt-0 md:mt-2">
      {/* Suggested Properties Section */}
      <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36 mb-12">
        <div className="text-left mb-8">
          <h2 className="text-xl md:text-3xl font-bold text-gray-900 mb-4">
            Bất động sản Hồ Chí Minh
          </h2>
          <p className="text-sm md:text-lg text-gray-600 max-w-4xl">
            Khám phá những bất động sản được chọn lọc phù hợp với sở thích và lối sống của bạn.
          </p>
        </div>
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
          {suggestedProperties.map(property => (
            <PropertyCard
              key={property.id}
              property={property as Property}
              priority={true}
              size="md"
            />
          ))}
        </div>
      </div>

      {/* Properties Near You Section */}
      <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36">
        <div className="text-left mb-8">
          <h2 className="text-xl md:text-3xl font-bold text-gray-900 mb-4">Bất động sản gần bạn</h2>
          <p className="text-sm md:text-lg text-gray-600 max-w-4xl">
            Khám phá những bất động sản trong khu vực của bạn mang đến sự tiện lợi và dễ tiếp cận.
          </p>
        </div>
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
          {nearbyProperties.map(property => (
            <PropertyCard
              key={property.id}
              property={property as Property}
              priority={false}
              size="md"
            />
          ))}
        </div>
      </div>
    </section>
  );
}
