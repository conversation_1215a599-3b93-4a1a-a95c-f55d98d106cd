import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  appointmentService,
  AppointmentRequest,
  AppointmentResponse,
  Appointment,
} from '@/lib/api/services/fetchAppointment';
import { toast } from 'sonner';

// Query Keys
export const appointmentKeys = {
  all: ['appointments'] as const,
  user: () => [...appointmentKeys.all, 'user'] as const,
  byProperty: (propertyId: string) => [...appointmentKeys.all, 'property', propertyId] as const,
};

/**
 * Hook to create a new appointment
 * Implements optimistic updates and proper error handling
 */
export function useCreateAppointment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (request: AppointmentRequest): Promise<AppointmentResponse> => {
      const response = await appointmentService.createAppointment(request);

      if (!response.status) {
        throw new Error(response.message || 'Không thể tạo lịch hẹn');
      }

      return response;
    },
    onSuccess: data => {
      // Show success toast
      toast.success('Lịch hẹn đã được tạo thành công');

      // Invalidate and refetch user appointments
      queryClient.invalidateQueries({
        queryKey: appointmentKeys.user(),
      });

      // Optionally add the new appointment to cache
      if (data.data) {
        queryClient.setQueryData<Appointment[]>(appointmentKeys.user(), (oldData = []) => [
          data.data!,
          ...oldData,
        ]);
      }
    },
    onError: error => {
      console.error('Create appointment error:', error);

      toast.error(error.message || 'Không thể tạo lịch hẹn. Vui lòng thử lại sau.');
    },
  });
}

/**
 * Hook to get all appointments for the current user
 * Implements proper caching and error handling
 */
export function useGetUserAppointments() {
  return useQuery({
    queryKey: appointmentKeys.user(),
    queryFn: async (): Promise<Appointment[]> => {
      const response = await appointmentService.getUserAppointments();

      if (!response.status) {
        throw new Error(response.message || 'Không thể tải danh sách lịch hẹn');
      }

      return response.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook to get appointments for a specific property
 * Useful for property-specific appointment views
 */
export function useGetPropertyAppointments(propertyId: string, enabled = true) {
  return useQuery({
    queryKey: appointmentKeys.byProperty(propertyId),
    queryFn: async (): Promise<Appointment[]> => {
      const response = await appointmentService.getUserAppointments();

      if (!response.status) {
        throw new Error(response.message || 'Không thể tải danh sách lịch hẹn');
      }

      // Filter appointments by property ID
      return (response.data || []).filter(appointment => appointment.propertyId === propertyId);
    },
    enabled: enabled && !!propertyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
}

/**
 * Hook to prefetch user appointments
 * Useful for improving perceived performance
 */
export function usePrefetchUserAppointments() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.prefetchQuery({
      queryKey: appointmentKeys.user(),
      queryFn: async (): Promise<Appointment[]> => {
        const response = await appointmentService.getUserAppointments();
        return response.status ? response.data || [] : [];
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
}
