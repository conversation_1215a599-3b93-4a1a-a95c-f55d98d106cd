import { chatService, Conversation, SendMessageRequest } from '@/lib/api/services/fetchChat';
import { signalRService } from '@/lib/realtime/signalR';
import { useQuery, useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * React Query hook to handle all chat-related operations
 * @returns {Object} Chat operations and states
 */
export function useChat() {
  // Query for current conversation
  const conversationQuery = useQuery<Conversation | null, Error>({
    queryKey: ['chat', 'currentUserConversation'],
    queryFn: () => chatService.getCurrentUserConversation(),
  });

  // Mutation for sending messages
  const sendMessageMutation = useMutation({
    mutationFn: async (request: SendMessageRequest) => {
      return await chatService.sendMessage(request);
    },
    onError: () => {
      toast.error('Không thể gửi tin nhắn. Vui lòng thử lại.');
    },
  });

  return {
    // Query states
    conversation: conversationQuery.data,
    isLoading: conversationQuery.isLoading,
    isError: conversationQuery.isError,
    refetchConversation: conversationQuery.refetch,

    // Mutations
    sendMessage: sendMessageMutation.mutateAsync,
    isSending: sendMessageMutation.isPending,

    // SignalR handlers
    setMessageHandler: signalRService.setMessageHandler,
    setMessageHistoryHandler: signalRService.setMessageHistoryHandler,
    setNewConversationHandler: signalRService.setNewConversationHandler,
    setNotificationHandler: signalRService.setNotificationHandler,
    setTypingHandler: signalRService.setTypingHandler,
    setReadHandler: signalRService.setReadHandler,
    setOnlineStatusHandler: signalRService.setOnlineStatusHandler,
  };
}
