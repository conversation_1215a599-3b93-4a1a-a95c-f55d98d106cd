import {
  ActionLeadRequest,
  ActionLeadResponse,
  AssignLeadRequest,
  AssignLeadResponse,
  LeadSearchParams,
  UpdateScoreRequest,
  UpdateScoreResponse,
  LeadResponse,
  leadService,
  LeadScore,
  unassignLeadRequest,
  unassignLeadResponse,
} from '@/lib/api/services/fetchLead';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useLeads(filters?: LeadSearchParams) {
  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['leads', 'list', filters ? JSON.stringify(filters) : 'all'],
    queryFn: () => leadService.getLeads(filters),
    select: (data: LeadResponse) => ({
      leads: data.data || [],
      status: data.status,
      message: data.message,
    }),
  });
  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    leads: data?.leads || [],
    status: data?.status,
    message: data?.message,
  };
}

export function useSellerLeads() {
  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['leads', 'seller'],
    queryFn: () => leadService.getSellerLeads(),
    select: (data: LeadResponse) => ({
      leads: data.data || [],
      status: data.status,
      message: data.message,
    }),
  });
  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    leads: data?.leads || [],
    status: data?.status,
    message: data?.message,
  };
}

export function useCreateLead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (newLead: ActionLeadRequest) => leadService.createLead(newLead),
    onSuccess: (data: ActionLeadResponse) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['leads', 'list'] });
        toast.success(data.message);
      } else {
        toast.error(data.message);
      }
    },
    onError: error => {
      toast.error(error.message);
      console.error('Failed to create lead:', error);
    },
  });
}

export function useAssignLead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (lead: AssignLeadRequest) => leadService.assignLead(lead),
    onSuccess: (data: AssignLeadResponse) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['leads', 'list'] });
        toast.success(data.message);
      } else {
        toast.error(data.message);
      }
    },
    onError: error => {
      toast.error(error.message);
      console.error('Failed to assign lead:', error);
    },
  });
}

export function useUnassignLead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (lead: unassignLeadRequest) => leadService.unassignLead(lead),
    onSuccess: (data: unassignLeadResponse) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['leads', 'list'] });
        toast.success(data.message);
      } else {
        toast.error(data.message);
      }
    },
    onError: error => {
      toast.error(error.message);
      console.error('Failed to unassign lead:', error);
    },
  });
}

export function useUpdateLeadScore() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (lead: UpdateScoreRequest) => leadService.updateLeadScore(lead),
    onMutate: async variables => {
      // Show optimistic feedback
      toast.info(`Đang thay đổi trạng thái khách hàng`);
      return variables;
    },
    onSuccess: (data: UpdateScoreResponse, variables) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['leads', 'list'] });

        const getScoreLabel = (score: LeadScore): string => {
          switch (score) {
            case LeadScore.Hot:
              return 'Tiềm năng cao';
            case LeadScore.Warm:
              return 'Tiềm năng trung bình';
            case LeadScore.Cold:
              return 'Tiềm năng thấp';
            default:
              return score;
          }
        };

        toast.success(`Đã chuyển khách hàng sang ${getScoreLabel(variables.score)}`);
      } else {
        toast.error(data.message || 'Không thể cập nhật trạng thái khách hàng');
      }
    },
    onError: error => {
      toast.error(error.message);
      console.error('Failed to update lead score:', error);
    },
  });
}
