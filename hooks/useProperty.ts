import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import propertyService, {
  PropertyResponse,
  PropertySearchParams,
  // Property,
  ActionPropertyResponse,
  PropertyDetailRequest,
  PropertyDetailResponse,
} from '@/lib/api/services/fetchProperty';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

// Define the PropertyUpdatePayload type
interface PropertyUpdatePayload {
  id: string;
  property: Partial<PropertyDetailRequest>;
}

/**
 * Hook to fetch properties with filters
 */
export function useProperties(filters?: PropertySearchParams) {
  // const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['properties', 'list', filters ? JSON.stringify(filters) : 'all'],
    queryFn: () => propertyService.getProperties(filters),
    // enabled: isAuthenticated,
    select: (data: PropertyResponse) => ({
      properties: data.data?.properties || [],
      count: data.data?.count || 0,
      limit: data.data?.limit || 10,
      page: data.data?.page || 1,
      totalPages: data.data?.totalPages || 1,
      status: data.status,
      message: data.message,
    }),
  });

  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    properties: data?.properties || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 10,
    totalPages: data?.totalPages || 1,
    status: data?.status,
    message: data?.message,
  };
}

export function usePropertiesBySaler(filters?: PropertySearchParams) {
  // const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['properties', 'list', 'saler', filters ? JSON.stringify(filters) : 'all'],
    queryFn: () => propertyService.getPropertiesBySaler(filters),
    // enabled: isAuthenticated,
    select: (data: PropertyResponse) => ({
      properties: data.data?.properties || [],
      count: data.data?.count || 0,
      limit: data.data?.limit || 10,
      page: data.data?.page || 1,
      totalPages: data.data?.totalPages || 1,
      status: data.status,
      message: data.message,
    }),
  });

  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    properties: data?.properties || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 10,
    totalPages: data?.totalPages || 1,
    status: data?.status,
    message: data?.message,
  };
}

/**
 * Hook to fetch a single property by ID
 */
export function useProperty(id?: string) {
  return useQuery({
    queryKey: ['properties', 'detail', id],
    queryFn: () => propertyService.getProperty(id!),
    enabled: !!id,
    select: (data: PropertyDetailResponse) => ({
      property: data.data,
      status: data.status,
      message: data.message,
      data: data.data,
    }),
  });
}

// export const usePropertyBySlug = (slug: string) => {
//   return useQuery({
//     queryKey: ['properties', 'detail', slug],
//     queryFn: () => propertyService.getProperty(slug),
//     enabled: !!slug, // Only run if slug exists
//     select: (data: PropertyResponse) => ({
//       property: data.data,
//       status: data.status,
//       message: data.message,
//       data: data.data,
//     }),
//   });
// };
/**
 * Hook to create a new property
 */
export function useCreateProperty() {
  const queryClient = useQueryClient();
  const router = useRouter();
  return useMutation({
    mutationFn: (newProperty: Partial<PropertyDetailRequest>) =>
      propertyService.createProperty(newProperty),
    onSuccess: (data: ActionPropertyResponse) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['properties', 'list'] });
        router.push(`/saler/property`);
        toast.success(data.message);
      }
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

/**
 * Hook to update an existing property
 */
export function useUpdateProperty() {
  const queryClient = useQueryClient();
  const router = useRouter();
  return useMutation({
    mutationFn: (payload: PropertyUpdatePayload) =>
      propertyService.updateProperty(payload.id, payload.property),
    onSuccess: (data: ActionPropertyResponse) => {
      if (data.status) {
        // Invalidate and refetch queries regardless of data.data
        queryClient.invalidateQueries({ queryKey: ['properties', 'detail'] });
        queryClient.invalidateQueries({ queryKey: ['properties', 'list'] });
        toast.success(data.message);
        router.push('/saler/property');
      } else {
        toast.error(data.message);
      }
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

/**
 * Hook to delete a property
 */
export function useDeleteProperty() {
  const queryClient = useQueryClient();
  const router = useRouter();
  return useMutation({
    mutationFn: (id: string) => propertyService.deleteProperty(id),
    onSuccess: (data: ActionPropertyResponse, id: string) => {
      if (data.status) {
        queryClient.removeQueries({ queryKey: ['properties', 'detail', 'deleted', id] });
        queryClient.invalidateQueries({ queryKey: ['properties', 'list'] });
        toast.success(data.message);
        router.push('/saler/property');
      } else {
        toast.error(data.message);
      }
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useVerifyProperty() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (propertyId: string) => propertyService.verifyProperty(propertyId),
    onSuccess: (data: ActionPropertyResponse) => {
      queryClient.invalidateQueries({ queryKey: ['properties', 'list'] });
      if (data.status) {
        toast.success(data.message);
      } else {
        toast.error(data.message);
      }
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUnverifyProperty() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (propertyId: string) => propertyService.unverifyProperty(propertyId),
    onSuccess: (data: ActionPropertyResponse) => {
      queryClient.invalidateQueries({ queryKey: ['properties', 'list'] });
      if (data.status) {
        toast.success(data.message);
      } else {
        toast.error(data.message);
      }
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
