import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

const authRoutes = ['/login', '/register', '/forgot-password'];
const otpRoute = '/otp';
const renewPasswordRoute = '/renew-password';

const getUserRole = (token: string | undefined): string | null => {
  if (!token) return null;
  try {
    const decoded = jwt.decode(token) as { role?: string } | null;
    return decoded?.role ?? null;
  } catch (error) {
    console.error('[AUTH] Failed to decode token:', error);
    return null;
  }
};

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const token = request.cookies.get('auth-token')?.value;
  const userRole = getUserRole(token);

  // Handle authentication routes (login, register)
  const isAuthRoute = authRoutes.some(
    route => pathname === route || pathname.startsWith(`${route}/`)
  );

  const isOtpRoute = pathname === otpRoute || pathname.startsWith(`${otpRoute}/`);

  if (isOtpRoute) {
    const keyRegister = request.nextUrl.searchParams.get('keyRegister');
    if (!keyRegister) {
      return NextResponse.redirect(new URL('/register', request.url));
    }
    // If user already has a valid token, redirect to appropriate dashboard
    if (token) {
      if (userRole === 'Admin') {
        return NextResponse.redirect(new URL('/admin/dashboard', request.url));
      } else if (userRole === 'Saler') {
        return NextResponse.redirect(new URL('/saler/dashboard', request.url));
      } else {
        return NextResponse.redirect(new URL('/properties', request.url));
      }
    }
  }

  const isRenewPasswordRoute =
    pathname === renewPasswordRoute || pathname.startsWith(`${renewPasswordRoute}/`);
  if (isRenewPasswordRoute) {
    const tokenParam = request.nextUrl.searchParams.get('token');
    if (!tokenParam) {
      return NextResponse.redirect(new URL('/forgot-password', request.url));
    }
    // If user already has a valid token, redirect to appropriate dashboard
    if (token) {
      if (userRole === 'Admin') {
        return NextResponse.redirect(new URL('/admin/dashboard', request.url));
      } else if (userRole === 'Saler') {
        return NextResponse.redirect(new URL('/saler/dashboard', request.url));
      } else {
        return NextResponse.redirect(new URL('/properties', request.url));
      }
    }
  }

  // If user is on an auth route but already has a valid token, redirect to appropriate dashboard
  if (isAuthRoute && token) {
    if (userRole === 'Admin') {
      return NextResponse.redirect(new URL('/admin/dashboard', request.url));
    } else if (userRole === 'Saler') {
      return NextResponse.redirect(new URL('/saler/dashboard', request.url));
    } else {
      // Default for users with other roles or no specific role
      return NextResponse.redirect(new URL('/properties', request.url));
    }
  }

  // Handle admin routes - only allow access to admins
  const isAdminRoute = pathname.startsWith('/admin/');
  if (isAdminRoute) {
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
    if (userRole !== 'Admin') {
      return NextResponse.redirect(new URL('/properties', request.url));
    }
  }

  // Handle saler routes - allow access to any authenticated user
  const isSalerRoute = pathname.startsWith('/saler/');
  if (isSalerRoute) {
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
    // Allow any authenticated user to access saler routes
  }

  // Redirect only admins to admin dashboard when they try to access other pages
  if (token && !isAuthRoute) {
    if (userRole === 'Admin' && !pathname.startsWith('/admin/') && pathname !== '/') {
      return NextResponse.redirect(new URL('/admin/dashboard', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|images|fonts|assets).*)'],
};
