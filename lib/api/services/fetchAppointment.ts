import apiService from '../core';

export interface AppointmentRequest {
  propertyId: string;
  date: string;
}

export interface Saler {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
}

export interface Customer {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
}

export interface Appointment {
  id: string;
  leadId: string;
  saler: Saler;
  customer: Customer;
  messages?: string[];
  propertyId: string;
  status: string;
  isRescheduled: boolean;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export interface AppointmentResponse {
  code: number;
  status: boolean;
  message: string;
  data?: Appointment;
}

export interface UserAppointmentsResponse {
  code: number;
  status: boolean;
  message: string;
  data: Appointment[];
}

export const appointmentService = {
  // Create a new appointment
  createAppointment: async (request: AppointmentRequest): Promise<AppointmentResponse> => {
    const response = await apiService.post<AppointmentResponse, AppointmentRequest>(
      '/api/appointments',
      request
    );
    return response.data;
  },

  // Get all appointments for the current user
  getUserAppointments: async (): Promise<UserAppointmentsResponse> => {
    const response = await apiService.get<UserAppointmentsResponse>('/api/appointments/user');
    return response.data;
  },
};

export default appointmentService;
