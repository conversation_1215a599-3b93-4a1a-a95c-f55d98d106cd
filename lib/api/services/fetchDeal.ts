import apiService, { RequestParams } from '../core';
import { User } from './fetchUser';

export enum DealStatus {
  New = 'New',
  Contacted = 'Contacted',
  Negotiation = 'Negotiation',
  Closing = 'Closing',
  Won = 'Won',
  Lost = 'Lost',
}

export enum DealPriority {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
}

export interface DealSearchParams {
  searchTerm?: string;
  pageNumber?: number;
  pageSize?: number;
  isAscending?: boolean;
  status?: string;
  priority?: string;
  sortBy?: string;
}

export interface CreateDealRequest {
  leadId: string;
  salesRepId?: string;
  title: string;
  description: string;
  priority: DealPriority;
}

export interface ActionDealResponse {
  code: string;
  status: boolean;
  message: string;
  data?: string;
}

export interface DealResponse {
  code: string;
  status: boolean;
  message: string;
  data: {
    count: number;
    pageNumber: number;
    pageSize: number;
    totalCount: number;
    items: Deal[];
  };
}

export interface DealLogResponse {
  status: boolean;
  message: string;
  data: {
    items: DealLog[];
    totalCount: number;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
  };
}

export interface DealDetailsResponse {
  code: string;
  status: boolean;
  message: string;
  data: Deal;
}

export interface Deal {
  id: string;
  title?: string;
  description?: string;
  status: string;
  position: number;
  createdAt: string;
  updatedAt: string;
  priority?: DealPriority;
  salesRepId: string;
  saler?: User;
  leadId: string;
  customer?: Customer;
  notes?: DealNote[];
  note?: DealNote; //for add note when updating deal
  // propertyType?: string;
  // expectedValue?: number;
}

export interface DealUpdateRequest {
  leadId?: string;
  salesRepId?: string;
  status?: string;
  position?: number;
  priority?: string;
  title?: string;
  description?: string;
  note?: {
    title: string;
    content: string;
  };
}

export interface DealNoteUpdateRequest {
  title: string;
  content: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  source:
    | 'Form'
    | 'Message'
    | 'Appointment'
    | 'ExternalSeller'
    | 'Admin'
    | 'Zalo'
    | 'Facebook'
    | 'Web';
  score: 'Hot' | 'Warm' | 'Cold';
}

export interface DealNote {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

// Deal Log Models
export interface DealLog {
  id: string;
  dealId: string;
  fromStatus: DealStatus;
  toStatus: DealStatus;
  isDeleteAction: boolean;
  timestamp: string; // ISO date string
  triggeredBy: User;
}
export interface DealLogSearchParams {
  pageNumber?: number;
  pageSize?: number;
  fromDate?: string;
  toDate?: string;
  status?: DealStatus;
  triggeredBy?: string;
}

const convertDealFilters = (filters?: DealSearchParams): RequestParams => {
  if (!filters) return {};

  const params: RequestParams = {};

  if (filters.searchTerm) params.searchTerm = filters.searchTerm;
  if (filters.pageNumber !== undefined) params.pageNumber = filters.pageNumber;
  if (filters.pageSize !== undefined) params.pageSize = filters.pageSize;
  if (filters.isAscending !== undefined) params.isAscending = filters.isAscending;
  if (filters.status) params.status = filters.status;
  if (filters.priority) params.priority = filters.priority;
  if (filters.sortBy) params.sortBy = filters.sortBy;
  return params;
};

const convertDealsLogFilters = (filters?: DealLogSearchParams): RequestParams => {
  if (!filters) return {};

  const params: RequestParams = {};

  if (filters.pageNumber !== undefined) params.pageNumber = filters.pageNumber;
  if (filters.pageSize !== undefined) params.pageSize = filters.pageSize;
  if (filters.fromDate) params.fromDate = filters.fromDate;
  if (filters.toDate) params.toDate = filters.toDate;
  if (filters.status) params.status = filters.status;
  if (filters.triggeredBy) params.triggeredBy = filters.triggeredBy;

  return params;
};

export const dealService = {
  getDeals: async (filters?: DealSearchParams) => {
    const params = convertDealFilters(filters);
    const { data } = await apiService.get<DealResponse>('/api/deals', params);
    return data;
  },
  // Get a single deal by ID
  getDeal: async (id: string): Promise<DealDetailsResponse> => {
    const response = await apiService.get<DealDetailsResponse>(`/api/deals/${id}`);
    return response.data;
  },

  // Create a new deal
  createDeal: async (deal: Partial<CreateDealRequest>): Promise<ActionDealResponse> => {
    const response = await apiService.post<ActionDealResponse>('/api/deals', deal);
    return response.data;
  },

  // Update an existing deal
  updateDeal: async (id: string, deal: Partial<DealUpdateRequest>): Promise<ActionDealResponse> => {
    const response = await apiService.put<ActionDealResponse>(`/api/deals/${id}`, deal);
    return response.data;
  },

  // Delete a property
  deleteDeal: async (id: string): Promise<ActionDealResponse> => {
    const response = await apiService.delete<ActionDealResponse>(`/api/deals/${id}`);
    return response.data;
  },

  // Update a deal note (using PATCH for partial updates)
  updateDealNote: async (
    id: string,
    dealNoteId: string,
    dealNote: Partial<DealNoteUpdateRequest>
  ): Promise<ActionDealResponse> => {
    const response = await apiService.patch<ActionDealResponse>(
      `/api/deals/${id}/notes/${dealNoteId}`,
      dealNote
    );
    return response.data;
  },

  getDealLogs: async (dealId: string, filters?: DealLogSearchParams): Promise<DealLogResponse> => {
    const params = convertDealsLogFilters(filters);
    const url = `/api/deals/${dealId}/log`;
    const { data } = await apiService.get<DealLogResponse>(url, params);
    return data;
  },
  // other methods...
};
