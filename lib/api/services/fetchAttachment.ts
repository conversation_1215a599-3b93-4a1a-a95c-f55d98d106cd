import apiService from '../core';

export interface RequestAttachment {
  files: File[];
}

export interface RequestVideoAttachment {
  file: File;
}

export interface Attachment {
  id: string;
  fileType: string;
  fileSize: number;
  fileUrl: string;
  fileName: string;
}

export interface ResponseAttachment {
  code: number;
  status: boolean;
  message: string;
  data: Attachment[];
}

export interface ResponseSingleAttachment {
  code: number;
  status: boolean;
  message: string;
  data: Attachment;
}

const attachmentService = {
  uploadAttachment: async (request: RequestAttachment): Promise<ResponseAttachment> => {
    const formData = new FormData();
    request.files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiService.post<ResponseAttachment, FormData>(
      '/api/attachments',
      formData
    );
    return response.data;
  },

  uploadPropertyFloorPlan: async (request: RequestAttachment): Promise<ResponseAttachment> => {
    const formData = new FormData();
    request.files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiService.post<ResponseAttachment, FormData>(
      '/api/attachments/property/floor-plan',
      formData
    );
    return response.data;
  },

  uploadPropertyVideo: async (
    request: RequestVideoAttachment
  ): Promise<ResponseSingleAttachment> => {
    const formData = new FormData();
    formData.append('file', request.file);

    const response = await apiService.post<ResponseSingleAttachment, FormData>(
      '/api/attachments/property/video',
      formData
    );
    return response.data;
  },

  uploadPropertyImage: async (request: RequestAttachment): Promise<ResponseAttachment> => {
    const formData = new FormData();
    request.files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiService.post<ResponseAttachment, FormData>(
      '/api/attachments/property/image',
      formData
    );
    return response.data;
  },

  uploadPropertyDocument: async (request: RequestAttachment): Promise<ResponseAttachment> => {
    const formData = new FormData();
    request.files.forEach(file => {
      formData.append('files', file);
    });

    const response = await apiService.post<ResponseAttachment, FormData>(
      '/api/attachments/property/legal-document',
      formData
    );
    return response.data;
  },
};

export default attachmentService;
