import apiService from '../core';

export interface ChatNotification {
  title: string;
  body: string;
}

export interface ChatMessage {
  direction: string;
  conversationId: string;
  content: string;
  senderId: string;
  timestamp: string;
  platform: string;
  messageId?: string;
  createdAt?: string;
}

export interface SendMessageRequest {
  senderId?: string;
  recipientId?: string;
  platformUserId?: string;
  content: string;
  messageType?: string;
  direction?: string;
  platform?: string;
  conversationId?: string;
  replyToMessageId?: string;
  createdAt?: string;
}

export interface ChannelOption {
  value: string;
  displayName: string;
  description: string;
}

export interface ChannelOption {
  value: string;
  displayName: string;
  description: string;
}

export interface Message {
  messageId: string;
  senderId: string | undefined;
  content: string;
  timestamp: Date;
  senderType?: SenderType;
  isRead?: boolean;
  isAnonymous?: boolean;
  isChannelSelection?: boolean;
  options?: ChannelOption[];
}

export interface MessageResponse extends Message {
  direction: string;
  createdAt: Date;
  isNew?: boolean;
  conversationId?: string;
  userId?: string;
}

export interface Conversation {
  id: string;
  lastUpdated: string;
  lastMessage: string;
  platform: string;
  platformUser: {
    id: string;
    avatarUrl: string;
    name: string;
  };
}

export enum SenderType {
  User = 'user',
  Admin = 'admin',
}

class ChatService {
  private getStoredConversation() {
    if (typeof window === 'undefined') return null;
    const stored = localStorage.getItem('chat_conversation');
    return stored ? JSON.parse(stored) : null;
  }

  private storeConversation(userId: string, conversationId: string) {
    if (typeof window === 'undefined') return;
    localStorage.setItem('chat_conversation', JSON.stringify({ userId, conversationId }));
  }

  async getCurrentUserConversation(): Promise<Conversation | null> {
    try {
      const response = await apiService.get('/api/conversations/user');
      return response.data as Conversation;
    } catch (error) {
      console.error('Failed to fetch current user conversation:', error);
      return null;
    }
  }

  async sendMessage(request: SendMessageRequest): Promise<MessageResponse> {
    const authStorage = typeof window !== 'undefined' ? localStorage.getItem('auth-storage') : null;
    const authData = authStorage ? JSON.parse(authStorage) : null;
    const isAuthenticated = !!authData?.state?.token;

    const stored = this.getStoredConversation();

    if (isAuthenticated) {
      request.senderId = stored?.userId;
      delete request.conversationId;
    } else {
      if (stored) {
        request.senderId = stored.userId;
        request.conversationId = stored.conversationId;
      }
    }
    const endpoint = isAuthenticated ? '/api/chat/send/system' : '/api/chat/send/anonymous';

    const response = await apiService.post(endpoint, {
      ...request,
      messageType: request.messageType || 'text',
      direction: request.direction || 'inbound',
      platform: request.platform || 'system',
      isAnonymous: endpoint === '/api/chat/send/anonymous',
    });
    const data = response.data as MessageResponse;
    if (data.isNew && data.conversationId && data.userId) {
      this.storeConversation(data.userId, data.conversationId);
    }

    return data;
  }
}

export const chatService = new ChatService();
