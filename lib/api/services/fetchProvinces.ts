export interface Province {
  readonly code: number;
  readonly name: string;
  readonly districts?: District[];
}

export interface District {
  readonly code: number;
  readonly name: string;
  readonly wards?: Ward[];
}

export interface Ward {
  readonly code: number;
  readonly name: string;
}

export interface ProvinceResponse {
  code: number;
  status: boolean;
  message: string;
  data: Province[];
}

export interface DistrictResponse {
  code: number;
  status: boolean;
  message: string;
  data: {
    districts: District[];
  };
}

export interface WardResponse {
  code: number;
  status: boolean;
  message: string;
  data: {
    wards: Ward[];
  };
}

const PROVINCES_API_BASE = 'https://provinces.open-api.vn/api';

export const provinceService = {
  // Get all provinces
  getProvinces: async (): Promise<Province[]> => {
    try {
      const response = await fetch(`${PROVINCES_API_BASE}/p/`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching provinces:', error);
      throw error;
    }
  },

  // Get districts by province code
  getDistrictsByProvince: async (provinceCode: string): Promise<District[]> => {
    try {
      const response = await fetch(`${PROVINCES_API_BASE}/p/${provinceCode}?depth=2`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.districts || [];
    } catch (error) {
      console.error('Error fetching districts:', error);
      throw error;
    }
  },

  // Get wards by district code
  getWardsByDistrict: async (districtCode: string): Promise<Ward[]> => {
    try {
      const response = await fetch(`${PROVINCES_API_BASE}/d/${districtCode}?depth=2`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.wards || [];
    } catch (error) {
      console.error('Error fetching wards:', error);
      throw error;
    }
  },
};

export default provinceService;
