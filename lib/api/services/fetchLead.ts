import apiService, { RequestParams } from '../core';

export enum LeadScore {
  Hot = 'Hot',
  Warm = 'Warm',
  Cold = 'Cold',
}

export interface AssignedSeller {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar: string | null;
  about: string;
  birthdate: string;
}
export interface LeadSearchParams {
  searchTerm?: string;
  pageNumber?: number;
  pageSize?: number;
  isDescending?: boolean;
  score?: LeadScore;
}

export interface FilterFormValues {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  score?: LeadScore;
}

export interface LeadRequest {
  name: string;
  email: string;
  phone: string;
  address: string;
  score: LeadScore;
}

export interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  source: string;
  score: LeadScore;
  assignedTo: AssignedSeller[];
}

export interface LeadResponse {
  code: number;
  status: boolean;
  message: string;
  data: Lead[];
}

export interface ActionLeadRequest {
  name: string;
  phone: string;
  email: string;
  address: string;
  score: LeadScore;
}

export interface ActionLeadResponse {
  code: number;
  status: boolean;
  message: string;
  data?: string;
}

export interface UpdateScoreRequest {
  leadId: string;
  score: LeadScore;
}

export interface UpdateScoreResponse {
  code: number;
  status: boolean;
  message: string;
  data?: string;
}

export interface AssignLeadRequest {
  leadId: string;
  sellerId: string;
}

export interface AssignLeadResponse {
  code: number;
  status: boolean;
  message: string;
  data?: string;
}

export interface unassignLeadRequest {
  leadId: string;
  sellerId: string;
}

export interface unassignLeadResponse {
  code: number;
  status: boolean;
  message: string;
  data?: string;
}

const convertLeadFilters = (filters?: LeadSearchParams): RequestParams => {
  if (!filters) return {};

  const params: RequestParams = {};

  // Add simple parameters
  if (filters.searchTerm) params.searchTerm = filters.searchTerm;
  if (filters.pageNumber !== undefined) params.pageNumber = filters.pageNumber;
  if (filters.pageSize !== undefined) params.pageSize = filters.pageSize;
  if (filters.isDescending !== undefined) params.isDescending = filters.isDescending;
  if (filters.score) params.score = filters.score;

  return params;
};

export const leadService = {
  getLeads: async (filters?: LeadSearchParams): Promise<LeadResponse> => {
    const params = convertLeadFilters(filters);
    const response = await apiService.get<LeadResponse>('/api/leads', params);
    return response.data;
  },
  getSellerLeads: async (): Promise<LeadResponse> => {
    const response = await apiService.get<LeadResponse>('/api/leads/seller');
    return response.data;
  },
  createLead: async (lead: Partial<ActionLeadRequest>): Promise<ActionLeadResponse> => {
    const response = await apiService.post<ActionLeadResponse, Partial<ActionLeadRequest>>(
      '/api/leads',
      lead
    );
    return response.data;
  },
  assignLead: async (lead: Partial<AssignLeadRequest>): Promise<AssignLeadResponse> => {
    const response = await apiService.patch<AssignLeadResponse, Partial<AssignLeadRequest>>(
      `/api/leads/assign?leadId=${lead.leadId}&sellerId=${lead.sellerId}`
    );
    return response.data;
  },
  unassignLead: async (lead: Partial<unassignLeadRequest>): Promise<AssignLeadResponse> => {
    const response = await apiService.patch<unassignLeadResponse, Partial<unassignLeadRequest>>(
      `/api/leads/unassign?leadId=${lead.leadId}&sellerId=${lead.sellerId}`
    );
    return response.data;
  },
  updateLeadScore: async (lead: UpdateScoreRequest): Promise<UpdateScoreResponse> => {
    const response = await apiService.patch<UpdateScoreResponse>(
      `/api/leads/score?leadId=${lead.leadId}&score=${lead.score}`
    );
    return response.data;
  },
};

export default leadService;
