import { ReactNode } from 'react';
import { ThemeProvider } from '@/lib/providers/themeProvider';
import { QueryProvider } from '@/lib/providers/queryProvider';

interface ProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <QueryProvider>{children}</QueryProvider>
    </ThemeProvider>
  );
}
