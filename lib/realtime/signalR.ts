import {
  HubConnectionBuilder,
  HubConnection,
  LogLevel,
  HttpTransportType,
} from '@microsoft/signalr';
import { ChatMessage, MessageResponse, SendMessageRequest } from '../api/services/fetchChat';

export interface NewConversationHandler {
  (data: { conversationId: string; userId: string }): void;
}
import { getCookie } from 'cookies-next';

export type MessageHandler = (message: ChatMessage) => void;
export type NotificationHandler = (title: string, body: string) => void;
export type TypingHandler = (sessionId: string, isTyping: boolean) => void;
export type ReadHandler = (sessionId: string, messageIds: string[]) => void;
export type OnlineStatusHandler = (sessionId: string, isOnline: boolean) => void;
export type MessageHistoryHandler = (messages: MessageResponse[]) => void;

class SignalRService {
  private connection: HubConnection | null = null;
  private connectionPromise: Promise<void> | null = null;
  private messageHandler: MessageHandler | null = null;
  private notificationHandler: NotificationHandler | null = null;
  private typingHandler: TypingHandler | null = null;
  private readHandler: ReadHandler | null = null;
  private onlineStatusHandler: OnlineStatusHandler | null = null;
  private messageHistoryHandler: MessageHistoryHandler | null = null;
  private newConversationHandler: NewConversationHandler | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor() {
    this.initializeConnection();
  }

  private initializeConnection() {
    this.connection = new HubConnectionBuilder()
      .withUrl(`${process.env.NEXT_PUBLIC_API_URL_BACKEND}/chatHub`, {
        skipNegotiation: true,
        transport: HttpTransportType.WebSockets,
        withCredentials: true,
        accessTokenFactory: () => getCookie('auth-token') || '',
      })
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: retryContext => {
          if (retryContext.previousRetryCount === 0) {
            return 0;
          }
          if (retryContext.previousRetryCount < 3) {
            return 2000;
          }
          return 5000;
        },
      })
      .configureLogging(LogLevel.Information)
      .build();

    this.connection.onreconnecting(error => {
      console.error('Error during reconnection attempt:', error);
    });

    this.connection.onreconnected(connectionId => {
      console.log('Reconnected with ID:', connectionId);
      this.reconnectAttempts = 0;
    });

    this.connection.onclose(error => {
      console.log('Connection closed', error);
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        this.tryReconnect();
      }
    });

    // Handle ReceiveMessage
    this.connection.on('ReceiveMessage', (message: ChatMessage) => {
      // console.log('Received message in SignalR service:', message);
      if (this.messageHandler) {
        // console.log('Calling message handler with:', message);
        this.messageHandler(message);
      }
      //  else {
      //   console.log('No message handler set');
      // }
    });

    this.connection.on('LoadMessageHistory', (messages: MessageResponse[]) => {
      // console.log('Received message history in SignalR service:', messages);
      if (this.messageHistoryHandler) {
        // console.log('Calling message history handler with:', messages);
        this.messageHistoryHandler(messages);
      }
      // else {
      //   console.log('No message history handler set');
      // }
    });

    this.connection.on('LoadMessages', (messages: MessageResponse[]) => {
      // console.log('Received more messages in SignalR service:', messages);
      if (this.messageHistoryHandler) {
        // console.log('Calling message history handler with:', messages);
        this.messageHistoryHandler(messages);
      }
      // else {
      //   console.log('No message history handler set');
      // }
    });

    this.connection.on(
      'NewConversationCreated',
      (data: { conversationId: string; userId: string }) => {
        // console.log('New conversation created:', data);
        if (this.newConversationHandler) {
          this.newConversationHandler(data);
        }
        localStorage.setItem('chat_conversation', JSON.stringify(data));
      }
    );

    this.connection.on('ReceiveNotification', (title: string, body: string) => {
      // console.log('Received notification:', { title, body });
      if (this.notificationHandler) {
        this.notificationHandler(title, body);
      }
    });

    // Handle typing status
    this.connection.on('UserTyping', (sessionId: string, isTyping: boolean) => {
      // console.log('User typing status:', { sessionId, isTyping });
      if (this.typingHandler) {
        this.typingHandler(sessionId, isTyping);
      }
    });

    // Handle read status
    this.connection.on('MessagesRead', (sessionId: string, messageIds: string[]) => {
      // console.log('Messages read:', { sessionId, messageIds });
      if (this.readHandler) {
        this.readHandler(sessionId, messageIds);
      }
    });

    // Handle online status
    this.connection.on('OnlineStatus', (sessionId: string, isOnline: boolean) => {
      // console.log('Online status:', { sessionId, isOnline });
      if (this.onlineStatusHandler) {
        this.onlineStatusHandler(sessionId, isOnline);
      }
    });
  }

  private async tryReconnect() {
    await this.connect();
  }

  public setNewConversationHandler(handler: NewConversationHandler) {
    this.newConversationHandler = handler;
  }

  public setMessageHistoryHandler(handler: MessageHistoryHandler) {
    this.messageHistoryHandler = handler;
  }

  public setMessageHandler(handler: MessageHandler) {
    this.messageHandler = handler;
  }

  public setNotificationHandler(handler: NotificationHandler) {
    this.notificationHandler = handler;
  }

  public setTypingHandler(handler: TypingHandler) {
    this.typingHandler = handler;
  }

  public setReadHandler(handler: ReadHandler) {
    this.readHandler = handler;
  }

  public setOnlineStatusHandler(handler: OnlineStatusHandler) {
    this.onlineStatusHandler = handler;
  }

  public async connect(): Promise<void> {
    if (!this.connection) {
      this.initializeConnection();
    }

    if (this.connection?.state === 'Connected') {
      return Promise.resolve();
    }

    if (!this.connectionPromise) {
      this.connectionPromise = new Promise<void>((resolve, reject) => {
        this.connection!.start()
          .then(() => {
            // console.log('SignalR Connected successfully');
            this.connectionPromise = null;
            this.reconnectAttempts = 0;
            resolve();
          })
          .catch(err => {
            // console.error('SignalR Connection Error:', err);
            this.connectionPromise = null;
            reject(err);
          });
      });
    }

    return this.connectionPromise;
  }

  public async disconnect(): Promise<void> {
    if (this.connection?.state === 'Connected') {
      await this.connection.stop();
      // console.log('SignalR Disconnected');
    }
  }

  public async joinConversation(sessionId: string, userId: string): Promise<void> {
    if (this.connection?.state !== 'Connected') {
      await this.connect();
    }
    if (this.connection?.state === 'Connected') {
      await this.connection!.invoke('JoinConversation', sessionId, userId);
    }
  }

  public async leaveConversation(sessionId: string): Promise<void> {
    if (this.connection?.state === 'Connected') {
      await this.connection.invoke('LeaveConversation', sessionId);
    }
  }

  public async loadMoreMessages(
    conversationId: string,
    pageNumber: number,
    pageSize: number
  ): Promise<void> {
    if (this.connection?.state !== 'Connected') {
      await this.connect();
    }
    await this.connection!.invoke('LoadMoreMessages', conversationId, {
      pageNumber,
      pageSize,
    });
  }

  public async sendMessageToConversation(
    conversationId: string,
    userId: string,
    content: string
  ): Promise<void> {
    if (this.connection?.state !== 'Connected') {
      await this.connect();
    }
    const now = new Date();
    const sendRequest: SendMessageRequest = {
      senderId: userId,
      content: content,
      messageType: 'text',
      direction: 'inbound',
      platform: 'system',
      conversationId: conversationId,
      createdAt: now.toISOString(),
    };
    await this.connection!.invoke('SendMessageToConversation', conversationId, userId, sendRequest);
  }
}

export const signalRService = new SignalRService();
