import { create } from 'zustand';
import { getCookie, setCookie, deleteCookie } from 'cookies-next';
import apiService from '@/lib/api/core';
import { User } from '../api/services/fetchUser';

interface AuthState {
  token: string | null;
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;

  // Actions
  setToken: (token: string | null) => void;
  setUser: (user: User | null) => void;
  login: (token: string, user: User) => void;
  logout: () => void;
  syncAuthState: () => void;
}

export const useAuthStore = create<AuthState>((set, _get) => ({
  token: null,
  user: null,
  isAuthenticated: false,
  isLoading: false,

  setToken: token => {
    if (token) {
      setCookie('auth-token', token, {
        maxAge: 60 * 60 * 24 * 7,
        path: '/',
      });
      apiService.setAuthToken(token);
    } else {
      deleteCookie('auth-token', { path: '/' });
      apiService.setAuthToken(null);
    }

    set({ token, isAuthenticated: !!token });
  },

  setUser: user => set({ user }),

  login: (token, user) => {
    setCookie('auth-token', token, {
      maxAge: 60 * 60 * 24 * 7,
      path: '/',
    });
    apiService.setAuthToken(token);

    set({
      token,
      user,
      isAuthenticated: true,
    });
  },

  logout: () => {
    deleteCookie('auth-token', { path: '/' });
    apiService.setAuthToken(null);

    set({
      token: null,
      user: null,
      isAuthenticated: false,
    });
  },

  syncAuthState: () => {
    if (typeof window !== 'undefined') {
      const cookieToken = getCookie('auth-token');
      set(state => {
        const storeHasToken = !!state.token;
        const cookieHasToken = !!cookieToken;

        if (storeHasToken !== cookieHasToken) {
          if (cookieHasToken) {
            return {
              token: cookieToken as string,
              isAuthenticated: true,
            };
          } else {
            return {
              token: null,
              isAuthenticated: false,
            };
          }
        }

        return {
          isAuthenticated: storeHasToken,
        };
      });
    }
  },
}));

// Initialize auth state from storage
setTimeout(() => {
  const state = useAuthStore.getState();
  if (typeof window !== 'undefined') {
    const cookieToken = getCookie('auth-token');
    const storeHasToken = !!state.token;
    const cookieHasToken = !!cookieToken;

    if (storeHasToken) {
      apiService.setAuthToken(state.token);
    } else if (cookieHasToken) {
      apiService.setAuthToken(cookieToken as string);
    }

    if (storeHasToken !== cookieHasToken || (storeHasToken && !state.isAuthenticated)) {
      state.syncAuthState();
    }

    // Listen for logout events from API service
    const handleLogout = () => {
      state.logout();
    };

    window.addEventListener('logout', handleLogout);

    // Cleanup listener on page unload
    window.addEventListener('beforeunload', () => {
      window.removeEventListener('logout', handleLogout);
    });
  }
}, 0);
